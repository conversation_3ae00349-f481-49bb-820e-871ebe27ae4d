# AD Description Checker Script
# Simple script to check the AD description/comments of a computer
# Developed by: The greatest technician that ever lived

function Write-Color {
    param([string]$Message, [string]$Color = "Gray", [switch]$NoNewline)
    if ($NoNewline) { Write-Host $Message -ForegroundColor $Color -NoNewline }
    else { Write-Host $Message -ForegroundColor $Color }
}

function Show-Banner {
    Write-Host ""
    Write-Host "  =================================================================" -ForegroundColor "Cyan"
    Write-Host "                      AD DESCRIPTION CHECKER                       " -ForegroundColor "Yellow"
    Write-Host "  =================================================================" -ForegroundColor "Cyan"
    Write-Host ""
    Write-Host "    Check Active Directory Description/Comments for Computers      " -ForegroundColor "Green"
    Write-Host ""
    Write-Host "    Developed by: The greatest technician that ever lived         " -ForegroundColor "Magenta"
    Write-Host ""
    Write-Host "  =================================================================" -ForegroundColor "Cyan"
    Write-Host ""
}

function Check-ADDescription {
    param([string]$ComputerName)
    
    Write-Color "Checking AD description for: $ComputerName" "Cyan"
    
    try {
        # Import Active Directory module
        Import-Module ActiveDirectory -ErrorAction Stop
        Write-Color "Active Directory module loaded successfully" "Green"
    }
    catch {
        Write-Color "Error: Could not load Active Directory module - $_" "Red"
        Write-Color "Make sure you're running this on a domain controller or computer with RSAT installed" "Yellow"
        return
    }
    
    # Ensure computer name has $ suffix for AD lookup
    $computerAD = if ($ComputerName[-1] -ne '$') { "$ComputerName$" } else { $ComputerName }
    
    try {
        Write-Color "`nSearching for computer: $computerAD" "Yellow"
        
        # Get computer with all possible description-related properties
        $computer = Get-ADComputer -Identity $computerAD -Properties Description, Info, Comment, DisplayName, CN, Name -ErrorAction Stop
        
        Write-Color "Computer found successfully!" "Green"
        Write-Color "`n=== COMPUTER INFORMATION ===" "Cyan"
        Write-Color "Computer Name: $($computer.Name)" "Gray"
        Write-Color "Distinguished Name: $($computer.DistinguishedName)" "Gray"
        Write-Color "Display Name: $($computer.DisplayName)" "Gray"
        Write-Color "CN: $($computer.CN)" "Gray"
        
        Write-Color "`n=== DESCRIPTION FIELDS ===" "Cyan"
        
        # Check Description field
        if ($computer.Description) {
            Write-Color "Description Field: '$($computer.Description)'" "Green"
            Write-Color "  Length: $($computer.Description.Length) characters" "Gray"
            Write-Color "  Trimmed: '$($computer.Description.Trim())'" "Gray"
        } else {
            Write-Color "Description Field: (empty/null)" "Yellow"
        }
        
        # Check Info field
        if ($computer.Info) {
            Write-Color "Info Field: '$($computer.Info)'" "Green"
            Write-Color "  Length: $($computer.Info.Length) characters" "Gray"
        } else {
            Write-Color "Info Field: (empty/null)" "Yellow"
        }
        
        # Check Comment field
        if ($computer.Comment) {
            Write-Color "Comment Field: '$($computer.Comment)'" "Green"
            Write-Color "  Length: $($computer.Comment.Length) characters" "Gray"
        } else {
            Write-Color "Comment Field: (empty/null)" "Yellow"
        }
        
        # Summary
        Write-Color "`n=== SUMMARY ===" "Cyan"
        $hasDescription = $computer.Description -and $computer.Description.Trim() -ne ""
        $hasInfo = $computer.Info -and $computer.Info.Trim() -ne ""
        $hasComment = $computer.Comment -and $computer.Comment.Trim() -ne ""
        
        if ($hasDescription) {
            Write-Color "Primary Description: '$($computer.Description.Trim())'" "Green"
        } elseif ($hasInfo) {
            Write-Color "Primary Description (from Info field): '$($computer.Info.Trim())'" "Green"
        } elseif ($hasComment) {
            Write-Color "Primary Description (from Comment field): '$($computer.Comment.Trim())'" "Green"
        } else {
            Write-Color "No description found in any field" "Yellow"
        }
        
        # Show what the migration script would use
        Write-Color "`n=== MIGRATION SCRIPT BEHAVIOR ===" "Cyan"
        if ($hasDescription) {
            Write-Color "Migration script would find: '$($computer.Description.Trim())'" "Green"
            Write-Color "Result: Would copy description with replacement prefix" "Green"
        } elseif ($hasInfo) {
            Write-Color "Migration script would find: '$($computer.Info.Trim())'" "Green"
            Write-Color "Result: Would copy description with replacement prefix" "Green"
        } elseif ($hasComment) {
            Write-Color "Migration script would find: '$($computer.Comment.Trim())'" "Green"
            Write-Color "Result: Would copy description with replacement prefix" "Green"
        } else {
            Write-Color "Migration script would find: (nothing)" "Yellow"
            Write-Color "Result: Would show 'No source comments - added replacement info only'" "Yellow"
        }
        
    }
    catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
        Write-Color "Error: Computer '$computerAD' not found in Active Directory" "Red"
        Write-Color "Make sure the computer name is correct and the computer exists in AD" "Yellow"
    }
    catch {
        Write-Color "Error: Failed to retrieve computer information - $_" "Red"
        Write-Color "This might be a permissions issue or the computer doesn't exist" "Yellow"
    }
}

function Get-MultipleComputers {
    Write-Color "Enter computer names (one per line, press Enter twice when done):" "Cyan"
    $computers = @()
    
    do {
        $input = Read-Host "Computer name"
        if ($input.Trim() -ne "") {
            $computers += $input.Trim()
        }
    } while ($input.Trim() -ne "")
    
    if ($computers.Count -eq 0) {
        Write-Color "No computers entered" "Yellow"
        return
    }
    
    foreach ($computer in $computers) {
        Write-Color "`n" + ("="*80) "DarkGray"
        Check-ADDescription -ComputerName $computer
    }
}

# Main script execution
Clear-Host
Show-Banner

do {
    Write-Color "Options:" "Cyan"
    Write-Color "1. Check single computer" "White"
    Write-Color "2. Check multiple computers" "White"
    Write-Color "3. Exit" "White"
    Write-Host ""
    
    $choice = Read-Host "Enter your choice (1-3)"
    
    switch ($choice) {
        "1" {
            $computerName = Read-Host "`nEnter computer name"
            if ($computerName.Trim() -ne "") {
                Write-Host ""
                Check-ADDescription -ComputerName $computerName.Trim()
            } else {
                Write-Color "No computer name entered" "Yellow"
            }
        }
        "2" {
            Write-Host ""
            Get-MultipleComputers
        }
        "3" {
            Write-Color "Exiting..." "Green"
            break
        }
        default {
            Write-Color "Invalid choice. Please enter 1, 2, or 3." "Red"
        }
    }
    
    if ($choice -ne "3") {
        Write-Host ""
        Read-Host "Press Enter to continue"
        Clear-Host
        Show-Banner
    }
    
} while ($choice -ne "3")
