# Transition to final phase
Show-Banner "*** PRINTER MIGRATION ***" "Green"
Show-Progress "Configuring printer settings" 6 7 "Yellow"




# Define paths
$sourceFolderPath = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\LexmarkGDI"
$destinationFolderPath = "\\$copyToDeviceId\C$\Files\Drivers\Print\GDI"

# Copy driver files
Write-Host "Copying driver files to $copyToDeviceId..." -ForegroundColor Yellow
robocopy $sourceFolderPath $destinationFolderPath /E /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /IS /IT
Write-Host "Driver files copied." -ForegroundColor Green

# Install the print driver
Invoke-Command -ComputerName $copyToDeviceId -ScriptBlock {
$driverPath = "C:\Files\Drivers\Print\GDI\LMUD1n40.inf"
Pnputil /add-driver $driverPath /install
Add-PrinterDriver -Name "Lexmark Universal v2 PostScript 3 Emulation"
Remove-Item -Path "C:\Files\Drivers" -Recurse -Force
Write-Host "Driver installed and files cleaned up." -ForegroundColor Green
}



# --- PRINTER MIGRATION SECTION ---
$printerBackupChoice = Read-Host "Do you want to perform printer backup and restore? (Y/N)  "
if ($printerBackupChoice -match '^(Y|y)') {
    # Validate essential variables
    if ([string]::IsNullOrEmpty($copyFromDeviceId) -or [string]::IsNullOrEmpty($copyToDeviceId)) {
        Write-Color "ERROR: Missing source or target computer identifiers" "Red"
        exit 1
    }

    # Standardize and sanitize input
    $sourcePC = $copyFromDeviceId.Trim()
    $targetPC = $copyToDeviceId.Trim()

    try {
        # Verify network connectivity
        Write-Color "`n[1/5] Verifying network connectivity..." "Cyan"
        Test-Connection $sourcePC -Count 1 -ErrorAction Stop | Out-Null
        Test-Connection $targetPC -Count 1 -ErrorAction Stop | Out-Null

        $DriverTempPath = "\\$targetPC\c`$\Temp\Drivers\"
        $exportPath = "\\$sourcePC\c`$\Temp\Drivers\"
        $ExcludeList = "XPS|Fax|PDF|OneNote|MSFT|CutePDF|Send to Microsoft OneNote|Microsoft Print To PDF|Microsoft XPS Document Writer|Snagit"

        # Create temporary directories
        Write-Color "`n[2/5] Preparing temporary directories..." "Cyan"
        Invoke-Command -ComputerName $sourcePC -ScriptBlock {
            New-Item -Path $using:exportPath -ItemType Directory -Force -ErrorAction Stop | Out-Null
        }
        Invoke-Command -ComputerName $targetPC -ScriptBlock {
            New-Item -Path $using:DriverTempPath -ItemType Directory -Force -ErrorAction Stop | Out-Null
        }

        # Get source printers
        Write-Color "`n[3/5] Retrieving source printers..." "Cyan"
        $printers = Invoke-Command -ComputerName $sourcePC -ScriptBlock {
            Get-Printer | Where-Object {
                $_.Name -notmatch $using:ExcludeList -and
                $_.Type -notmatch 'Text|PrintServer'
            } | Select-Object Name, DriverName, PortName, Shared, Published, Location, @{
                Name = 'DriverInfPath'
                Expression = {
                    $driver = Get-PrinterDriver -Name $_.DriverName -ErrorAction SilentlyContinue
                    if ($driver) { $driver.InfPath } else { $null }
                }
            }
        } -ErrorAction Stop

        Write-Color "  Found $($printers.Count) printers on source computer" "Gray"
        $printers | ForEach-Object {
            Write-Color "  [Source] $($_.Name) | Driver: $($_.DriverName)" "DarkGray"
        }

        # Get target drivers with normalized names
        $targetDrivers = Invoke-Command -ComputerName $targetPC -ScriptBlock {
            Get-PrinterDriver | Select-Object Name, @{
                Name = 'Normalized'
                Expression = {
                    ($_.Name -replace 'PS3','PostScript3' -replace '[^a-zA-Z0-9]','').ToLower()
                }
            }
        } -ErrorAction Stop

        # Process printers
        Write-Color "`n[4/5] Migrating printers..." "Cyan"
        $successCount = 0
        $failureCount = 0

        foreach ($printer in $printers) {
            $printerName = $printer.Name
            $wantedDriverName = $printer.DriverName
            $portName = $printer.PortName
            # Driver inf path available if needed for troubleshooting

            try {
                Write-Color "  Processing: $printerName" "Gray"

                # Enhanced normalization with PS3 -> PostScript substitution
                $normalizedSourceDriver = ($wantedDriverName -replace 'PS3','PostScript3' -replace '[^a-zA-Z0-9]','').ToLower()

                # Find best match using multiple criteria
                $matchedDriver = $targetDrivers | Where-Object {
                    ($_.Normalized -eq $normalizedSourceDriver) -or
                    ($_.Normalized -like "*$normalizedSourceDriver*" -and $_.Normalized -like "*lexmark*") -or
                    ($_.Normalized -replace 'postscript','ps' -eq $normalizedSourceDriver)
                } | Select-Object -First 1

                if ($matchedDriver) {
                    $finalDriverName = $matchedDriver.Name
                    Write-Color "    Auto-matched driver: $finalDriverName" "Yellow"
                }
                else {
                    # Fallback to interactive selection
                    Write-Color "    Driver '$wantedDriverName' not found on $targetPC." "Yellow"
                    $availableDrivers = $targetDrivers.Name

                    if ($availableDrivers.Count -gt 0) {
                        Write-Host "Available printer drivers:"
                        $i = 0
                        $availableDrivers | ForEach-Object {
                            Write-Host "[$i] $_"
                            $i++
                        }
                        $selectedIndex = Read-Host "Enter driver index or press Enter to type name"

                        if ($selectedIndex -match '^\d+$' -and [int]$selectedIndex -lt $availableDrivers.Count) {
                            $finalDriverName = $availableDrivers[[int]$selectedIndex]
                        } else {
                            $finalDriverName = Read-Host "Enter exact driver name"
                        }
                    } else {
                        $finalDriverName = Read-Host "No drivers found. Enter exact driver name"
                    }
                }

                # Create printer port and printer
                Invoke-Command -ComputerName $targetPC -ScriptBlock {
                    param($printerName, $finalDriverName, $portName, $printer)

                    # Create port if missing
                    if (-not (Get-PrinterPort -Name $portName -ErrorAction SilentlyContinue)) {
                        Add-PrinterPort -Name $portName -ErrorAction Stop | Out-Null
                    }

                    # Create printer if missing
                    if (-not (Get-Printer -Name $printerName -ErrorAction SilentlyContinue)) {
                        Add-Printer -Name $printerName -DriverName $finalDriverName -PortName $portName -ErrorAction Stop | Out-Null
                    }

                    # Set printer properties
                    Set-Printer -Name $printerName -Shared:$printer.Shared -ErrorAction SilentlyContinue | Out-Null
                } -ArgumentList $printerName, $finalDriverName, $portName, $printer -ErrorAction Stop

                Write-Color "    Success: $printerName" "Green"
                $successCount++
            }
            catch {
                Write-Color "    [!] Failed: $printerName - $($_.Exception.Message)" "Red"
                $failureCount++
            }
        }

        # Cleanup
        Write-Color "`n[5/5] Removing temporary files..." "Cyan"
        Invoke-Command -ComputerName $sourcePC -ScriptBlock {
            Remove-Item -Path $using:exportPath -Recurse -Force -ErrorAction SilentlyContinue | Out-Null
        }
        Invoke-Command -ComputerName $targetPC -ScriptBlock {
            Remove-Item -Path $using:DriverTempPath -Recurse -Force -ErrorAction SilentlyContinue | Out-Null
        }

        # Results summary
        Write-Color "`n[Migration Complete]" "Cyan"
        Write-Color "  Attempted printers: $($printers.Count)" "Gray"
        Write-Color "  Successful: $successCount" "Green"
        Write-Color "  Failed: $failureCount" "Red"
    }
    catch {
        Write-Color "`n[!] Critical printer migration error: $($_.Exception.Message)" "Red"
    }
}
