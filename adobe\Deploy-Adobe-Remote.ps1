# Adobe Acrobat DC Remote Deployment Script
# Developed by: The greatest technician that ever lived

param(
    [Parameter(Mandatory=$false)]
    [string[]]$ComputerNames,

    [Parameter(Mandatory=$false)]
    [PSCredential]$Credential,

    [Parameter(Mandatory=$false)]
    [switch]$UseLocalInstaller,

    [Parameter(Mandatory=$false)]
    [string]$LocalInstallerPath = "D:\Adobe\adobe.bat",

    [Parameter(Mandatory=$false)]
    [string]$ComputerListFile
)

# Function to get computer names interactively
function Get-ComputerNames {
    Write-Host "`n========================================================================" -ForegroundColor Yellow
    Write-Host "                    Computer Selection Options" -ForegroundColor Yellow
    Write-Host "========================================================================" -ForegroundColor Yellow
    Write-Host "1. Single computer"
    Write-Host "2. Multiple computers (enter one by one)"
    Write-Host "3. Load from text file"
    Write-Host ""

    do {
        $choice = Read-Host "Select option (1, 2, or 3)"
    } while ($choice -notmatch '^[123]$')

    switch ($choice) {
        "1" {
            # Single computer
            do {
                $computerName = Read-Host "`nEnter computer name"
            } while ([string]::IsNullOrWhiteSpace($computerName))

            return @($computerName.Trim())
        }

        "2" {
            # Multiple computers - enter one by one
            $computers = @()
            Write-Host "`nEnter computer names (press Enter with empty name to finish):" -ForegroundColor Yellow

            do {
                $computerName = Read-Host "Computer name"
                if (-not [string]::IsNullOrWhiteSpace($computerName)) {
                    $computers += $computerName.Trim()
                    Write-Host "Added: $($computerName.Trim())" -ForegroundColor Green
                }
            } while (-not [string]::IsNullOrWhiteSpace($computerName))

            if ($computers.Count -eq 0) {
                Write-Error "No computer names entered"
                return $null
            }

            Write-Host "`nTotal computers added: $($computers.Count)" -ForegroundColor Cyan
            return $computers
        }

        "3" {
            # Load from text file
            do {
                $filePath = Read-Host "`nEnter path to text file containing computer names"
            } while ([string]::IsNullOrWhiteSpace($filePath))

            if (-not (Test-Path $filePath)) {
                Write-Error "File not found: $filePath"
                return $null
            }

            try {
                $computers = Get-Content $filePath | Where-Object { -not [string]::IsNullOrWhiteSpace($_) } | ForEach-Object { $_.Trim() }

                if ($computers.Count -eq 0) {
                    Write-Error "No valid computer names found in file"
                    return $null
                }

                Write-Host "`nLoaded $($computers.Count) computer names from file:" -ForegroundColor Green
                $computers | ForEach-Object { Write-Host "  - $_" -ForegroundColor Gray }

                return $computers
            }
            catch {
                Write-Error "Failed to read file: $_"
                return $null
            }
        }
    }
}

# Function to test computer connectivity and DNS resolution
function Test-ComputerConnectivity {
    param([string]$ComputerName)

    Write-Host "  Testing connectivity to $ComputerName..." -ForegroundColor Yellow

    # Test basic ping connectivity
    if (-not (Test-Connection -ComputerName $ComputerName -Count 2 -Quiet)) {
        Write-Warning "$ComputerName is offline or unreachable via ping"
        return $false
    }

    # Test DNS resolution and verify it resolves to expected name
    try {
        $dnsResult = Resolve-DnsName -Name $ComputerName -ErrorAction Stop
        $resolvedName = $dnsResult | Where-Object { $_.Type -eq 'A' } | Select-Object -First 1

        if (-not $resolvedName) {
            Write-Warning "$ComputerName does not resolve to an IP address"
            return $false
        }

        $ipAddress = $resolvedName.IPAddress
        Write-Host "  $ComputerName resolves to IP: $ipAddress" -ForegroundColor Green

        # Reverse DNS lookup to verify the IP resolves back to the correct name
        try {
            $reverseResult = Resolve-DnsName -Name $ipAddress -ErrorAction Stop
            $reverseName = $reverseResult | Where-Object { $_.Type -eq 'PTR' } | Select-Object -First 1

            if ($reverseName) {
                $resolvedBackName = $reverseName.NameHost
                Write-Host "  IP $ipAddress resolves back to: $resolvedBackName" -ForegroundColor Green

                # Check if the reverse lookup matches (allowing for FQDN differences)
                $shortReverseName = $resolvedBackName.Split('.')[0]
                $shortComputerName = $ComputerName.Split('.')[0]

                if ($shortReverseName -eq $shortComputerName -or $resolvedBackName -eq $ComputerName) {
                    Write-Host "  DNS verification successful" -ForegroundColor Green
                    return $true
                } else {
                    Write-Host "  ⚠️  CRITICAL DNS MISMATCH DETECTED!" -ForegroundColor Red
                    Write-Host "  Target: $ComputerName" -ForegroundColor Yellow
                    Write-Host "  Would actually connect to: $resolvedBackName" -ForegroundColor Red
                    Write-Host "  This could install Adobe on the WRONG COMPUTER!" -ForegroundColor Red
                    Write-Host "  SKIPPING this computer for safety." -ForegroundColor Red
                    return $false
                }
            } else {
                Write-Host "  ⚠️  WARNING: Reverse DNS lookup failed for $ipAddress" -ForegroundColor Red
                Write-Host "  Cannot verify this is the correct computer!" -ForegroundColor Red
                Write-Host "  SKIPPING this computer for safety." -ForegroundColor Red
                return $false
            }
        }
        catch {
            Write-Host "  ⚠️  WARNING: Reverse DNS lookup failed for $ipAddress`: $_" -ForegroundColor Red
            Write-Host "  Cannot verify this is the correct computer!" -ForegroundColor Red
            Write-Host "  SKIPPING this computer for safety." -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Warning "DNS resolution failed for $ComputerName`: $_"
        return $false
    }
}

# Function to execute remote commands with job handling
function Invoke-RemoteCommand {
    param(
        [string]$ComputerName,
        [string]$CommandLine
    )

    $remoteOutput = Invoke-Command -ComputerName $ComputerName -ScriptBlock ([scriptblock]::Create($CommandLine)) -AsJob
    while ($remoteOutput.HasMoreData -eq $true) {
        if ($remoteOutput.hasmoredata) {
           Receive-Job -Job $remoteOutput
        }
        Start-Sleep -Milliseconds 200
    }
}

# Function to check PSRemoting status using actual remote command test
function Check-PSRemoting {
    param([string]$ComputerName)

    try {
        # Test with a simple remote command - this is the most reliable method
        $testJob = Invoke-Command -ComputerName $ComputerName -ScriptBlock { $env:COMPUTERNAME } -AsJob -ErrorAction Stop

        # Wait for job to complete with timeout
        $timeout = 10 # seconds
        $completed = Wait-Job -Job $testJob -Timeout $timeout

        if ($completed) {
            $result = Receive-Job -Job $testJob -ErrorAction SilentlyContinue
            Remove-Job -Job $testJob -Force -ErrorAction SilentlyContinue

            if ($result) {
                return $true
            }
        } else {
            # Job timed out
            Remove-Job -Job $testJob -Force -ErrorAction SilentlyContinue
            return $false
        }

        return $false
    }
    catch {
        return $false
    }
}

# Function to enable PSRemoting via CIM/DCOM
function Enable-PSRemoting {
    param([string]$ComputerName)

    try {
        Write-Host "  Enabling PSRemoting on $ComputerName via CIM/DCOM..." -ForegroundColor Yellow

        $SessionArgs = @{
            ComputerName = $ComputerName
            SessionOption = New-CimSessionOption -Protocol Dcom
        }

        $cimSession = New-CimSession @SessionArgs -ErrorAction Stop

        # Enable PSRemoting
        $MethodArgsEnablePS = @{
            ClassName = 'Win32_Process'
            MethodName = 'Create'
            CimSession = $cimSession
            Arguments = @{ CommandLine = 'powershell -Command "Enable-PSRemoting -Force -SkipNetworkProfileCheck"' }
        }
        Invoke-CimMethod @MethodArgsEnablePS -ErrorAction Stop

        # Configure WinRM
        $MethodArgsRunWinRM = @{
            ClassName = 'Win32_Process'
            MethodName = 'Create'
            CimSession = $cimSession
            Arguments = @{ CommandLine = 'winrm qc -quiet' }
        }
        Invoke-CimMethod @MethodArgsRunWinRM -ErrorAction Stop

        Remove-CimSession -CimSession $cimSession

        # Wait for services to start and retry verification
        Write-Host "  Waiting for WinRM service to start..." -ForegroundColor Yellow
        Start-Sleep -Seconds 20

        # Try multiple verification attempts
        $maxAttempts = 3
        $attempt = 1

        while ($attempt -le $maxAttempts) {
            Write-Host "  Verification attempt $attempt of $maxAttempts..." -ForegroundColor Yellow

            if (Check-PSRemoting -ComputerName $ComputerName) {
                Write-Host "  Successfully enabled PSRemoting on $ComputerName" -ForegroundColor Green
                return $true
            }

            if ($attempt -lt $maxAttempts) {
                Write-Host "  Waiting 10 seconds before retry..." -ForegroundColor Yellow
                Start-Sleep -Seconds 10
            }

            $attempt++
        }

        Write-Warning "  PSRemoting enablement verification failed on $ComputerName after $maxAttempts attempts"
        return $false
    }
    catch {
        Write-Error "  Failed to enable PSRemoting on $ComputerName`: $_"
        return $false
    }
}

# Function to execute remote commands with PSRemoting check
function Execute-RemoteCommandWithCheck {
    param(
        [string]$ComputerName,
        [string]$CommandLine
    )

    if (-not (Check-PSRemoting -ComputerName $ComputerName)) {
        try {
            if (-not (Enable-PSRemoting -ComputerName $ComputerName)) {
                Write-Error "Failed to enable PSRemoting on $ComputerName"
                return $false
            }
        }
        catch {
            Write-Error "Failed to enable PSRemoting on $ComputerName`: $_"
            return $false
        }
    }

    try {
        Invoke-RemoteCommand -ComputerName $ComputerName -CommandLine $CommandLine
        return $true
    }
    catch {
        Write-Error "Failed to execute remote command on $ComputerName`: $_"
        return $false
    }
}

# Function to execute remote installation
function Install-AdobeRemote {
    param(
        [string]$ComputerName,
        [PSCredential]$Cred,
        [bool]$UseLocal,
        [string]$LocalPath
    )
    
    Write-Host "`n========================================================================" -ForegroundColor Cyan
    Write-Host "Installing Adobe Acrobat DC on: $ComputerName" -ForegroundColor Cyan
    Write-Host "========================================================================" -ForegroundColor Cyan
    
    try {
        # Test connectivity and DNS resolution
        if (-not (Test-ComputerConnectivity -ComputerName $ComputerName)) {
            Write-Error "$ComputerName failed connectivity or DNS verification"
            return $false
        }
        
        # Check if Adobe Acrobat DC is already installed
        Write-Host "  Checking if Adobe Acrobat DC is already installed..." -ForegroundColor Yellow

        $checkInstallation = Invoke-Command -ComputerName $ComputerName -ScriptBlock {
            # Check multiple possible installation paths for Adobe Acrobat Pro DC
            $paths = @(
                "C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe",
                "C:\Program Files (x86)\Adobe\Acrobat DC\Acrobat\Acrobat.exe",
                "C:\Program Files\Adobe\Acrobat 2020\Acrobat\Acrobat.exe",
                "C:\Program Files (x86)\Adobe\Acrobat 2020\Acrobat\Acrobat.exe"
            )

            foreach ($path in $paths) {
                if (Test-Path $path) {
                    try {
                        $fileInfo = Get-ItemProperty $path
                        $version = $fileInfo.VersionInfo.FileVersion
                        $productName = $fileInfo.VersionInfo.ProductName

                        return @{
                            Installed = $true
                            Path = $path
                            Version = $version
                            ProductName = $productName
                        }
                    }
                    catch {
                        return @{
                            Installed = $true
                            Path = $path
                            Version = "Unknown"
                            ProductName = "Adobe Acrobat"
                        }
                    }
                }
            }

            # Check via registry for Adobe Acrobat Pro DC specifically
            try {
                $regPaths = @(
                    "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*",
                    "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*"
                )

                foreach ($regPath in $regPaths) {
                    $adobeApps = Get-ItemProperty $regPath -ErrorAction SilentlyContinue | Where-Object {
                        $_.DisplayName -like "*Adobe Acrobat*" -and
                        ($_.DisplayName -like "*Pro*" -or $_.DisplayName -like "*DC*")
                    }

                    if ($adobeApps) {
                        $app = $adobeApps | Select-Object -First 1
                        return @{
                            Installed = $true
                            Path = "Registry"
                            Version = $app.DisplayVersion
                            Name = $app.DisplayName
                            Publisher = $app.Publisher
                        }
                    }
                }
            }
            catch {
                # Registry check failed, continue
            }

            return @{
                Installed = $false
                Path = $null
                Version = $null
            }
        } -ErrorAction SilentlyContinue

        if ($checkInstallation -and $checkInstallation.Installed) {
            Write-Host "  ✓ Adobe Acrobat Pro DC is already installed" -ForegroundColor Green
            Write-Host "    Path: $($checkInstallation.Path)" -ForegroundColor Gray
            Write-Host "    Version: $($checkInstallation.Version)" -ForegroundColor Gray

            if ($checkInstallation.ProductName) {
                Write-Host "    Product: $($checkInstallation.ProductName)" -ForegroundColor Gray
            }
            if ($checkInstallation.Name) {
                Write-Host "    Name: $($checkInstallation.Name)" -ForegroundColor Gray
            }
            if ($checkInstallation.Publisher) {
                Write-Host "    Publisher: $($checkInstallation.Publisher)" -ForegroundColor Gray
            }

            Write-Host "  Skipping installation - Adobe Acrobat Pro DC already present" -ForegroundColor Yellow
            return $true
        } else {
            Write-Host "  Adobe Acrobat Pro DC not found - proceeding with installation" -ForegroundColor Green
        }

        # Check/Enable PSRemoting using your proven method
        Write-Host "  Checking PSRemoting status..." -ForegroundColor Yellow

        # Use the Execute-RemoteCommandWithCheck approach that you know works
        try {
            # Test with a simple command first
            $testResult = Execute-RemoteCommandWithCheck -ComputerName $ComputerName -CommandLine "echo 'PSRemoting Test'"

            if ($testResult) {
                Write-Host "  PSRemoting is working" -ForegroundColor Green
            } else {
                Write-Error "PSRemoting test failed on $ComputerName - cannot proceed with installation"
                return $false
            }
        }
        catch {
            Write-Error "PSRemoting connection failed on $ComputerName`: $_"
            return $false
        }
        
        # Create remote session
        $sessionParams = @{
            ComputerName = $ComputerName
            ErrorAction = 'Stop'
        }
        if ($Cred) { $sessionParams.Credential = $Cred }
        
        $session = New-PSSession @sessionParams
        
        if ($UseLocal) {
            # Copy local installer to remote computer
            Write-Host "Copying local installer to $ComputerName..." -ForegroundColor Yellow
            
            if (-not (Test-Path $LocalPath)) {
                Write-Error "Local installer not found at: $LocalPath"
                Remove-PSSession $session
                return $false
            }
            
            Copy-Item -Path $LocalPath -Destination "C:\Temp\adobe.bat" -ToSession $session -Force
            
            # Execute the copied installer
            $result = Invoke-Command -Session $session -ScriptBlock {
                if (-not (Test-Path "C:\Temp")) { New-Item -Path "C:\Temp" -ItemType Directory -Force }
                
                Write-Host "[$(Get-Date)] Starting Adobe installation from local file..."
                $process = Start-Process -FilePath "C:\Temp\adobe.bat" -Wait -PassThru -WindowStyle Hidden
                
                # Clean up
                Remove-Item "C:\Temp\adobe.bat" -Force -ErrorAction SilentlyContinue
                
                return $process.ExitCode
            }
        }
        else {
            # Use remote download and installation
            Write-Host "Starting remote download and installation on $ComputerName..." -ForegroundColor Yellow
            
            $result = Invoke-Command -Session $session -ScriptBlock {
                # Create temp directory
                if (-not (Test-Path "$env:TEMP\AdobeInstaller")) {
                    New-Item -Path "$env:TEMP\AdobeInstaller" -ItemType Directory -Force
                }
                
                Write-Host "[$(Get-Date)] Downloading Adobe Acrobat DC..."
                
                try {
                    # Download Adobe installer with progress
                    $url = "https://trials.adobe.com/AdobeProducts/APRO/Acrobat_HelpX/win32/Acrobat_DC_Web_x64_WWMUI.zip"
                    $output = "$env:TEMP\AdobeInstaller\Acrobat_DC_Web_x64_WWMUI.zip"

                    # Function to format bytes to human readable
                    function Format-FileSize {
                        param([long]$Size)
                        if ($Size -gt 1GB) {
                            return "{0:N2} GB" -f ($Size / 1GB)
                        } elseif ($Size -gt 1MB) {
                            return "{0:N2} MB" -f ($Size / 1MB)
                        } elseif ($Size -gt 1KB) {
                            return "{0:N2} KB" -f ($Size / 1KB)
                        } else {
                            return "$Size bytes"
                        }
                    }

                    # Download with progress tracking
                    $webClient = New-Object System.Net.WebClient
                    $webClient.Headers.Add("User-Agent", "PowerShell")

                    # Register progress event
                    Register-ObjectEvent -InputObject $webClient -EventName DownloadProgressChanged -Action {
                        $receivedSize = Format-FileSize $Event.SourceEventArgs.BytesReceived
                        $totalSize = Format-FileSize $Event.SourceEventArgs.TotalBytesToReceive
                        $percentage = $Event.SourceEventArgs.ProgressPercentage
                        Write-Progress -Activity "Downloading Adobe Acrobat DC" -Status "$receivedSize of $totalSize" -PercentComplete $percentage
                    } | Out-Null

                    try {
                        $webClient.DownloadFile($url, $output)
                        Write-Progress -Activity "Downloading Adobe Acrobat DC" -Completed
                    } finally {
                        $webClient.Dispose()
                        Get-EventSubscriber | Unregister-Event
                    }

                    if (-not (Test-Path $output)) {
                        throw "Download failed"
                    }

                    $fileSize = Format-FileSize (Get-Item $output).Length
                    Write-Host "[$(Get-Date)] Download completed successfully ($fileSize)"
                    Write-Host "[$(Get-Date)] Extracting installer..."
                    
                    # Extract the installer
                    Expand-Archive -Path $output -DestinationPath "$env:TEMP\AdobeInstaller" -Force
                    Remove-Item $output -Force
                    
                    Write-Host "[$(Get-Date)] Starting silent installation..."
                    
                    # Run silent installation
                    $setupPath = "$env:TEMP\AdobeInstaller\Adobe Acrobat\setup.exe"
                    if (Test-Path $setupPath) {
                        $process = Start-Process -FilePath $setupPath -ArgumentList "/sAll", "/rs", "/msi", "EULA_ACCEPT=YES" -Wait -PassThru -WindowStyle Hidden
                        
                        Write-Host "[$(Get-Date)] Installation process completed with exit code: $($process.ExitCode)"
                        
                        # Wait for any remaining MSI processes
                        do {
                            Start-Sleep -Seconds 5
                            $msiProcesses = Get-Process -Name "msiexec" -ErrorAction SilentlyContinue
                        } while ($msiProcesses)
                        
                        # Clean up
                        Remove-Item "$env:TEMP\AdobeInstaller" -Recurse -Force -ErrorAction SilentlyContinue
                        
                        # Verify installation
                        if (Test-Path "C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe") {
                            Write-Host "[$(Get-Date)] Adobe Acrobat DC installation successful" -ForegroundColor Green
                            return 0
                        } else {
                            Write-Host "[$(Get-Date)] Adobe Acrobat DC installation verification failed" -ForegroundColor Red
                            return 1
                        }
                    } else {
                        throw "Setup.exe not found after extraction"
                    }
                }
                catch {
                    Write-Error "[$(Get-Date)] Installation failed: $_"
                    # Clean up on error
                    Remove-Item "$env:TEMP\AdobeInstaller" -Recurse -Force -ErrorAction SilentlyContinue
                    return 1
                }
            }
        }
        
        Remove-PSSession $session

        # Handle the result properly - extract just the exit code
        $exitCode = $null
        if ($result -is [array]) {
            # If result is an array, get the last numeric value (exit code)
            $exitCode = $result | Where-Object { $_ -is [int] -or ($_ -match '^\d+$') } | Select-Object -Last 1
        } elseif ($result -is [int] -or ($result -match '^\d+$')) {
            $exitCode = [int]$result
        } else {
            # If we can't determine exit code, check for success indicators in output
            $resultString = $result | Out-String
            if ($resultString -like "*installation successful*" -or $resultString -like "*successfully installed*") {
                $exitCode = 0
            } else {
                $exitCode = 1
            }
        }

        if ($exitCode -eq 0) {
            Write-Host "Adobe Acrobat DC successfully installed on $ComputerName" -ForegroundColor Green
            return $true
        } else {
            Write-Error "Adobe Acrobat DC installation failed on $ComputerName (Exit Code: $exitCode)"
            return $false
        }
    }
    catch {
        Write-Error "Failed to install Adobe on $ComputerName`: $_"
        if ($session) { Remove-PSSession $session -ErrorAction SilentlyContinue }
        return $false
    }
}

# Main execution
Write-Host "========================================================================" -ForegroundColor Cyan
Write-Host "           Adobe Acrobat DC Remote Deployment Script" -ForegroundColor Cyan
Write-Host "           Developed by: The greatest technician that ever lived" -ForegroundColor Cyan
Write-Host "========================================================================" -ForegroundColor Cyan

# Get computer names if not provided
if (-not $ComputerNames -and -not $ComputerListFile) {
    $ComputerNames = Get-ComputerNames
    if (-not $ComputerNames) {
        Write-Error "No computer names provided"
        exit 1
    }
}
elseif ($ComputerListFile) {
    # Load from file if specified
    if (-not (Test-Path $ComputerListFile)) {
        Write-Error "Computer list file not found: $ComputerListFile"
        exit 1
    }

    try {
        $ComputerNames = Get-Content $ComputerListFile | Where-Object { -not [string]::IsNullOrWhiteSpace($_) } | ForEach-Object { $_.Trim() }
        Write-Host "`nLoaded $($ComputerNames.Count) computer names from file: $ComputerListFile" -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to read computer list file: $_"
        exit 1
    }
}

# Choose installation method if not specified
if (-not $PSBoundParameters.ContainsKey('UseLocalInstaller')) {
    Write-Host "`n========================================================================" -ForegroundColor Yellow
    Write-Host "                    Installation Method Selection" -ForegroundColor Yellow
    Write-Host "========================================================================" -ForegroundColor Yellow
    Write-Host "1. Network download (download installer on each target PC)"
    Write-Host "2. Local installer (copy local adobe.bat file to target PCs)"
    Write-Host ""

    do {
        $installChoice = Read-Host "Select installation method (1 or 2)"
    } while ($installChoice -notmatch '^[12]$')

    if ($installChoice -eq "2") {
        $UseLocalInstaller = $true

        # Get local installer path if not provided
        if (-not $LocalInstallerPath -or -not (Test-Path $LocalInstallerPath)) {
            do {
                $LocalInstallerPath = Read-Host "`nEnter path to local adobe.bat file"
            } while ([string]::IsNullOrWhiteSpace($LocalInstallerPath) -or -not (Test-Path $LocalInstallerPath))
        }

        Write-Host "Using local installer: $LocalInstallerPath" -ForegroundColor Green
    } else {
        Write-Host "Using network download method" -ForegroundColor Green
    }
}

# Get credentials if not provided
if (-not $Credential) {
    Write-Host "`n========================================================================" -ForegroundColor Yellow
    Write-Host "                    Credential Authentication" -ForegroundColor Yellow
    Write-Host "========================================================================" -ForegroundColor Yellow
    Write-Host "Enter domain credentials for remote computer access:" -ForegroundColor Yellow
    $Credential = Get-Credential -Message "Enter domain credentials for remote installation"

    if (-not $Credential) {
        Write-Error "Credentials are required for remote installation"
        exit 1
    }
}

# Display deployment summary
Write-Host "`n========================================================================" -ForegroundColor Cyan
Write-Host "                    Deployment Summary" -ForegroundColor Cyan
Write-Host "========================================================================" -ForegroundColor Cyan
Write-Host "Target Computers: $($ComputerNames.Count)" -ForegroundColor White
Write-Host "Installation Method: $(if ($UseLocalInstaller) { 'Local Installer' } else { 'Network Download' })" -ForegroundColor White
if ($UseLocalInstaller) {
    Write-Host "Local Installer Path: $LocalInstallerPath" -ForegroundColor White
}
Write-Host "Credentials: $($Credential.UserName)" -ForegroundColor White
Write-Host ""
Write-Host "Target Computers:" -ForegroundColor Yellow
$ComputerNames | ForEach-Object { Write-Host "  - $_" -ForegroundColor Gray }
Write-Host ""

# Pre-deployment connectivity check
Write-Host "`n========================================================================" -ForegroundColor Cyan
Write-Host "                    Pre-Deployment Connectivity Check" -ForegroundColor Cyan
Write-Host "========================================================================" -ForegroundColor Cyan

$validComputers = @()
$invalidComputers = @()

foreach ($computer in $ComputerNames) {
    Write-Host "`nChecking $computer..." -ForegroundColor White

    if (Test-ComputerConnectivity -ComputerName $computer) {
        # Also test PSRemoting connectivity
        Write-Host "  Testing PSRemoting connectivity..." -ForegroundColor Yellow
        if (Check-PSRemoting -ComputerName $computer) {
            Write-Host "  PSRemoting is available" -ForegroundColor Green
            $validComputers += $computer
            Write-Host "✓ $computer is ready for deployment" -ForegroundColor Green
        } else {
            Write-Host "  PSRemoting not available, but can be enabled" -ForegroundColor Yellow
            $validComputers += $computer
            Write-Host "✓ $computer is ready for deployment (PSRemoting will be enabled)" -ForegroundColor Green
        }
    } else {
        $invalidComputers += $computer
        Write-Host "✗ $computer failed connectivity check" -ForegroundColor Red
    }
}

# Display results
Write-Host "`n========================================================================" -ForegroundColor Cyan
Write-Host "                    Connectivity Check Results" -ForegroundColor Cyan
Write-Host "========================================================================" -ForegroundColor Cyan
Write-Host "Valid computers: $($validComputers.Count)" -ForegroundColor Green
Write-Host "Invalid computers: $($invalidComputers.Count)" -ForegroundColor Red

if ($invalidComputers.Count -gt 0) {
    Write-Host "`nComputers that failed connectivity check:" -ForegroundColor Red
    $invalidComputers | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }

    if ($validComputers.Count -eq 0) {
        Write-Error "No computers are available for deployment"
        exit 1
    }

    Write-Host "`nDo you want to:" -ForegroundColor Yellow
    Write-Host "1. Continue with valid computers only"
    Write-Host "2. Re-enter computer names for failed computers"
    Write-Host "3. Cancel deployment"

    do {
        $choice = Read-Host "Select option (1, 2, or 3)"
    } while ($choice -notmatch '^[123]$')

    switch ($choice) {
        "1" {
            $ComputerNames = $validComputers
            Write-Host "Proceeding with $($validComputers.Count) valid computers" -ForegroundColor Green
        }
        "2" {
            Write-Host "`nRe-enter computer names for failed computers:" -ForegroundColor Yellow
            $additionalComputers = @()

            foreach ($failedComputer in $invalidComputers) {
                Write-Host "`nOriginal name: $failedComputer (failed)" -ForegroundColor Red
                $newName = Read-Host "Enter new name (or press Enter to skip)"

                if (-not [string]::IsNullOrWhiteSpace($newName)) {
                    Write-Host "Testing $newName..." -ForegroundColor Yellow
                    if (Test-ComputerConnectivity -ComputerName $newName.Trim()) {
                        $additionalComputers += $newName.Trim()
                        Write-Host "✓ $newName is ready for deployment" -ForegroundColor Green
                    } else {
                        Write-Host "✗ $newName also failed connectivity check" -ForegroundColor Red
                    }
                }
            }

            $ComputerNames = $validComputers + $additionalComputers
            Write-Host "`nProceeding with $($ComputerNames.Count) total computers" -ForegroundColor Green
        }
        "3" {
            Write-Host "Deployment cancelled by user" -ForegroundColor Yellow
            exit 0
        }
    }
}

if ($ComputerNames.Count -eq 0) {
    Write-Error "No valid computers available for deployment"
    exit 1
}

Write-Host "`nFinal deployment list:" -ForegroundColor Cyan
$ComputerNames | ForEach-Object { Write-Host "  - $_" -ForegroundColor Green }

$confirm = Read-Host "`nProceed with deployment? (Y/N)"
if ($confirm -notmatch '^[Yy]') {
    Write-Host "Deployment cancelled by user" -ForegroundColor Yellow
    exit 0
}

$successCount = 0
$failCount = 0
$results = @()

foreach ($computer in $ComputerNames) {
    $success = Install-AdobeRemote -ComputerName $computer -Cred $Credential -UseLocal $UseLocalInstaller.IsPresent -LocalPath $LocalInstallerPath
    
    if ($success) {
        $successCount++
        $results += [PSCustomObject]@{
            ComputerName = $computer
            Status = "Success"
            Message = "Adobe Acrobat DC installed successfully"
        }
    } else {
        $failCount++
        $results += [PSCustomObject]@{
            ComputerName = $computer
            Status = "Failed"
            Message = "Installation failed"
        }
    }
}

# Summary
Write-Host "`n========================================================================" -ForegroundColor Cyan
Write-Host "                        Installation Summary" -ForegroundColor Cyan
Write-Host "========================================================================" -ForegroundColor Cyan
Write-Host "Total Computers: $($ComputerNames.Count)" -ForegroundColor White
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red
Write-Host ""

$results | Format-Table -AutoSize

Write-Host "Deployment completed!" -ForegroundColor Cyan
