# Install Missing Applications from Source PC
# Installs applications that exist on source PC but are missing on target PC
# Developed by: The greatest technician that ever lived

param(
    [string]$CopyFromPC,
    [string]$CopyToPC,
    [PSCredential]$Credential,
    [switch]$AutoInstall,
    [switch]$ListOnly
)

# Function to display banner
function Show-AppInstallBanner {
    Clear-Host
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "    Missing Applications Installer" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "Developed by: The greatest technician that ever lived" -ForegroundColor Green
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host ""
}

# Function to get installed applications from a PC
function Get-InstalledApplications {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )
    
    Write-Host "Gathering installed applications from $ComputerName..." -ForegroundColor Yellow
    
    try {
        $apps = @()
        
        # Check if we're querying the local computer
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            Write-Host "Querying local computer..." -ForegroundColor Cyan
            
            # Get applications from registry (64-bit) - Local
            $apps += Get-ItemProperty "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
                Where-Object {
                    $_.DisplayName -and
                    $_.DisplayName -notlike "KB*" -and
                    $_.DisplayName -notlike "Microsoft*" -and
                    $_.DisplayName -notlike "Windows*" -and
                    $_.DisplayName -notlike "*Visual C++*" -and
                    $_.DisplayName -notlike "*DirectX*" -and
                    $_.DisplayName -notlike "*Framework*" -and
                    $_.DisplayName -notlike "*Runtime*" -and
                    $_.DisplayName -notlike "*Redistributable*" -and
                    $_.Publisher -notlike "Microsoft*"
                } |
                Select-Object DisplayName, DisplayVersion, Publisher

            # Get applications from registry (32-bit on 64-bit systems) - Local
            $apps += Get-ItemProperty "HKLM:\Software\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
                Where-Object {
                    $_.DisplayName -and
                    $_.DisplayName -notlike "KB*" -and
                    $_.DisplayName -notlike "Microsoft*" -and
                    $_.DisplayName -notlike "Windows*" -and
                    $_.DisplayName -notlike "*Visual C++*" -and
                    $_.DisplayName -notlike "*DirectX*" -and
                    $_.DisplayName -notlike "*Framework*" -and
                    $_.DisplayName -notlike "*Runtime*" -and
                    $_.DisplayName -notlike "*Redistributable*" -and
                    $_.Publisher -notlike "Microsoft*"
                } |
                Select-Object DisplayName, DisplayVersion, Publisher
        } else {
            Write-Host "Trying WMI query..." -ForegroundColor Cyan
            
            # Try WMI for remote computers
            try {
                if ($Credential) {
                    $wmiApps = Get-WmiObject -Class Win32_Product -ComputerName $ComputerName -Credential $Credential -ErrorAction Stop |
                        Where-Object {
                            $_.Name -and
                            $_.Name -notlike "KB*" -and
                            $_.Name -notlike "Microsoft*" -and
                            $_.Name -notlike "Windows*" -and
                            $_.Name -notlike "*Visual C++*" -and
                            $_.Name -notlike "*DirectX*" -and
                            $_.Name -notlike "*Framework*" -and
                            $_.Name -notlike "*Runtime*" -and
                            $_.Name -notlike "*Redistributable*" -and
                            $_.Vendor -notlike "Microsoft*"
                        } |
                        Select-Object @{Name="DisplayName";Expression={$_.Name}},
                                      @{Name="DisplayVersion";Expression={$_.Version}},
                                      @{Name="Publisher";Expression={$_.Vendor}}
                } else {
                    $wmiApps = Get-WmiObject -Class Win32_Product -ComputerName $ComputerName -ErrorAction Stop |
                        Where-Object {
                            $_.Name -and
                            $_.Name -notlike "KB*" -and
                            $_.Name -notlike "Microsoft*" -and
                            $_.Name -notlike "Windows*" -and
                            $_.Name -notlike "*Visual C++*" -and
                            $_.Name -notlike "*DirectX*" -and
                            $_.Name -notlike "*Framework*" -and
                            $_.Name -notlike "*Runtime*" -and
                            $_.Name -notlike "*Redistributable*" -and
                            $_.Vendor -notlike "Microsoft*"
                        } |
                        Select-Object @{Name="DisplayName";Expression={$_.Name}},
                                      @{Name="DisplayVersion";Expression={$_.Version}},
                                      @{Name="Publisher";Expression={$_.Vendor}}
                }
                $apps += $wmiApps
                Write-Host "WMI query successful!" -ForegroundColor Green
            } catch {
                Write-Host "WMI query failed: $($_.Exception.Message)" -ForegroundColor Red
                
                # Fallback to PowerShell remoting
                Write-Host "Trying PowerShell remoting..." -ForegroundColor Yellow
                try {
                    if ($Credential) {
                        $apps += Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock {
                            $localApps = @()
                            $localApps += Get-ItemProperty "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
                                Where-Object {
                                    $_.DisplayName -and
                                    $_.DisplayName -notlike "KB*" -and
                                    $_.DisplayName -notlike "Microsoft*" -and
                                    $_.DisplayName -notlike "Windows*" -and
                                    $_.DisplayName -notlike "*Visual C++*" -and
                                    $_.DisplayName -notlike "*DirectX*" -and
                                    $_.DisplayName -notlike "*Framework*" -and
                                    $_.DisplayName -notlike "*Runtime*" -and
                                    $_.DisplayName -notlike "*Redistributable*" -and
                                    $_.Publisher -notlike "Microsoft*"
                                } |
                                Select-Object DisplayName, DisplayVersion, Publisher

                            $localApps += Get-ItemProperty "HKLM:\Software\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
                                Where-Object {
                                    $_.DisplayName -and
                                    $_.DisplayName -notlike "KB*" -and
                                    $_.DisplayName -notlike "Microsoft*" -and
                                    $_.DisplayName -notlike "Windows*" -and
                                    $_.DisplayName -notlike "*Visual C++*" -and
                                    $_.DisplayName -notlike "*DirectX*" -and
                                    $_.DisplayName -notlike "*Framework*" -and
                                    $_.DisplayName -notlike "*Runtime*" -and
                                    $_.DisplayName -notlike "*Redistributable*" -and
                                    $_.Publisher -notlike "Microsoft*"
                                } |
                                Select-Object DisplayName, DisplayVersion, Publisher

                            return $localApps
                        } -ErrorAction Stop
                    }
                    Write-Host "PowerShell remoting successful!" -ForegroundColor Green
                } catch {
                    Write-Host "PowerShell remoting failed: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
        
        # Remove duplicates and sort
        $uniqueApps = $apps | Where-Object { $_.DisplayName } | Sort-Object DisplayName -Unique
        
        Write-Host "Found $($uniqueApps.Count) applications on $ComputerName" -ForegroundColor Green
        return $uniqueApps
        
    } catch {
        Write-Host "Error gathering applications from $ComputerName`: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

# Function to compare applications and find missing ones
function Get-MissingApplications {
    param(
        [array]$SourceApps,
        [array]$TargetApps,
        [string]$SourcePCName,
        [string]$TargetPCName
    )
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "COMPARING APPLICATIONS" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    # Find applications missing on target PC
    $missingApps = @()
    foreach ($sourceApp in $SourceApps) {
        $found = $false
        foreach ($targetApp in $TargetApps) {
            if ($sourceApp.DisplayName -eq $targetApp.DisplayName) {
                $found = $true
                break
            }
        }
        if (-not $found) {
            $missingApps += $sourceApp
        }
    }
    
    Write-Host "Applications on $SourcePCName`: $($SourceApps.Count)" -ForegroundColor Cyan
    Write-Host "Applications on $TargetPCName`: $($TargetApps.Count)" -ForegroundColor Cyan
    Write-Host "Missing on $TargetPCName`: $($missingApps.Count)" -ForegroundColor Red
    
    return $missingApps
}

# Function to map application names to package managers
function Get-PackageMapping {
    param([string]$AppName)
    
    # Common application mappings
    $packageMap = @{
        "Google Chrome" = @{Chocolatey = "googlechrome"; Winget = "Google.Chrome"}
        "Mozilla Firefox" = @{Chocolatey = "firefox"; Winget = "Mozilla.Firefox"}
        "7-Zip" = @{Chocolatey = "7zip"; Winget = "7zip.7zip"}
        "Adobe Acrobat Reader" = @{Chocolatey = "adobereader"; Winget = "Adobe.Acrobat.Reader.64-bit"}
        "Adobe Reader" = @{Chocolatey = "adobereader"; Winget = "Adobe.Acrobat.Reader.64-bit"}
        "VLC media player" = @{Chocolatey = "vlc"; Winget = "VideoLAN.VLC"}
        "Notepad++" = @{Chocolatey = "notepadplusplus"; Winget = "Notepad++.Notepad++"}
        "Microsoft Teams" = @{Chocolatey = "microsoft-teams"; Winget = "Microsoft.Teams"}
        "Zoom" = @{Chocolatey = "zoom"; Winget = "Zoom.Zoom"}
        "Citrix Workspace" = @{Chocolatey = "citrix-workspace"; Winget = "Citrix.Workspace"}
        "Git" = @{Chocolatey = "git"; Winget = "Git.Git"}
        "Visual Studio Code" = @{Chocolatey = "vscode"; Winget = "Microsoft.VisualStudioCode"}
        "WinRAR" = @{Chocolatey = "winrar"; Winget = "RARLab.WinRAR"}
        "CCleaner" = @{Chocolatey = "ccleaner"; Winget = "Piriform.CCleaner"}
        "Malwarebytes" = @{Chocolatey = "malwarebytes"; Winget = "Malwarebytes.Malwarebytes"}
        "TreeSize Free" = @{Chocolatey = "treesizefree"; Winget = "JAMSoftware.TreeSize.Free"}
        "PowerShell 7" = @{Chocolatey = "powershell-core"; Winget = "Microsoft.PowerShell"}
        "LibreOffice" = @{Chocolatey = "libreoffice-fresh"; Winget = "TheDocumentFoundation.LibreOffice"}
        "Google Update Helper" = @{Chocolatey = "googlechrome"; Winget = "Google.Chrome"}
        "Microsoft Update Health Tools" = @{Chocolatey = $null; Winget = $null; Notes = "Install via Windows Update"}
    }
    
    # Try exact match first
    if ($packageMap.ContainsKey($AppName)) {
        return $packageMap[$AppName]
    }
    
    # Try partial matches
    foreach ($key in $packageMap.Keys) {
        if ($AppName -like "*$key*" -or $key -like "*$AppName*") {
            return $packageMap[$key]
        }
    }
    
    return $null
}

# Function to check package managers
function Test-PackageManagers {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )
    
    $scriptBlock = {
        $managers = @{
            Chocolatey = $null -ne (Get-Command choco -ErrorAction SilentlyContinue)
            Winget = $null -ne (Get-Command winget -ErrorAction SilentlyContinue)
        }
        return $managers
    }
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            return & $scriptBlock
        } else {
            if ($Credential) {
                return Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock
            } else {
                return Invoke-Command -ComputerName $ComputerName -ScriptBlock $scriptBlock
            }
        }
    } catch {
        return @{Chocolatey = $false; Winget = $false}
    }
}

# Function to install application
function Install-Application {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential,
        [hashtable]$App,
        [hashtable]$PackageInfo,
        [hashtable]$AvailableManagers
    )
    
    Write-Host "Installing $($App.DisplayName)..." -ForegroundColor Yellow
    
    # Choose installation method
    $installMethod = $null
    $packageName = $null
    
    if ($AvailableManagers.Chocolatey -and $PackageInfo.Chocolatey) {
        $installMethod = "Chocolatey"
        $packageName = $PackageInfo.Chocolatey
    } elseif ($AvailableManagers.Winget -and $PackageInfo.Winget) {
        $installMethod = "Winget"
        $packageName = $PackageInfo.Winget
    } else {
        if ($PackageInfo.Notes) {
            Write-Host "  $($PackageInfo.Notes)" -ForegroundColor Yellow
        } else {
            Write-Host "  No package manager available for $($App.DisplayName)" -ForegroundColor Red
        }
        return $false
    }
    
    $scriptBlock = {
        param($method, $package)
        
        try {
            if ($method -eq "Chocolatey") {
                $result = & choco install $package -y --force 2>&1
                return @{Success = $LASTEXITCODE -eq 0; Output = $result -join "`n"}
            } elseif ($method -eq "Winget") {
                $result = & winget install --id $package --silent --accept-package-agreements --accept-source-agreements 2>&1
                return @{Success = $LASTEXITCODE -eq 0; Output = $result -join "`n"}
            }
        } catch {
            return @{Success = $false; Output = $_.Exception.Message}
        }
    }
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            $result = & $scriptBlock $installMethod $packageName
        } else {
            if ($Credential) {
                $result = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock -ArgumentList $installMethod, $packageName
            } else {
                $result = Invoke-Command -ComputerName $ComputerName -ScriptBlock $scriptBlock -ArgumentList $installMethod, $packageName
            }
        }
        
        if ($result.Success) {
            Write-Host "  Successfully installed $($App.DisplayName) via $installMethod" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  Failed to install $($App.DisplayName)" -ForegroundColor Red
            Write-Host "  Error: $($result.Output)" -ForegroundColor Gray
            return $false
        }
    } catch {
        Write-Host "  Error installing $($App.DisplayName): $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
Show-AppInstallBanner

# Get PC names if not provided
if (-not $CopyFromPC) {
    $CopyFromPC = Read-Host "Enter the source PC name (copy FROM - has the applications)"
}

if (-not $CopyToPC -and -not $ListOnly) {
    $CopyToPC = Read-Host "Enter the target PC name (copy TO - needs the applications)"
}

# Get credentials if needed
$needsCredentials = ($CopyFromPC -ne $env:COMPUTERNAME -and $CopyFromPC -ne "localhost" -and $CopyFromPC -ne ".") -or 
                   ($CopyToPC -and $CopyToPC -ne $env:COMPUTERNAME -and $CopyToPC -ne "localhost" -and $CopyToPC -ne ".")

if ($needsCredentials -and -not $Credential) {
    Write-Host "Remote computer access detected. Credentials required." -ForegroundColor Yellow
    $Credential = Get-Credential -Message "Enter credentials for remote computer access"
}

Write-Host "Copy FROM PC: $CopyFromPC" -ForegroundColor Cyan
if ($CopyToPC) {
    Write-Host "Copy TO PC: $CopyToPC" -ForegroundColor Cyan
}

# Get applications from source PC
$sourceApps = Get-InstalledApplications -ComputerName $CopyFromPC -Credential $Credential

if ($sourceApps.Count -eq 0) {
    Write-Host "No applications found on source PC. Cannot proceed." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if ($ListOnly) {
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "APPLICATIONS ON $CopyFromPC" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    $sourceApps | Sort-Object DisplayName | ForEach-Object {
        $packageInfo = Get-PackageMapping -AppName $_.DisplayName
        $installable = if ($packageInfo -and ($packageInfo.Chocolatey -or $packageInfo.Winget)) { "Yes" } else { "Manual" }
        
        Write-Host "$($_.DisplayName) ($($_.DisplayVersion))" -ForegroundColor White
        Write-Host "  Publisher: $($_.Publisher)" -ForegroundColor Gray
        Write-Host "  Auto-installable: $installable" -ForegroundColor $(if($installable -eq "Yes"){"Green"}else{"Yellow"})
        Write-Host ""
    }
    
    $installableCount = ($sourceApps | ForEach-Object { 
        $packageInfo = Get-PackageMapping -AppName $_.DisplayName
        if ($packageInfo -and ($packageInfo.Chocolatey -or $packageInfo.Winget)) { 1 } else { 0 }
    } | Measure-Object -Sum).Sum
    
    Write-Host "Summary:" -ForegroundColor Cyan
    Write-Host "  Total applications: $($sourceApps.Count)" -ForegroundColor White
    Write-Host "  Auto-installable: $installableCount" -ForegroundColor Green
    Write-Host "  Manual install required: $($sourceApps.Count - $installableCount)" -ForegroundColor Yellow
    
    Read-Host "`nPress Enter to exit"
    exit 0
}

# Get applications from target PC
$targetApps = Get-InstalledApplications -ComputerName $CopyToPC -Credential $Credential

# Find missing applications
$missingApps = Get-MissingApplications -SourceApps $sourceApps -TargetApps $targetApps -SourcePCName $CopyFromPC -TargetPCName $CopyToPC

if ($missingApps.Count -eq 0) {
    Write-Host "`nNo missing applications found. Both computers have the same applications!" -ForegroundColor Green
    Read-Host "Press Enter to exit"
    exit 0
}

# Show missing applications
Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "MISSING APPLICATIONS TO INSTALL" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

$installableApps = @()
$manualApps = @()

foreach ($app in $missingApps) {
    $packageInfo = Get-PackageMapping -AppName $app.DisplayName
    
    if ($packageInfo -and ($packageInfo.Chocolatey -or $packageInfo.Winget)) {
        $installableApps += @{App = $app; Package = $packageInfo}
        Write-Host "$($app.DisplayName) ($($app.DisplayVersion))" -ForegroundColor Green
        Write-Host "  Can be installed automatically" -ForegroundColor Gray
    } else {
        $manualApps += $app
        Write-Host "$($app.DisplayName) ($($app.DisplayVersion))" -ForegroundColor Yellow
        Write-Host "  Requires manual installation" -ForegroundColor Gray
    }
}

Write-Host "`nSummary:" -ForegroundColor Cyan
Write-Host "  Auto-installable: $($installableApps.Count)" -ForegroundColor Green
Write-Host "  Manual install required: $($manualApps.Count)" -ForegroundColor Yellow

if ($installableApps.Count -eq 0) {
    Write-Host "`nNo applications can be installed automatically." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 0
}

# Check package managers on target PC
Write-Host "`nChecking package managers on $CopyToPC..." -ForegroundColor Cyan
$managers = Test-PackageManagers -ComputerName $CopyToPC -Credential $Credential

Write-Host "Available package managers:" -ForegroundColor Yellow
Write-Host "  Chocolatey: $(if($managers.Chocolatey){'Available'}else{'Not Available'})" -ForegroundColor $(if($managers.Chocolatey){'Green'}else{'Red'})
Write-Host "  Winget: $(if($managers.Winget){'Available'}else{'Not Available'})" -ForegroundColor $(if($managers.Winget){'Green'}else{'Red'})

if (-not $managers.Chocolatey -and -not $managers.Winget) {
    Write-Host "`nNo package managers available. Please install Chocolatey or ensure Winget is available." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Install applications
Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "INSTALLING MISSING APPLICATIONS" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

$successCount = 0
$failCount = 0

foreach ($item in $installableApps) {
    if ($AutoInstall -or (Read-Host "Install $($item.App.DisplayName)? (y/n)") -eq 'y') {
        $success = Install-Application -ComputerName $CopyToPC -Credential $Credential -App $item.App -PackageInfo $item.Package -AvailableManagers $managers
        if ($success) {
            $successCount++
        } else {
            $failCount++
        }
        Start-Sleep -Seconds 2  # Brief pause between installations
    }
}

Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "INSTALLATION SUMMARY" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

Write-Host "Successfully installed: $successCount applications" -ForegroundColor Green
Write-Host "Failed installations: $failCount applications" -ForegroundColor Red
Write-Host "Manual installations needed: $($manualApps.Count) applications" -ForegroundColor Yellow

if ($manualApps.Count -gt 0) {
    Write-Host "`nApplications requiring manual installation:" -ForegroundColor Yellow
    $manualApps | ForEach-Object {
        Write-Host "  - $($_.DisplayName) ($($_.DisplayVersion))" -ForegroundColor White
    }
}

Read-Host "`nPress Enter to exit"
