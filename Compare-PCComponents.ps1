# PC Component Comparison Script
# Compares installed applications and drivers between two PCs
# Developed by: The greatest technician that ever lived

param(
    [string]$SourcePC,
    [string]$TargetPC,
    [switch]$SaveReport,
    [PSCredential]$Credential
)

# Function to display banner
function Show-Banner {
    Clear-Host
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "    PC Component Comparison Tool" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "Developed by: The greatest technician that ever lived" -ForegroundColor Green
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host ""
}

# Function to invoke remote commands
Function Invoke-RemoteCommand {
    param(
        [string]$ComputerName,
        [string]$CommandLine
    )

    $remoteOutput = Invoke-Command -ComputerName $ComputerName -ScriptBlock ([scriptblock]::Create($CommandLine)) -AsJob
    while ($remoteOutput.HasMoreData -eq $true) {
        if ($remoteOutput.hasmoredata) {
           Receive-Job -Job $remoteOutput
        }
        Start-Sleep -Milliseconds 200
    }
}

# Function to check PSRemoting status
Function Test-PSRemotingStatus {
    param(
        [string]$ComputerName
    )
    try {
        $winrm = Get-WmiObject -Class Win32_Service -ComputerName $ComputerName | Where-Object { $_.Name -eq 'WinRM' } -ErrorAction SilentlyContinue
        if ($winrm.Status -ne "Running") {
            return $false
        } else {
            # Try a simple test instead of firewall rule check which might fail
            try {
                Test-WSMan -ComputerName $ComputerName -ErrorAction Stop | Out-Null
                return $true
            } catch {
                return $false
            }
        }
    } catch {
        return $false
    }
}

# Function to enable PSRemoting
Function Enable-PSRemoting {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )
    try {
        Write-Host "  Enabling PSRemoting on $ComputerName..." -ForegroundColor Yellow

        # Try with credentials if provided
        if ($Credential) {
            $SessionArgs = @{
                ComputerName = $ComputerName
                SessionOption = New-CimSessionOption -Protocol Dcom
                Credential = $Credential
            }
        } else {
            $SessionArgs = @{
                ComputerName = $ComputerName
                SessionOption = New-CimSessionOption -Protocol Dcom
            }
        }

        $CimSession = New-CimSession @SessionArgs

        $MethodArgsEnablePS = @{
            ClassName = 'Win32_Process'
            MethodName = 'Create'
            CimSession = $CimSession
            Arguments = @{ CommandLine = 'powershell -Command "Enable-PSRemoting -Force"' }
        }
        Invoke-CimMethod @MethodArgsEnablePS | Out-Null

        $MethodArgsRunWinRM = @{
            ClassName = 'Win32_Process'
            MethodName = 'Create'
            CimSession = $CimSession
            Arguments = @{ CommandLine = 'winrm qc -quiet' }
        }
        Invoke-CimMethod @MethodArgsRunWinRM | Out-Null

        Remove-CimSession -CimSession $CimSession
        Write-Host "  PSRemoting enabled successfully" -ForegroundColor Green
        Start-Sleep -Seconds 3  # Give time for services to start
    } catch {
        Write-Host "  Failed to enable PSRemoting: $($_.Exception.Message)" -ForegroundColor Red
        throw
    }
}

# Function to execute remote command with PSRemoting check
Function Invoke-RemoteCommandWithCheck {
    param(
        [string]$ComputerName,
        [string]$CommandLine,
        [PSCredential]$Credential
    )
    if (-not (Test-PSRemotingStatus -ComputerName $ComputerName)) {
        try {
            Enable-PSRemoting -ComputerName $ComputerName -Credential $Credential
        }
        catch {
            Write-Error "Failed to enable PS Remoting on $ComputerName`: $_"
            return $null
        }
    }
    return Invoke-RemoteCommand -ComputerName $ComputerName -CommandLine $CommandLine
}

# Function to test PC connectivity
function Test-PCConnectivity {
    param([string]$ComputerName)
    
    Write-Host "Testing connectivity to $ComputerName..." -ForegroundColor Yellow
    
    if (Test-Connection -ComputerName $ComputerName -Count 2 -Quiet) {
        Write-Host "[SUCCESS] $ComputerName is online" -ForegroundColor Green
        return $true
    } else {
        Write-Host "[ERROR] $ComputerName is offline or unreachable" -ForegroundColor Red
        return $false
    }
}

# Function to get installed applications
function Get-InstalledApplications {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )

    Write-Host "Gathering installed applications from $ComputerName..." -ForegroundColor Yellow

    try {
        $apps = @()

        # Check if we're querying the local computer
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            Write-Host "Querying local computer..." -ForegroundColor Cyan

            # Get applications from registry (64-bit) - Local
            $apps += Get-ItemProperty "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
                Where-Object { $_.DisplayName -and $_.DisplayName -notlike "KB*" } |
                Select-Object DisplayName, DisplayVersion, Publisher

            # Get applications from registry (32-bit on 64-bit systems) - Local
            $apps += Get-ItemProperty "HKLM:\Software\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
                Where-Object { $_.DisplayName -and $_.DisplayName -notlike "KB*" } |
                Select-Object DisplayName, DisplayVersion, Publisher
        } else {
            Write-Host "Trying remote access methods..." -ForegroundColor Cyan

            # Method 1: Try WMI first (this is working based on your output)
            Write-Host "  Attempting WMI query..." -ForegroundColor Yellow
            try {
                if ($Credential) {
                    $wmiApps = Get-WmiObject -Class Win32_Product -ComputerName $ComputerName -Credential $Credential -ErrorAction Stop |
                        Where-Object { $_.Name -and $_.Name -notlike "KB*" } |
                        Select-Object @{Name="DisplayName";Expression={$_.Name}},
                                      @{Name="DisplayVersion";Expression={$_.Version}},
                                      @{Name="Publisher";Expression={$_.Vendor}}
                } else {
                    $wmiApps = Get-WmiObject -Class Win32_Product -ComputerName $ComputerName -ErrorAction Stop |
                        Where-Object { $_.Name -and $_.Name -notlike "KB*" } |
                        Select-Object @{Name="DisplayName";Expression={$_.Name}},
                                      @{Name="DisplayVersion";Expression={$_.Version}},
                                      @{Name="Publisher";Expression={$_.Vendor}}
                }
                $apps += $wmiApps
                Write-Host "  WMI query successful!" -ForegroundColor Green
            } catch {
                Write-Host "  WMI query failed: $($_.Exception.Message)" -ForegroundColor Red
            }

            # Method 3: Try PowerShell remoting with PSRemoting enablement if WMI failed
            if ($apps.Count -eq 0) {
                Write-Host "  Attempting PowerShell remoting with auto-enablement..." -ForegroundColor Yellow
                try {
                    # First try standard PowerShell remoting
                    if ($Credential) {
                        $apps += Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock {
                            $localApps = @()
                            $localApps += Get-ItemProperty "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
                                Where-Object { $_.DisplayName -and $_.DisplayName -notlike "KB*" } |
                                Select-Object DisplayName, DisplayVersion, Publisher

                            $localApps += Get-ItemProperty "HKLM:\Software\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
                                Where-Object { $_.DisplayName -and $_.DisplayName -notlike "KB*" } |
                                Select-Object DisplayName, DisplayVersion, Publisher

                            return $localApps
                        } -ErrorAction Stop
                    } else {
                        $apps += Invoke-Command -ComputerName $ComputerName -ScriptBlock {
                            $localApps = @()
                            $localApps += Get-ItemProperty "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
                                Where-Object { $_.DisplayName -and $_.DisplayName -notlike "KB*" } |
                                Select-Object DisplayName, DisplayVersion, Publisher

                            $localApps += Get-ItemProperty "HKLM:\Software\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
                                Where-Object { $_.DisplayName -and $_.DisplayName -notlike "KB*" } |
                                Select-Object DisplayName, DisplayVersion, Publisher

                            return $localApps
                        } -ErrorAction Stop
                    }
                    Write-Host "  PowerShell remoting successful!" -ForegroundColor Green
                } catch {
                    Write-Host "  Standard PowerShell remoting failed, trying with PSRemoting enablement..." -ForegroundColor Yellow
                    try {
                        # Use the enhanced remote command execution
                        $commandLine = @"
`$localApps = @()
`$localApps += Get-ItemProperty 'HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*' -ErrorAction SilentlyContinue | Where-Object { `$_.DisplayName -and `$_.DisplayName -notlike 'KB*' } | Select-Object DisplayName, DisplayVersion, Publisher
`$localApps += Get-ItemProperty 'HKLM:\Software\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*' -ErrorAction SilentlyContinue | Where-Object { `$_.DisplayName -and `$_.DisplayName -notlike 'KB*' } | Select-Object DisplayName, DisplayVersion, Publisher
`$localApps | ConvertTo-Json -Depth 3
"@
                        $result = Invoke-RemoteCommandWithCheck -ComputerName $ComputerName -CommandLine $commandLine -Credential $Credential
                        if ($result) {
                            $apps += $result | ConvertFrom-Json
                            Write-Host "  Enhanced PowerShell remoting successful!" -ForegroundColor Green
                        }
                    } catch {
                        Write-Host "  Enhanced PowerShell remoting failed: $($_.Exception.Message)" -ForegroundColor Red
                    }
                }
            }
        }

        # Remove duplicates and sort
        $uniqueApps = $apps | Where-Object { $_.DisplayName } | Sort-Object DisplayName -Unique

        Write-Host "Found $($uniqueApps.Count) applications on $ComputerName" -ForegroundColor Green

        if ($uniqueApps.Count -eq 0) {
            Write-Host "WARNING: No applications found. Possible causes:" -ForegroundColor Yellow
            Write-Host "  - Remote Registry service not running" -ForegroundColor Yellow
            Write-Host "  - WMI service not accessible" -ForegroundColor Yellow
            Write-Host "  - WinRM not configured" -ForegroundColor Yellow
            Write-Host "  - Insufficient permissions" -ForegroundColor Yellow
            Write-Host "  - Firewall blocking access" -ForegroundColor Yellow
        }

        return $uniqueApps

    } catch {
        Write-Host "Error gathering applications from $ComputerName`: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

# Function to get installed drivers
function Get-InstalledDrivers {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )

    Write-Host "Gathering installed drivers from $ComputerName..." -ForegroundColor Yellow

    try {
        $drivers = @()

        # Check if we're querying the local computer
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            Write-Host "Querying local drivers..." -ForegroundColor Cyan

            # Get drivers locally
            $drivers = Get-WmiObject -Class Win32_PnPSignedDriver -ErrorAction SilentlyContinue |
                Where-Object { $_.DeviceName -and $_.DriverVersion } |
                Select-Object DeviceName, DriverVersion, DriverDate, Manufacturer
        } else {
            Write-Host "Trying multiple remote access methods for drivers..." -ForegroundColor Cyan

            # Method 1: Try WMI for remote computer
            Write-Host "  Attempting WMI query..." -ForegroundColor Yellow
            try {
                if ($Credential) {
                    $drivers = Get-WmiObject -Class Win32_PnPSignedDriver -ComputerName $ComputerName -Credential $Credential -ErrorAction Stop |
                        Where-Object { $_.DeviceName -and $_.DriverVersion } |
                        Select-Object DeviceName, DriverVersion, DriverDate, Manufacturer
                } else {
                    $drivers = Get-WmiObject -Class Win32_PnPSignedDriver -ComputerName $ComputerName -ErrorAction Stop |
                        Where-Object { $_.DeviceName -and $_.DriverVersion } |
                        Select-Object DeviceName, DriverVersion, DriverDate, Manufacturer
                }
                Write-Host "  WMI query successful!" -ForegroundColor Green
            } catch {
                Write-Host "  WMI query failed: $($_.Exception.Message)" -ForegroundColor Red
            }

            # Method 2: Try PowerShell remoting with PSRemoting enablement if WMI failed
            if ($drivers.Count -eq 0) {
                Write-Host "  Attempting PowerShell remoting with auto-enablement..." -ForegroundColor Yellow
                try {
                    # First try standard PowerShell remoting
                    if ($Credential) {
                        $drivers = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock {
                            Get-WmiObject -Class Win32_PnPSignedDriver |
                            Where-Object { $_.DeviceName -and $_.DriverVersion } |
                            Select-Object DeviceName, DriverVersion, DriverDate, Manufacturer
                        } -ErrorAction Stop
                    } else {
                        $drivers = Invoke-Command -ComputerName $ComputerName -ScriptBlock {
                            Get-WmiObject -Class Win32_PnPSignedDriver |
                            Where-Object { $_.DeviceName -and $_.DriverVersion } |
                            Select-Object DeviceName, DriverVersion, DriverDate, Manufacturer
                        } -ErrorAction Stop
                    }
                    Write-Host "  PowerShell remoting successful!" -ForegroundColor Green
                } catch {
                    Write-Host "  Standard PowerShell remoting failed, trying with PSRemoting enablement..." -ForegroundColor Yellow
                    try {
                        # Use the enhanced remote command execution
                        $commandLine = @"
Get-WmiObject -Class Win32_PnPSignedDriver | Where-Object { `$_.DeviceName -and `$_.DriverVersion } | Select-Object DeviceName, DriverVersion, DriverDate, Manufacturer | ConvertTo-Json -Depth 3
"@
                        $result = Invoke-RemoteCommandWithCheck -ComputerName $ComputerName -CommandLine $commandLine -Credential $Credential
                        if ($result) {
                            $drivers = $result | ConvertFrom-Json
                            Write-Host "  Enhanced PowerShell remoting successful!" -ForegroundColor Green
                        }
                    } catch {
                        Write-Host "  Enhanced PowerShell remoting failed: $($_.Exception.Message)" -ForegroundColor Red
                    }
                }
            }

            # Method 3: Try alternative WMI classes if standard method failed
            if ($drivers.Count -eq 0) {
                Write-Host "  Attempting alternative driver query..." -ForegroundColor Yellow
                try {
                    $drivers = Get-WmiObject -Class Win32_SystemDriver -ComputerName $ComputerName -ErrorAction Stop |
                        Where-Object { $_.Name -and $_.Version } |
                        Select-Object @{Name="DeviceName";Expression={$_.Name}},
                                      @{Name="DriverVersion";Expression={$_.Version}},
                                      @{Name="DriverDate";Expression={$_.InstallDate}},
                                      @{Name="Manufacturer";Expression={"System"}}
                    Write-Host "  Alternative driver query successful!" -ForegroundColor Green
                } catch {
                    Write-Host "  Alternative driver query failed: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }

        Write-Host "Found $($drivers.Count) drivers on $ComputerName" -ForegroundColor Green

        if ($drivers.Count -eq 0) {
            Write-Host "WARNING: No drivers found. Possible causes:" -ForegroundColor Yellow
            Write-Host "  - WMI service not accessible" -ForegroundColor Yellow
            Write-Host "  - WinRM not configured" -ForegroundColor Yellow
            Write-Host "  - Insufficient permissions" -ForegroundColor Yellow
            Write-Host "  - Firewall blocking WMI access" -ForegroundColor Yellow
        }

        return $drivers

    } catch {
        Write-Host "Error gathering drivers from $ComputerName`: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

# Function to compare applications
function Compare-Applications {
    param(
        [array]$SourceApps,
        [array]$TargetApps,
        [string]$SourcePCName,
        [string]$TargetPCName
    )
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "APPLICATION COMPARISON RESULTS" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    # Find applications missing on target PC
    $missingOnTarget = @()
    foreach ($sourceApp in $SourceApps) {
        $found = $false
        foreach ($targetApp in $TargetApps) {
            if ($sourceApp.DisplayName -eq $targetApp.DisplayName) {
                $found = $true
                break
            }
        }
        if (-not $found) {
            $missingOnTarget += $sourceApp
        }
    }
    
    # Find applications only on target PC
    $extraOnTarget = @()
    foreach ($targetApp in $TargetApps) {
        $found = $false
        foreach ($sourceApp in $SourceApps) {
            if ($targetApp.DisplayName -eq $sourceApp.DisplayName) {
                $found = $true
                break
            }
        }
        if (-not $found) {
            $extraOnTarget += $targetApp
        }
    }
    
    Write-Host "`nApplications missing on $TargetPCName (present on $SourcePCName):" -ForegroundColor Red
    Write-Host "Count: $($missingOnTarget.Count)" -ForegroundColor Yellow
    if ($missingOnTarget.Count -gt 0) {
        $missingOnTarget | Sort-Object DisplayName | ForEach-Object {
            Write-Host "  - $($_.DisplayName) ($($_.DisplayVersion))" -ForegroundColor White
        }
    } else {
        Write-Host "  No missing applications found" -ForegroundColor Green
    }
    
    Write-Host "`nApplications only on $TargetPCName (not on $SourcePCName):" -ForegroundColor Blue
    Write-Host "Count: $($extraOnTarget.Count)" -ForegroundColor Yellow
    if ($extraOnTarget.Count -gt 0) {
        $extraOnTarget | Sort-Object DisplayName | ForEach-Object {
            Write-Host "  + $($_.DisplayName) ($($_.DisplayVersion))" -ForegroundColor White
        }
    } else {
        Write-Host "  No extra applications found" -ForegroundColor Green
    }
    
    return @{
        MissingOnTarget = $missingOnTarget
        ExtraOnTarget = $extraOnTarget
    }
}

# Function to compare drivers
function Compare-Drivers {
    param(
        [array]$SourceDrivers,
        [array]$TargetDrivers,
        [string]$SourcePCName,
        [string]$TargetPCName
    )
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "DRIVER COMPARISON RESULTS" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    # Find drivers missing on target PC
    $missingDrivers = @()
    foreach ($sourceDriver in $SourceDrivers) {
        $found = $false
        foreach ($targetDriver in $TargetDrivers) {
            if ($sourceDriver.DeviceName -eq $targetDriver.DeviceName) {
                $found = $true
                break
            }
        }
        if (-not $found) {
            $missingDrivers += $sourceDriver
        }
    }
    
    # Find drivers only on target PC
    $extraDrivers = @()
    foreach ($targetDriver in $TargetDrivers) {
        $found = $false
        foreach ($sourceDriver in $SourceDrivers) {
            if ($targetDriver.DeviceName -eq $sourceDriver.DeviceName) {
                $found = $true
                break
            }
        }
        if (-not $found) {
            $extraDrivers += $targetDriver
        }
    }
    
    Write-Host "`nDrivers missing on $TargetPCName (present on $SourcePCName):" -ForegroundColor Red
    Write-Host "Count: $($missingDrivers.Count)" -ForegroundColor Yellow
    if ($missingDrivers.Count -gt 0) {
        $missingDrivers | Sort-Object DeviceName | ForEach-Object {
            Write-Host "  - $($_.DeviceName) (v$($_.DriverVersion))" -ForegroundColor White
        }
    } else {
        Write-Host "  No missing drivers found" -ForegroundColor Green
    }
    
    Write-Host "`nDrivers only on $TargetPCName (not on $SourcePCName):" -ForegroundColor Blue
    Write-Host "Count: $($extraDrivers.Count)" -ForegroundColor Yellow
    if ($extraDrivers.Count -gt 0) {
        $extraDrivers | Sort-Object DeviceName | ForEach-Object {
            Write-Host "  + $($_.DeviceName) (v$($_.DriverVersion))" -ForegroundColor White
        }
    } else {
        Write-Host "  No extra drivers found" -ForegroundColor Green
    }
    
    return @{
        MissingOnTarget = $missingDrivers
        ExtraOnTarget = $extraDrivers
    }
}

# Main script execution
Show-Banner

# Get PC names if not provided as parameters
if (-not $SourcePC) {
    Write-Host "Options:" -ForegroundColor Cyan
    Write-Host "  - Enter computer name (e.g., PC001)" -ForegroundColor White
    Write-Host "  - Enter 'localhost' or '.' for this computer" -ForegroundColor White
    $SourcePC = Read-Host "Enter the source PC name (reference PC)"
}

if (-not $TargetPC) {
    Write-Host "Options:" -ForegroundColor Cyan
    Write-Host "  - Enter computer name (e.g., PC002)" -ForegroundColor White
    Write-Host "  - Enter 'localhost' or '.' for this computer" -ForegroundColor White
    $TargetPC = Read-Host "Enter the target PC name (PC to compare against)"
}

# Get credentials if not provided and we're accessing remote computers
$needsCredentials = ($SourcePC -ne $env:COMPUTERNAME -and $SourcePC -ne "localhost" -and $SourcePC -ne ".") -or
                   ($TargetPC -ne $env:COMPUTERNAME -and $TargetPC -ne "localhost" -and $TargetPC -ne ".")

if ($needsCredentials -and -not $Credential) {
    Write-Host "`nRemote computer access detected. Credentials required." -ForegroundColor Yellow
    Write-Host "Please enter credentials with administrative access to the remote computers:" -ForegroundColor Cyan
    $Credential = Get-Credential -Message "Enter credentials for remote computer access"
    if (-not $Credential) {
        Write-Host "Credentials are required for remote access. Exiting." -ForegroundColor Red
        exit 1
    }
}

Write-Host "`nStarting comparison between $SourcePC and $TargetPC..." -ForegroundColor Cyan

# Test connectivity to both PCs
$sourceOnline = Test-PCConnectivity -ComputerName $SourcePC
$targetOnline = Test-PCConnectivity -ComputerName $TargetPC

if (-not $sourceOnline -or -not $targetOnline) {
    Write-Host "`nCannot proceed - one or both PCs are offline" -ForegroundColor Red
    Write-Host "`nTroubleshooting suggestions:" -ForegroundColor Yellow
    Write-Host "1. Verify computer names are correct" -ForegroundColor White
    Write-Host "2. Check network connectivity" -ForegroundColor White
    Write-Host "3. Ensure computers are powered on" -ForegroundColor White
    Write-Host "4. Try using IP addresses instead of computer names" -ForegroundColor White
    Read-Host "Press Enter to exit"
    exit 1
}

# Additional connectivity checks for remote access
Write-Host "`nPerforming additional connectivity checks..." -ForegroundColor Cyan

foreach ($pc in @($SourcePC, $TargetPC)) {
    if ($pc -ne $env:COMPUTERNAME -and $pc -ne "localhost" -and $pc -ne ".") {
        Write-Host "`nChecking remote access capabilities for $pc..." -ForegroundColor Yellow

        # Skip Remote Registry test as it requires additional permissions
        Write-Host "  Remote Registry: SKIPPED (requires additional permissions)" -ForegroundColor Yellow

        # Test WMI
        try {
            if ($Credential) {
                Get-WmiObject -Class Win32_OperatingSystem -ComputerName $pc -Credential $Credential -ErrorAction Stop | Out-Null
            } else {
                Get-WmiObject -Class Win32_OperatingSystem -ComputerName $pc -ErrorAction Stop | Out-Null
            }
            Write-Host "  WMI Service: ACCESSIBLE" -ForegroundColor Green
        } catch {
            Write-Host "  WMI Service: NOT ACCESSIBLE - $($_.Exception.Message)" -ForegroundColor Red
        }

        # Test and Enable WinRM if needed
        if (Test-PSRemotingStatus -ComputerName $pc) {
            Write-Host "  WinRM/PowerShell Remoting: ACCESSIBLE" -ForegroundColor Green
        } else {
            Write-Host "  WinRM/PowerShell Remoting: NOT ACCESSIBLE - Attempting to enable..." -ForegroundColor Yellow
            try {
                Enable-PSRemoting -ComputerName $pc -Credential $Credential
                if (Test-PSRemotingStatus -ComputerName $pc) {
                    Write-Host "  WinRM/PowerShell Remoting: ENABLED SUCCESSFULLY" -ForegroundColor Green
                } else {
                    Write-Host "  WinRM/PowerShell Remoting: FAILED TO ENABLE" -ForegroundColor Red
                }
            } catch {
                Write-Host "  WinRM/PowerShell Remoting: FAILED TO ENABLE - $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

# Gather data from both PCs
Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "GATHERING DATA" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

$sourceApps = Get-InstalledApplications -ComputerName $SourcePC -Credential $Credential
$targetApps = Get-InstalledApplications -ComputerName $TargetPC -Credential $Credential
$sourceDrivers = Get-InstalledDrivers -ComputerName $SourcePC -Credential $Credential
$targetDrivers = Get-InstalledDrivers -ComputerName $TargetPC -Credential $Credential

# Perform comparisons
$appComparison = Compare-Applications -SourceApps $sourceApps -TargetApps $targetApps -SourcePCName $SourcePC -TargetPCName $TargetPC
$driverComparison = Compare-Drivers -SourceDrivers $sourceDrivers -TargetDrivers $targetDrivers -SourcePCName $SourcePC -TargetPCName $TargetPC

# Save report if requested
if ($SaveReport) {
    $reportPath = "PC_Comparison_Report_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
    $report = @"
PC Component Comparison Report
Generated: $(Get-Date)
Source PC: $SourcePC
Target PC: $TargetPC

APPLICATIONS MISSING ON $TargetPC`:
$($appComparison.MissingOnTarget | ForEach-Object { "- $($_.DisplayName) ($($_.DisplayVersion))" } | Out-String)

APPLICATIONS ONLY ON $TargetPC`:
$($appComparison.ExtraOnTarget | ForEach-Object { "+ $($_.DisplayName) ($($_.DisplayVersion))" } | Out-String)

DRIVERS MISSING ON $TargetPC`:
$($driverComparison.MissingOnTarget | ForEach-Object { "- $($_.DeviceName) (v$($_.DriverVersion))" } | Out-String)

DRIVERS ONLY ON $TargetPC`:
$($driverComparison.ExtraOnTarget | ForEach-Object { "+ $($_.DeviceName) (v$($_.DriverVersion))" } | Out-String)
"@
    
    $report | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Host "`nReport saved to: $reportPath" -ForegroundColor Green
}

Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "COMPARISON COMPLETE" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

# Offer to handle missing components
$totalMissing = $appComparison.MissingOnTarget.Count + $driverComparison.MissingOnTarget.Count

if ($totalMissing -gt 0) {
    Write-Host "`nFound $totalMissing missing components on $TargetPC (Copy TO PC)" -ForegroundColor Yellow
    Write-Host "  Missing Applications: $($appComparison.MissingOnTarget.Count)" -ForegroundColor Red
    Write-Host "  Missing Drivers: $($driverComparison.MissingOnTarget.Count)" -ForegroundColor Red

    Write-Host "`nWhat would you like to do?" -ForegroundColor Cyan
    Write-Host "Options:" -ForegroundColor White
    Write-Host "  1. Export and transfer missing drivers from Copy FROM PC to Copy TO PC" -ForegroundColor White
    Write-Host "  2. Install missing applications via package managers" -ForegroundColor White
    Write-Host "  3. Do both drivers and applications" -ForegroundColor White
    Write-Host "  4. Skip installation" -ForegroundColor White

    $choice = Read-Host "Enter your choice (1-4)"

    switch ($choice) {
        "1" {
            Write-Host "`nStarting driver export and transfer process..." -ForegroundColor Green
            Start-DriverTransferProcess -SourcePC $SourcePC -TargetPC $TargetPC -Credential $Credential -MissingDrivers $driverComparison.MissingOnTarget
        }
        "2" {
            Write-Host "`nStarting application installation..." -ForegroundColor Green
            if (Test-Path "$PSScriptRoot\Install-MissingComponents.ps1") {
                & "$PSScriptRoot\Install-MissingComponents.ps1" -TargetPC $TargetPC -Credential $Credential -MissingApps $appComparison.MissingOnTarget -MissingDrivers @()
            } else {
                Write-Host "Install-MissingComponents.ps1 not found in script directory" -ForegroundColor Red
            }
        }
        "3" {
            Write-Host "`nStarting comprehensive installation..." -ForegroundColor Green
            Start-DriverTransferProcess -SourcePC $SourcePC -TargetPC $TargetPC -Credential $Credential -MissingDrivers $driverComparison.MissingOnTarget
            if (Test-Path "$PSScriptRoot\Install-MissingComponents.ps1") {
                & "$PSScriptRoot\Install-MissingComponents.ps1" -TargetPC $TargetPC -Credential $Credential -MissingApps $appComparison.MissingOnTarget -MissingDrivers @()
            }
        }
        "4" {
            Write-Host "`nSkipping installation." -ForegroundColor Yellow
        }
        default {
            Write-Host "`nInvalid choice. Skipping installation." -ForegroundColor Red
        }
    }
} else {
    Write-Host "`nNo missing components found. Both computers are synchronized!" -ForegroundColor Green
}

Read-Host "`nPress Enter to exit"
