# TWAIN Driver Diagnostic Script
# Specifically checks for Fujitsu and other TWAIN drivers
# Developed by: The greatest technician that ever lived

param(
    [string]$ComputerName = "MGBR366PCP03D",
    [PSCredential]$Credential
)

# Function to display banner
function Show-TWAINBanner {
    Clear-Host
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "    TWAIN Driver Diagnostic Tool" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "Developed by: The greatest technician that ever lived" -ForegroundColor Green
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host ""
}

# Function to check all drivers (no filtering)
function Get-AllDrivers {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )
    
    Write-Host "Getting ALL drivers from $ComputerName..." -ForegroundColor Yellow
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            $drivers = Get-WmiObject -Class Win32_PnPSignedDriver -ErrorAction SilentlyContinue |
                Where-Object { $_.DeviceName -and $_.DriverVersion } |
                Select-Object DeviceName, DriverVersion, DriverDate, Manufacturer, HardwareID
        } else {
            if ($Credential) {
                $drivers = Get-WmiObject -Class Win32_PnPSignedDriver -ComputerName $ComputerName -Credential $Credential -ErrorAction Stop |
                    Where-Object { $_.DeviceName -and $_.DriverVersion } |
                    Select-Object DeviceName, DriverVersion, DriverDate, Manufacturer, HardwareID
            } else {
                $drivers = Get-WmiObject -Class Win32_PnPSignedDriver -ComputerName $ComputerName -ErrorAction Stop |
                    Where-Object { $_.DeviceName -and $_.DriverVersion } |
                    Select-Object DeviceName, DriverVersion, DriverDate, Manufacturer, HardwareID
            }
        }
        
        Write-Host "Found $($drivers.Count) total drivers" -ForegroundColor Green
        return $drivers
        
    } catch {
        Write-Host "Error getting drivers: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

# Function to search for TWAIN-related drivers
function Find-TWAINDrivers {
    param([array]$AllDrivers)
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "SEARCHING FOR TWAIN DRIVERS" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    $twainDrivers = @()
    
    # Search criteria for TWAIN drivers
    foreach ($driver in $AllDrivers) {
        $isTWAIN = $false
        $reason = ""
        
        # Check device name
        if ($driver.DeviceName -like "*TWAIN*") {
            $isTWAIN = $true
            $reason = "DeviceName contains TWAIN"
        }
        elseif ($driver.DeviceName -like "*Scanner*") {
            $isTWAIN = $true
            $reason = "DeviceName contains Scanner"
        }
        elseif ($driver.DeviceName -like "*Imaging*") {
            $isTWAIN = $true
            $reason = "DeviceName contains Imaging"
        }
        elseif ($driver.DeviceName -like "*Document*") {
            $isTWAIN = $true
            $reason = "DeviceName contains Document"
        }
        
        # Check manufacturer
        if ($driver.Manufacturer -like "*Fujitsu*") {
            $isTWAIN = $true
            $reason += " + Fujitsu manufacturer"
        }
        elseif ($driver.Manufacturer -like "*Canon*") {
            $isTWAIN = $true
            $reason += " + Canon manufacturer"
        }
        elseif ($driver.Manufacturer -like "*Epson*") {
            $isTWAIN = $true
            $reason += " + Epson manufacturer"
        }
        elseif ($driver.Manufacturer -like "*HP*" -and $driver.DeviceName -like "*Scan*") {
            $isTWAIN = $true
            $reason += " + HP Scanner"
        }
        
        if ($isTWAIN) {
            $twainDrivers += [PSCustomObject]@{
                DeviceName = $driver.DeviceName
                Manufacturer = $driver.Manufacturer
                DriverVersion = $driver.DriverVersion
                DriverDate = $driver.DriverDate
                HardwareID = $driver.HardwareID
                MatchReason = $reason.Trim(" + ")
            }
        }
    }
    
    return $twainDrivers
}

# Function to search for Fujitsu-specific drivers
function Find-FujitsuDrivers {
    param([array]$AllDrivers)
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "SEARCHING FOR FUJITSU DRIVERS" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    $fujitsuDrivers = $AllDrivers | Where-Object { 
        $_.Manufacturer -like "*Fujitsu*" -or 
        $_.DeviceName -like "*Fujitsu*" -or
        $_.DeviceName -like "*fi-*" -or
        $_.DeviceName -like "*ScanSnap*"
    }
    
    return $fujitsuDrivers
}

# Function to check TWAIN registry entries
function Check-TWAINRegistry {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "CHECKING TWAIN REGISTRY ENTRIES" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    try {
        $scriptBlock = {
            $twainEntries = @()
            
            # Check common TWAIN registry locations
            $twainPaths = @(
                "HKLM:\SOFTWARE\TWAIN_32",
                "HKLM:\SOFTWARE\WOW6432Node\TWAIN_32",
                "HKLM:\SOFTWARE\TWAIN",
                "HKLM:\SOFTWARE\WOW6432Node\TWAIN"
            )
            
            foreach ($path in $twainPaths) {
                if (Test-Path $path) {
                    $subKeys = Get-ChildItem -Path $path -ErrorAction SilentlyContinue
                    foreach ($subKey in $subKeys) {
                        $twainEntries += [PSCustomObject]@{
                            Path = $path
                            Name = $subKey.PSChildName
                            FullPath = $subKey.Name
                        }
                    }
                }
            }
            
            return $twainEntries
        }
        
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            $twainEntries = & $scriptBlock
        } else {
            if ($Credential) {
                $twainEntries = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock
            } else {
                $twainEntries = Invoke-Command -ComputerName $ComputerName -ScriptBlock $scriptBlock
            }
        }
        
        return $twainEntries
        
    } catch {
        Write-Host "Error checking TWAIN registry: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

# Function to check installed programs for TWAIN software
function Check-TWAINPrograms {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "CHECKING INSTALLED TWAIN PROGRAMS" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    try {
        $scriptBlock = {
            $programs = @()
            
            # Check 64-bit programs
            $programs += Get-ItemProperty "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
                Where-Object { 
                    $_.DisplayName -and (
                        $_.DisplayName -like "*TWAIN*" -or
                        $_.DisplayName -like "*Scanner*" -or
                        $_.DisplayName -like "*Fujitsu*" -or
                        $_.DisplayName -like "*ScanSnap*" -or
                        $_.DisplayName -like "*fi-*" -or
                        $_.DisplayName -like "*Imaging*" -or
                        $_.Publisher -like "*Fujitsu*"
                    )
                } |
                Select-Object DisplayName, DisplayVersion, Publisher
            
            # Check 32-bit programs
            $programs += Get-ItemProperty "HKLM:\Software\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
                Where-Object { 
                    $_.DisplayName -and (
                        $_.DisplayName -like "*TWAIN*" -or
                        $_.DisplayName -like "*Scanner*" -or
                        $_.DisplayName -like "*Fujitsu*" -or
                        $_.DisplayName -like "*ScanSnap*" -or
                        $_.DisplayName -like "*fi-*" -or
                        $_.DisplayName -like "*Imaging*" -or
                        $_.Publisher -like "*Fujitsu*"
                    )
                } |
                Select-Object DisplayName, DisplayVersion, Publisher
            
            return $programs | Sort-Object DisplayName -Unique
        }
        
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            $programs = & $scriptBlock
        } else {
            if ($Credential) {
                $programs = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock
            } else {
                $programs = Invoke-Command -ComputerName $ComputerName -ScriptBlock $scriptBlock
            }
        }
        
        return $programs
        
    } catch {
        Write-Host "Error checking TWAIN programs: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

# Main execution
Show-TWAINBanner

if (-not $Credential -and $ComputerName -ne $env:COMPUTERNAME -and $ComputerName -ne "localhost" -and $ComputerName -ne ".") {
    Write-Host "Remote computer access detected. Credentials required." -ForegroundColor Yellow
    $Credential = Get-Credential -Message "Enter credentials for remote computer access"
}

Write-Host "Target PC: $ComputerName" -ForegroundColor Cyan
Write-Host "Checking for Fujitsu TWAIN drivers..." -ForegroundColor Cyan

# Test connectivity
Write-Host "`nTesting connectivity..." -ForegroundColor Yellow
if (Test-Connection -ComputerName $ComputerName -Count 2 -Quiet) {
    Write-Host "[SUCCESS] $ComputerName is online" -ForegroundColor Green
} else {
    Write-Host "[ERROR] $ComputerName is offline or unreachable" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Get all drivers
$allDrivers = Get-AllDrivers -ComputerName $ComputerName -Credential $Credential

if ($allDrivers.Count -eq 0) {
    Write-Host "No drivers found. Check permissions or WMI access." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Search for TWAIN drivers
$twainDrivers = Find-TWAINDrivers -AllDrivers $allDrivers

Write-Host "`nTWAIN-related drivers found: $($twainDrivers.Count)" -ForegroundColor Cyan
if ($twainDrivers.Count -gt 0) {
    $twainDrivers | ForEach-Object {
        Write-Host "`nDevice: $($_.DeviceName)" -ForegroundColor White
        Write-Host "  Manufacturer: $($_.Manufacturer)" -ForegroundColor Gray
        Write-Host "  Version: $($_.DriverVersion)" -ForegroundColor Gray
        Write-Host "  Date: $($_.DriverDate)" -ForegroundColor Gray
        Write-Host "  Match Reason: $($_.MatchReason)" -ForegroundColor Yellow
        if ($_.HardwareID) {
            Write-Host "  Hardware ID: $($_.HardwareID)" -ForegroundColor Gray
        }
    }
} else {
    Write-Host "No TWAIN drivers found in driver list" -ForegroundColor Red
}

# Search specifically for Fujitsu drivers
$fujitsuDrivers = Find-FujitsuDrivers -AllDrivers $allDrivers

Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "FUJITSU DRIVERS FOUND: $($fujitsuDrivers.Count)" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

if ($fujitsuDrivers.Count -gt 0) {
    $fujitsuDrivers | ForEach-Object {
        Write-Host "`nDevice: $($_.DeviceName)" -ForegroundColor White
        Write-Host "  Manufacturer: $($_.Manufacturer)" -ForegroundColor Gray
        Write-Host "  Version: $($_.DriverVersion)" -ForegroundColor Gray
        Write-Host "  Date: $($_.DriverDate)" -ForegroundColor Gray
        if ($_.HardwareID) {
            Write-Host "  Hardware ID: $($_.HardwareID)" -ForegroundColor Gray
        }
    }
} else {
    Write-Host "No Fujitsu drivers found in driver list" -ForegroundColor Red
}

# Check TWAIN registry entries
$twainRegistry = Check-TWAINRegistry -ComputerName $ComputerName -Credential $Credential

Write-Host "`nTWAIN registry entries found: $($twainRegistry.Count)" -ForegroundColor Cyan
if ($twainRegistry.Count -gt 0) {
    $twainRegistry | ForEach-Object {
        Write-Host "  $($_.Path)\$($_.Name)" -ForegroundColor White
    }
} else {
    Write-Host "No TWAIN registry entries found" -ForegroundColor Red
}

# Check installed TWAIN programs
$twainPrograms = Check-TWAINPrograms -ComputerName $ComputerName -Credential $Credential

Write-Host "`nTWAIN-related programs found: $($twainPrograms.Count)" -ForegroundColor Cyan
if ($twainPrograms.Count -gt 0) {
    $twainPrograms | ForEach-Object {
        Write-Host "`nProgram: $($_.DisplayName)" -ForegroundColor White
        Write-Host "  Version: $($_.DisplayVersion)" -ForegroundColor Gray
        Write-Host "  Publisher: $($_.Publisher)" -ForegroundColor Gray
    }
} else {
    Write-Host "No TWAIN programs found" -ForegroundColor Red
}

Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "DIAGNOSTIC SUMMARY" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

Write-Host "Total drivers found: $($allDrivers.Count)" -ForegroundColor White
Write-Host "TWAIN drivers found: $($twainDrivers.Count)" -ForegroundColor White
Write-Host "Fujitsu drivers found: $($fujitsuDrivers.Count)" -ForegroundColor White
Write-Host "TWAIN registry entries: $($twainRegistry.Count)" -ForegroundColor White
Write-Host "TWAIN programs installed: $($twainPrograms.Count)" -ForegroundColor White

if ($fujitsuDrivers.Count -eq 0 -and $twainDrivers.Count -eq 0 -and $twainPrograms.Count -eq 0) {
    Write-Host "`nCONCLUSION: No Fujitsu TWAIN drivers detected on this PC" -ForegroundColor Red
    Write-Host "Possible reasons:" -ForegroundColor Yellow
    Write-Host "  1. Fujitsu scanner software not installed" -ForegroundColor White
    Write-Host "  2. Scanner not connected/detected" -ForegroundColor White
    Write-Host "  3. Drivers installed but not showing in WMI" -ForegroundColor White
    Write-Host "  4. Different scanner manufacturer" -ForegroundColor White
} else {
    Write-Host "`nCONCLUSION: TWAIN/Fujitsu components detected!" -ForegroundColor Green
}

Read-Host "`nPress Enter to exit"
