# Printer Migration Script
# Standalone script for migrating printers between computers
# Developed by: The greatest technician that ever lived

# Global variables
$script:storedCredentials = $null
$script:targetSession = $null

function Write-Color {
    param([string]$Message, [string]$Color = "Gray", [switch]$NoNewline)
    if ($NoNewline) { Write-Host $Message -ForegroundColor $Color -NoNewline }
    else { Write-Host $Message -ForegroundColor $Color }
}

function Show-Banner {
    param([string]$Title, [string]$Color = "Cyan")
    $width = 80
    $padding = [math]::Max(0, ($width - $Title.Length - 4) / 2)
    $leftPad = [math]::Floor($padding)
    $rightPad = [math]::Ceiling($padding)
    Write-Host ""
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ("*" + " " * ($width - 2) + "*") -ForegroundColor $Color
    Write-Host ("*" + " " * $leftPad + $Title + " " * $rightPad + "*") -ForegroundColor $Color
    Write-Host ("*" + " " * ($width - 2) + "*") -ForegroundColor $Color
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ""
}

function Initialize-Credentials {
    Write-Color "Initializing credentials for printer migration..." "Cyan"
    try {
        $script:storedCredentials = Get-Credential -Message "Enter domain credentials for printer migration" -UserName "mclaren\"
        if ($script:storedCredentials) {
            Write-Color "Credentials stored successfully" "Green"
            return $true
        }
        Write-Color "No credentials provided" "Red"
        return $false
    }
    catch {
        Write-Color "Error storing credentials: $_" "Red"
        return $false
    }
}

function Test-PCOnlineAndDNS {
    param([string]$computerName)
    try {
        Test-Connection -ComputerName $computerName -Count 1 -ErrorAction Stop | Out-Null
        $dnsStatus = if ($computerName -match '^\d+\.\d+\.\d+\.\d+$') { "IP Address" } else { "DNS Resolved" }
        return @{ Online = $true; DNSStatus = $dnsStatus; Error = $null }
    }
    catch {
        return @{ Online = $false; DNSStatus = "Failed"; Error = $_.Exception.Message }
    }
}

function Initialize-PSRemoting {
    param([string]$TargetComputer)

    Write-Color "Initializing PSRemoting connection to $TargetComputer..." "Cyan"

    # First, validate credentials by testing basic connectivity
    Write-Color "Validating credentials against target computer..." "Yellow"
    try {
        $testResult = Get-WmiObject -Class Win32_ComputerSystem -ComputerName $TargetComputer -Credential $script:storedCredentials -ErrorAction Stop
        Write-Color "Credential validation successful" "Green"
    }
    catch {
        Write-Color "Credential validation failed: $_" "Red"
        return $false
    }

    # Check WinRM service
    try {
        $winrm = Get-WmiObject -Class Win32_Service -ComputerName $TargetComputer -Credential $script:storedCredentials | Where-Object { $_.Name -eq 'WinRM' }
        if ($winrm.Status -ne "Running") {
            Write-Color "Attempting to enable PSRemoting..." "Yellow"
            $SessionArgs = @{
                ComputerName = $TargetComputer
                Credential = $script:storedCredentials
                SessionOption = New-CimSessionOption -Protocol Dcom
            }
            try {
                $cimSession = New-CimSession @SessionArgs
                $MethodArgsEnablePS = @{
                    ClassName = 'Win32_Process'
                    MethodName = 'Create'
                    CimSession = $cimSession
                    Arguments = @{ CommandLine = 'powershell -Command "Enable-PSRemoting -Force"' }
                }
                Invoke-CimMethod @MethodArgsEnablePS | Out-Null
                Remove-CimSession $cimSession
                Start-Sleep -Seconds 10
            }
            catch {
                Write-Color "Warning: Could not enable PSRemoting via CIM - $_" "Yellow"
            }
        }
    }
    catch {
        Write-Color "Warning: Could not check WinRM service status - $_" "Yellow"
    }

    # Create PSSession
    Write-Color "Creating PSSession..." "Yellow"
    try {
        $sessionOption = New-PSSessionOption -IdleTimeout 3600000 -OpenTimeout 60000 -OperationTimeout 300000
        $script:targetSession = New-PSSession -ComputerName $TargetComputer -Credential $script:storedCredentials -SessionOption $sessionOption -ErrorAction Stop
        Write-Color "PSSession established successfully!" "Green"
        return $true
    }
    catch {
        Write-Color "PSSession creation failed: $_" "Red"
        return $false
    }
}

function Invoke-RemoteCommand {
    param(
        [scriptblock]$ScriptBlock,
        [object[]]$ArgumentList = @(),
        [string]$TargetComputer,
        [int]$MaxRetries = 2,
        [switch]$UseFallback
    )

    $attempt = 0
    while ($attempt -le $MaxRetries) {
        $attempt++

        # Test session
        if ($null -eq $script:targetSession -or $script:targetSession.State -ne 'Opened') {
            if (-not [string]::IsNullOrEmpty($TargetComputer)) {
                Initialize-PSRemoting -TargetComputer $TargetComputer | Out-Null
            }
        }

        try {
            if ($script:targetSession -and $script:targetSession.State -eq 'Opened') {
                if ($ArgumentList.Count -gt 0) {
                    return Invoke-Command -Session $script:targetSession -ScriptBlock $ScriptBlock -ArgumentList $ArgumentList -ErrorAction Stop
                } else {
                    return Invoke-Command -Session $script:targetSession -ScriptBlock $ScriptBlock -ErrorAction Stop
                }
            }
        }
        catch {
            Write-Color "Remote command failed (attempt $attempt): $_" "Yellow"
            if ($attempt -le $MaxRetries) {
                Start-Sleep -Seconds 5
                $script:targetSession = $null
            }
        }
    }

    # Fallback to direct Invoke-Command
    if ($UseFallback) {
        Write-Color "Using fallback method (direct Invoke-Command)..." "Cyan"
        try {
            if ($ArgumentList.Count -gt 0) {
                return Invoke-Command -ComputerName $TargetComputer -Credential $script:storedCredentials -ScriptBlock $ScriptBlock -ArgumentList $ArgumentList -ErrorAction Stop
            } else {
                return Invoke-Command -ComputerName $TargetComputer -Credential $script:storedCredentials -ScriptBlock $ScriptBlock -ErrorAction Stop
            }
        }
        catch {
            Write-Color "Fallback method also failed: $_" "Red"
            return "Error: Both PSSession and fallback methods failed"
        }
    }

    return "Error: Remote command failed after all attempts"
}

function Install-LexmarkDriver {
    param([string]$TargetComputer)

    Write-Color "Installing Lexmark Universal Driver..." "Cyan"
    
    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        try {
            # Check if Lexmark Universal driver is already installed
            $existingDriver = Get-PrinterDriver -Name "Lexmark Universal v2 PostScript 3 Emulation" -ErrorAction SilentlyContinue
            if ($existingDriver) {
                Write-Host "Lexmark Universal driver is already installed" -ForegroundColor Yellow
                return "Success: Lexmark Universal driver already available"
            }

            # Try to add the driver (assumes it's available in Windows driver store)
            Add-PrinterDriver -Name "Lexmark Universal v2 PostScript 3 Emulation" -ErrorAction Stop
            Write-Host "Lexmark Universal driver installed successfully" -ForegroundColor Green
            return "Success: Lexmark Universal driver installed"
        }
        catch {
            Write-Host "Could not install Lexmark Universal driver: $_" -ForegroundColor Yellow
            return "Warning: Lexmark driver installation failed - $_"
        }
    }

    Write-Color $result "Gray"
}

function Install-SpecificZebraDriver {
    param([string]$TargetComputer, [string]$DriverName)

    Write-Color "Attempting to install Zebra driver: $DriverName" "Yellow"

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($RequestedDriverName)

        try {
            # Try to add the specific driver
            Add-PrinterDriver -Name $RequestedDriverName -ErrorAction Stop
            Write-Host "Successfully installed Zebra driver: $RequestedDriverName" -ForegroundColor Green
            return "Success: $RequestedDriverName driver installed"
        }
        catch {
            # Try some common Zebra driver alternatives
            $commonZebraDrivers = @(
                "ZDesigner Generic Text Driver",
                "ZDesigner GK420d (EPL)",
                "ZDesigner ZT220 (ZPL)",
                "Generic / Text Only"
            )

            foreach ($genericDriver in $commonZebraDrivers) {
                try {
                    Add-PrinterDriver -Name $genericDriver -ErrorAction Stop
                    Write-Host "Installed alternative driver: $genericDriver" -ForegroundColor Green
                    return "Success: Installed $genericDriver (alternative for $RequestedDriverName)"
                }
                catch {
                    continue
                }
            }

            return "Warning: Could not install $RequestedDriverName or find suitable alternative"
        }
    } -ArgumentList $DriverName

    Write-Color $result "Gray"
    return $result
}

function Start-PrinterMigration {
    param([string]$SourceComputer, [string]$TargetComputer)

    Write-Color "Starting printer migration from $SourceComputer to $TargetComputer..." "Cyan"

    # Validate parameters
    if ([string]::IsNullOrEmpty($SourceComputer) -or [string]::IsNullOrEmpty($TargetComputer)) {
        Write-Color "ERROR: Missing source or target computer identifiers" "Red"
        return
    }

    $sourcePC = $SourceComputer.Trim()
    $targetPC = $TargetComputer.Trim()

    try {
        # Step 1: Verify connectivity
        Write-Color "`n[1/6] Verifying network connectivity..." "Cyan"
        try {
            Test-Connection $sourcePC -Count 1 -ErrorAction Stop | Out-Null
            Write-Color "  Source computer ($sourcePC) is reachable" "Green"
        }
        catch {
            Write-Color "  Source computer ($sourcePC) is not reachable: $($_.Exception.Message)" "Red"
            throw "Source computer connectivity failed"
        }

        try {
            Test-Connection $targetPC -Count 1 -ErrorAction Stop | Out-Null
            Write-Color "  Target computer ($targetPC) is reachable" "Green"
        }
        catch {
            Write-Color "  Target computer ($targetPC) is not reachable: $($_.Exception.Message)" "Red"
            throw "Target computer connectivity failed"
        }

        # Step 2: Initialize PSRemoting to target
        Write-Color "`n[2/6] Establishing connection to target computer..." "Cyan"
        if (-not (Initialize-PSRemoting -TargetComputer $targetPC)) {
            Write-Color "Failed to establish connection to target computer" "Red"
            throw "PSRemoting initialization failed"
        }

        # Step 3: Install Lexmark Universal Driver
        Write-Color "`n[3/6] Installing Lexmark Universal Driver on target..." "Cyan"
        Install-LexmarkDriver -TargetComputer $targetPC

        # Step 4: Get source printers
        Write-Color "`n[4/6] Retrieving printers from source computer..." "Cyan"
        $ExcludeList = "XPS|Fax|PDF|OneNote|MSFT|CutePDF|Send to Microsoft OneNote|Microsoft Print To PDF|Microsoft XPS Document Writer|Snagit|WebEx Document Loader|WebEx"

        $printers = Invoke-Command -ComputerName $sourcePC -Credential $script:storedCredentials -ScriptBlock {
            param($ExcludeList)
            try {
                $foundPrinters = Get-Printer | Where-Object {
                    $_.Name -notmatch $ExcludeList -and
                    $_.Type -notmatch 'Text|PrintServer'
                } | Select-Object Name, DriverName, PortName, Shared, Published, Location

                Write-Host "Successfully retrieved $($foundPrinters.Count) printers from source" -ForegroundColor Green
                return $foundPrinters
            }
            catch {
                Write-Host "Error retrieving printers from source: $($_.Exception.Message)" -ForegroundColor Red
                throw
            }
        } -ArgumentList $ExcludeList

        if (-not $printers -or $printers.Count -eq 0) {
            Write-Color "  No printers found on source computer" "Yellow"
            return
        }

        Write-Color "  Found $($printers.Count) printers on source computer:" "Gray"
        $printers | ForEach-Object {
            Write-Color "    [Source] $($_.Name) | Driver: $($_.DriverName)" "DarkGray"
        }

        # Step 5: Get target drivers
        Write-Color "`n[5/6] Retrieving available drivers from target computer..." "Cyan"
        $targetDrivers = Invoke-RemoteCommand -TargetComputer $targetPC -UseFallback -ScriptBlock {
            try {
                $drivers = Get-PrinterDriver | Select-Object Name, @{
                    Name = 'Normalized'
                    Expression = {
                        ($_.Name -replace 'PS3','PostScript3' -replace '[^a-zA-Z0-9]','').ToLower()
                    }
                }

                Write-Host "Successfully retrieved $($drivers.Count) drivers from target" -ForegroundColor Green
                return $drivers
            }
            catch {
                Write-Host "Error retrieving drivers from target: $($_.Exception.Message)" -ForegroundColor Red
                throw
            }
        }

        if (-not $targetDrivers -or $targetDrivers.Count -eq 0) {
            Write-Color "  Warning: No drivers found on target computer" "Yellow"
            $targetDrivers = @()
        } else {
            Write-Color "  Found $($targetDrivers.Count) drivers on target computer" "Gray"
        }

        # Step 6: Migrate printers
        Write-Color "`n[6/6] Migrating printers..." "Cyan"
        $successCount = 0
        $failureCount = 0

        foreach ($printer in $printers) {
            $printerName = $printer.Name
            $wantedDriverName = $printer.DriverName
            $portName = $printer.PortName

            try {
                Write-Color "  Processing: $printerName" "Gray"

                # Enhanced normalization for driver matching
                $normalizedSourceDriver = ($wantedDriverName -replace 'PS3','PostScript3' -replace '[^a-zA-Z0-9]','').ToLower()

                # Find best driver match
                $matchedDriver = $targetDrivers | Where-Object {
                    ($_.Normalized -eq $normalizedSourceDriver) -or
                    ($_.Normalized -like "*$normalizedSourceDriver*" -and $_.Normalized -like "*lexmark*") -or
                    ($_.Normalized -replace 'postscript','ps' -eq $normalizedSourceDriver)
                } | Select-Object -First 1

                # Determine final driver name
                $finalDriverName = $null

                # Check if this is a Zebra printer
                $isZebraPrinter = ($printerName -like "*ZD*" -or $printerName -like "*ZT*" -or $printerName -like "*ZQ*" -or
                                  $printerName -like "*Zebra*" -or $wantedDriverName -like "*ZDesigner*")

                if ($matchedDriver -and -not $isZebraPrinter) {
                    $finalDriverName = $matchedDriver.Name
                    Write-Color "    Auto-matched driver: $finalDriverName" "Yellow"
                }
                elseif ($isZebraPrinter) {
                    Write-Color "    Zebra printer detected: $printerName" "Cyan"
                    $zebraResult = Install-SpecificZebraDriver -TargetComputer $targetPC -DriverName $wantedDriverName
                    if ($zebraResult -like "Success:*") {
                        $finalDriverName = $wantedDriverName
                        Write-Color "    Using installed Zebra driver: $finalDriverName" "Green"
                    } else {
                        Write-Color "    Using Generic/Text Only driver as fallback" "Yellow"
                        $finalDriverName = "Generic / Text Only"
                    }
                }
                else {
                    # Use Lexmark Universal as fallback for Lexmark printers
                    if ($wantedDriverName -like "*Lexmark*" -or $printerName -like "*Lexmark*") {
                        $finalDriverName = "Lexmark Universal v2 PostScript 3 Emulation"
                        Write-Color "    Using Lexmark Universal driver fallback" "Yellow"
                    }
                    else {
                        Write-Color "    Using Generic/Text Only driver as fallback" "Yellow"
                        $finalDriverName = "Generic / Text Only"
                    }
                }

                # Create printer with enhanced error handling
                $printerResult = Invoke-RemoteCommand -TargetComputer $targetPC -UseFallback -MaxRetries 3 -ScriptBlock {
                    param($printerName, $finalDriverName, $portName, $printer)

                    $results = @{
                        PortCreated = $false
                        PrinterCreated = $false
                        PropertiesSet = $false
                        Errors = @()
                    }

                    try {
                        # Step 1: Create port if missing
                        Write-Host "    Checking printer port: $portName" -ForegroundColor Gray
                        if (-not (Get-PrinterPort -Name $portName -ErrorAction SilentlyContinue)) {
                            try {
                                Add-PrinterPort -Name $portName -ErrorAction Stop | Out-Null
                                $results.PortCreated = $true
                                Write-Host "    Port created successfully: $portName" -ForegroundColor Green
                            }
                            catch {
                                $results.Errors += "Port creation failed: $($_.Exception.Message)"
                                Write-Host "    Port creation failed: $($_.Exception.Message)" -ForegroundColor Red
                                return $results
                            }
                        } else {
                            $results.PortCreated = $true
                            Write-Host "    Port already exists: $portName" -ForegroundColor Yellow
                        }

                        # Step 2: Create printer if missing
                        Write-Host "    Checking printer: $printerName" -ForegroundColor Gray
                        if (-not (Get-Printer -Name $printerName -ErrorAction SilentlyContinue)) {
                            try {
                                # Verify driver exists before creating printer
                                $driverExists = Get-PrinterDriver -Name $finalDriverName -ErrorAction SilentlyContinue
                                if (-not $driverExists) {
                                    $results.Errors += "Driver not found: $finalDriverName"
                                    Write-Host "    Driver not found: $finalDriverName" -ForegroundColor Red
                                    return $results
                                }

                                Add-Printer -Name $printerName -DriverName $finalDriverName -PortName $portName -ErrorAction Stop | Out-Null
                                $results.PrinterCreated = $true
                                Write-Host "    Printer created successfully: $printerName" -ForegroundColor Green
                            }
                            catch {
                                $results.Errors += "Printer creation failed: $($_.Exception.Message)"
                                Write-Host "    Printer creation failed: $($_.Exception.Message)" -ForegroundColor Red
                                return $results
                            }
                        } else {
                            $results.PrinterCreated = $true
                            Write-Host "    Printer already exists: $printerName" -ForegroundColor Yellow
                        }

                        # Step 3: Set printer properties
                        try {
                            Write-Host "    Setting printer properties..." -ForegroundColor Gray
                            Set-Printer -Name $printerName -Shared:$printer.Shared -ErrorAction Stop | Out-Null
                            $results.PropertiesSet = $true
                            Write-Host "    Properties set successfully" -ForegroundColor Green
                        }
                        catch {
                            $results.Errors += "Property setting failed: $($_.Exception.Message)"
                            Write-Host "    Warning: Could not set printer properties - $($_.Exception.Message)" -ForegroundColor Yellow
                        }

                        return $results
                    }
                    catch {
                        $results.Errors += "Unexpected error: $($_.Exception.Message)"
                        return $results
                    }
                } -ArgumentList $printerName, $finalDriverName, $portName, $printer

                # Process results
                if ($printerResult -and $printerResult.GetType().Name -eq "Hashtable") {
                    if ($printerResult.PrinterCreated) {
                        Write-Color "    Success: $printerName" "Green"
                        if ($printerResult.Errors.Count -gt 0) {
                            Write-Color "    Warnings: $($printerResult.Errors -join '; ')" "Yellow"
                        }
                        $successCount++
                    } else {
                        Write-Color "    [!] Failed: $printerName" "Red"
                        $printerResult.Errors | ForEach-Object {
                            Write-Color "      Error: $_" "Red"
                        }
                        $failureCount++
                    }
                } else {
                    Write-Color "    [!] Failed: $printerName - PSRemoting connection failed" "Red"
                    Write-Color "      Error: $printerResult" "Red"
                    $failureCount++
                }
            }
            catch {
                Write-Color "    [!] Failed: $printerName - $($_.Exception.Message)" "Red"
                $failureCount++
            }
        }

        # Results summary
        Write-Color "`n[Migration Complete]" "Cyan"
        Write-Color "  Attempted printers: $($printers.Count)" "Gray"
        Write-Color "  Successful: $successCount" "Green"
        Write-Color "  Failed: $failureCount" "Red"
    }
    catch {
        Write-Color "`n[!] Critical printer migration error: $($_.Exception.Message)" "Red"
        Write-Color "Please check connectivity and credentials, then try again." "Yellow"
    }
    finally {
        # Clean up PSSession
        if ($script:targetSession) {
            try {
                Remove-PSSession $script:targetSession -ErrorAction SilentlyContinue
                Write-Color "`nPSSession cleaned up successfully" "Gray"
            }
            catch { }
            $script:targetSession = $null
        }
    }
}

# Main script execution
function Start-MainScript {
    # Display banner
    Clear-Host
    Write-Host ""
    Write-Host "  =================================================================" -ForegroundColor "Cyan"
    Write-Host "                      PRINTER MIGRATION TOOL                       " -ForegroundColor "Yellow"
    Write-Host "  =================================================================" -ForegroundColor "Cyan"
    Write-Host ""
    Write-Host "    Standalone Printer Migration Script                           " -ForegroundColor "Green"
    Write-Host ""
    Write-Host "    Features:                                                      " -ForegroundColor "White"
    Write-Host "    * Migrate printers between computers                           " -ForegroundColor "Yellow"
    Write-Host "    * Automatic driver matching and installation                   " -ForegroundColor "Yellow"
    Write-Host "    * Enhanced error handling and reporting                        " -ForegroundColor "Yellow"
    Write-Host "    * Support for Zebra and Lexmark printers                       " -ForegroundColor "Yellow"
    Write-Host "    * Robust PSRemoting with fallback methods                      " -ForegroundColor "Yellow"
    Write-Host ""
    Write-Host "    Developed by: The greatest technician that ever lived         " -ForegroundColor "Magenta"
    Write-Host ""
    Write-Host "  =================================================================" -ForegroundColor "Cyan"
    Write-Host ""

    # Initialize credentials
    if (-not (Initialize-Credentials)) {
        Write-Color "Cannot proceed without valid credentials" "Red"
        return
    }

    # Get computer names with connectivity validation
    do {
        $sourceComputer = Read-Host -Prompt "`nEnter SOURCE computer name (copy FROM)"
        $targetComputer = Read-Host "Enter TARGET computer name (copy TO)"

        # Test connectivity
        Show-Banner "CONNECTIVITY TEST" "Yellow"

        Write-Color "Testing connectivity to source computer: $sourceComputer" "Cyan"
        $sourceResult = Test-PCOnlineAndDNS -computerName $sourceComputer

        Write-Color "Testing connectivity to target computer: $targetComputer" "Cyan"
        $targetResult = Test-PCOnlineAndDNS -computerName $targetComputer

        $allOnline = $sourceResult.Online -and $targetResult.Online

        if ($allOnline) {
            Write-Color "$sourceComputer is online. DNS Status: $($sourceResult.DNSStatus)" "Green"
            Write-Color "$targetComputer is online. DNS Status: $($targetResult.DNSStatus)" "Green"
            Write-Host ""
            break
        } else {
            Write-Host ""
            Write-Color "=== CONNECTIVITY ISSUES DETECTED ===" "Red"

            if (-not $sourceResult.Online) {
                Write-Color "SOURCE COMPUTER OFFLINE: $sourceComputer" "Red"
                Write-Color "   Error: $($sourceResult.Error)" "Yellow"
            } else {
                Write-Color "Source computer online: $sourceComputer" "Green"
            }

            if (-not $targetResult.Online) {
                Write-Color "TARGET COMPUTER OFFLINE: $targetComputer" "Red"
                Write-Color "   Error: $($targetResult.Error)" "Yellow"
            } else {
                Write-Color "Target computer online: $targetComputer" "Green"
            }

            Write-Host ""
            Write-Color "Please check the following:" "Yellow"
            Write-Color "• Verify computer names are spelled correctly" "Gray"
            Write-Color "• Ensure computers are powered on and connected to network" "Gray"
            Write-Color "• Check if computers are accessible from this location" "Gray"
            Write-Color "• Try using IP addresses instead of hostnames if DNS issues" "Gray"
            Write-Host ""

            $retry = Read-Host "Do you want to re-enter computer names? (Y/N)"
            if ($retry -notmatch '^(Y|y)$') {
                Write-Color "Exiting script due to connectivity issues." "Red"
                return
            }
            Write-Host ""
        }
    } while (-not $allOnline)

    # Confirm migration
    Write-Color "Ready to migrate printers:" "Cyan"
    Write-Color "  FROM: $sourceComputer" "Yellow"
    Write-Color "  TO:   $targetComputer" "Yellow"
    Write-Host ""

    $confirm = Read-Host "Do you want to proceed with printer migration? (Y/N)"
    if ($confirm -notmatch '^(Y|y)$') {
        Write-Color "Printer migration cancelled by user." "Yellow"
        return
    }

    # Start migration
    Show-Banner "PRINTER MIGRATION" "Green"
    Start-PrinterMigration -SourceComputer $sourceComputer -TargetComputer $targetComputer

    Write-Host ""
    Write-Color "Printer migration script completed!" "Green"
    Write-Host ""
    Read-Host "Press Enter to exit"
}

# Run the script
Start-MainScript
