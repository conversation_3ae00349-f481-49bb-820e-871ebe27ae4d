# Install Imprivata Type 2
        Write-Color "`nInstalling Imprivata Type 2..." "Cyan"
        
        # Create Imprivata directory
        $imprivataDir = "\\$copyToDeviceId\c$\Files\Imprivata"
        if (-not (Test-Path $imprivataDir)) {
            New-Item -Path $imprivataDir -ItemType Directory -Force | Out-Null
        }

        # Copy Imprivata files
        Copy-Item -Path "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Imprivata_push\*" -Destination $imprivataDir -Recurse -Force

        # Get stored credentials from source
        $sourceCreds = Invoke-Command -ComputerName $copyFromDeviceId -Credential $Credentials -ScriptBlock {
            $regPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon"
            if (Test-Path $regPath) {
                $props = Get-ItemProperty -Path $regPath
                [PSCustomObject]@{
                    UserName = $props.DefaultUserName
                    Password = $props.DefaultPassword
                }
            }
        }

        if ($sourceCreds.UserName -and $sourceCreds.Password) {
            Write-Color "  Found stored credentials on source computer" "Green"
            $AutoUsername = $sourceCreds.UserName
            $AutoPassword = ConvertTo-SecureString $sourceCreds.Password -AsPlainText -Force
        } else {
            Write-Color "  No stored credentials found" "Yellow"
            $AutoUsername = Read-Host "Enter Imprivata autologin username"
            $AutoPassword = Read-Host "Enter Imprivata autologin password" -AsSecureString
        }

        # Install Imprivata
        $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($AutoPassword)
        $PlainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
        [Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)

        Invoke-Command -Session $session -ScriptBlock {
            param($Username, $Password)
            
            # Install Imprivata
            Start-Process -FilePath "C:\Files\Imprivata\InstallType2.cmd" -WorkingDirectory "C:\Files\Imprivata" -Wait -NoNewWindow

            # Configure Winlogon settings
            $winlogonPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon"
            Set-ItemProperty -Path $winlogonPath -Name "DefaultUserName" -Value $Username
            Set-ItemProperty -Path $winlogonPath -Name "DefaultDomainName" -Value "MCLAREN"
            Set-ItemProperty -Path $winlogonPath -Name "DefaultPassword" -Value $Password
            Set-ItemProperty -Path $winlogonPath -Name "AutoAdminLogon" -Value "1"
            Set-ItemProperty -Path $winlogonPath -Name "ForceAutoLogon" -Value "1"

            # Configure System Policies
            Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "DontDisplayLastUsername" -Value 1 -Type DWord

            # Configure SSO Provider
            $ssoPath = "HKLM:\SOFTWARE\SSOProvider\ISXAgent"
            if (-not (Test-Path $ssoPath)) {
                New-Item -Path $ssoPath -Force | Out-Null
            }
            Set-ItemProperty -Path $ssoPath -Name "Type" -Value 2 -Type DWord

            # Cleanup
            Remove-Item -Path "C:\Files\Imprivata" -Recurse -Force -ErrorAction SilentlyContinue
        } -ArgumentList $AutoUsername, $PlainPassword

        # Clear sensitive data
        Remove-Variable -Name AutoUsername, PlainPassword -ErrorAction SilentlyContinue
        [GC]::Collect()
    }
    finally {
        if ($null -ne $session) {
            Remove-PSSession $session
        }
    }