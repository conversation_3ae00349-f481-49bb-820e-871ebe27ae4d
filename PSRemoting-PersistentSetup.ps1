# One-time PSRemoting configuration script
# Run this once on target computers to set up persistent PSRemoting

param(
    [string[]]$TargetComputers,
    [PSCredential]$Credential
)

function Configure-PersistentPSRemoting {
    param([string]$TargetComputer, [PSCredential]$Credential)
    
    Write-Host "Configuring persistent PSRemoting on $TargetComputer..." -ForegroundColor Cyan
    
    try {
        # Use CIM/DCOM to configure PSRemoting permanently
        $SessionArgs = @{
            ComputerName = $TargetComputer
            Credential = $Credential
            SessionOption = New-CimSessionOption -Protocol Dcom
        }
        
        $cimSession = New-CimSession @SessionArgs
        
        # Configure PSRemoting with optimal settings
        $configScript = @"
# Enable PSRemoting with force
Enable-PSRemoting -Force -SkipNetworkProfileCheck

# Configure WinRM service for automatic startup
Set-Service -Name WinRM -StartupType Automatic

# Set trusted hosts (adjust as needed for your environment)
Set-Item WSMan:\localhost\Client\TrustedHosts -Value "*" -Force

# Configure session limits for better performance
Set-Item WSMan:\localhost\Shell\MaxConcurrentUsers -Value 50
Set-Item WSMan:\localhost\Shell\MaxShellsPerUser -Value 25
Set-Item WSMan:\localhost\Shell\MaxProcessesPerShell -Value 100
Set-Item WSMan:\localhost\Shell\MaxMemoryPerShellMB -Value 1024

# Set longer timeouts for persistent sessions
Set-Item WSMan:\localhost\Shell\IdleTimeout -Value 3600000
Set-Item WSMan:\localhost\Shell\MaxShellRunTime -Value 0

# Configure authentication
Set-Item WSMan:\localhost\Service\Auth\Basic -Value $false
Set-Item WSMan:\localhost\Service\Auth\Kerberos -Value $true
Set-Item WSMan:\localhost\Service\Auth\Negotiate -Value $true
Set-Item WSMan:\localhost\Service\Auth\CredSSP -Value $false

# Restart WinRM to apply changes
Restart-Service WinRM -Force

Write-Host "PSRemoting configured successfully on $env:COMPUTERNAME" -ForegroundColor Green
"@

        # Execute configuration script
        $MethodArgs = @{
            ClassName = 'Win32_Process'
            MethodName = 'Create'
            CimSession = $cimSession
            Arguments = @{ CommandLine = "powershell -Command `"$configScript`"" }
        }
        
        $result = Invoke-CimMethod @MethodArgs
        Remove-CimSession $cimSession
        
        if ($result.ReturnValue -eq 0) {
            Write-Host "✓ PSRemoting configured successfully on $TargetComputer" -ForegroundColor Green
            
            # Wait for service restart
            Start-Sleep -Seconds 20
            
            # Test the configuration
            try {
                $testSession = New-PSSession -ComputerName $TargetComputer -Credential $Credential -ErrorAction Stop
                $testResult = Invoke-Command -Session $testSession -ScriptBlock { $env:COMPUTERNAME }
                Remove-PSSession $testSession
                Write-Host "✓ PSRemoting test successful on $TargetComputer" -ForegroundColor Green
                return $true
            }
            catch {
                Write-Host "✗ PSRemoting test failed on $TargetComputer`: $_" -ForegroundColor Red
                return $false
            }
        }
        else {
            Write-Host "✗ PSRemoting configuration failed on $TargetComputer (Return code: $($result.ReturnValue))" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ Error configuring PSRemoting on $TargetComputer`: $_" -ForegroundColor Red
        return $false
    }
}

# Main execution
if (-not $Credential) {
    $Credential = Get-Credential -Message "Enter domain credentials for PSRemoting configuration"
}

if (-not $TargetComputers) {
    $TargetComputers = @(Read-Host "Enter target computer name")
}

$results = @{}
foreach ($computer in $TargetComputers) {
    $results[$computer] = Configure-PersistentPSRemoting -TargetComputer $computer -Credential $Credential
}

# Summary
Write-Host "`nConfiguration Summary:" -ForegroundColor Cyan
foreach ($computer in $results.Keys) {
    $status = if ($results[$computer]) { "SUCCESS" } else { "FAILED" }
    $color = if ($results[$computer]) { "Green" } else { "Red" }
    Write-Host "  $computer`: $status" -ForegroundColor $color
}
