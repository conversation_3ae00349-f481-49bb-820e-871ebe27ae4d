# Network Files Copy Script
# Developed by: The greatest technician that ever lived
# Purpose: Copy all needed installer files to network locations

param(
    [Parameter(Mandatory=$false)]
    [switch]$WhatIf
)

# Color output function
function Write-Color {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

# Banner function
function Show-Banner {
    param([string]$Message, [string]$Color = "Cyan")
    $width = [Math]::Max(60, $Message.Length + 10)
    Write-Host ""
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ("*" + (" " * (($width - $Message.Length - 2) / 2)) + $Message + (" " * (($width - $Message.Length - 2) / 2)) + "*") -ForegroundColor $Color
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ""
}

# Copy function with verification
function Copy-WithVerification {
    param(
        [string]$Source,
        [string]$Destination,
        [string]$Name,
        [bool]$IsFolder = $false
    )
    
    Write-Color "Copying $Name..." "Yellow"
    Write-Color "  Source: $Source" "Gray"
    Write-Color "  Destination: $Destination" "Gray"

    if (-not (Test-Path $Source)) {
        Write-Color "  ERROR: Source not found: $Source" "Red"
        return $false
    }

    # Check if source and destination are the same
    $sourcePath = (Resolve-Path $Source -ErrorAction SilentlyContinue).Path
    $destPath = if ($IsFolder) { $Destination } else { (Split-Path $Destination -Parent) }

    if ($sourcePath -and (Test-Path $destPath)) {
        $resolvedDestPath = (Resolve-Path $destPath -ErrorAction SilentlyContinue).Path
        if ($sourcePath -eq $resolvedDestPath -or $Source -eq $Destination) {
            Write-Color "  INFO: Source and destination are the same - skipping copy" "Cyan"
            return $true
        }
    }

    if ($WhatIf) {
        Write-Color "  INFO: WHAT-IF: Would copy to $Destination" "Cyan"
        return $true
    }
    
    try {
        if ($IsFolder) {
            # Create destination directory
            if (-not (Test-Path $Destination)) {
                New-Item -Path $Destination -ItemType Directory -Force | Out-Null
            }
            
            # Use robocopy for folders with optimized arguments
            $robocopyArgs = @(
                "`"$Source`""
                "`"$Destination`""
                "/E"           # Copy subdirectories including empty ones
                "/MT:32"       # Multi-threaded copy (32 threads for max speed)
                "/R:1"         # Retry only 1 time (faster failure recovery)
                "/W:1"         # Wait only 1 second between retries
                "/J"           # Use unbuffered I/O (faster for large files)
                "/NP"          # No progress (reduces overhead)
                "/NDL"         # No directory list (reduces overhead)
                "/NJH"         # No job header (reduces overhead)
                "/NJS"         # No job summary (reduces overhead)
                "/NFL"         # No file list (reduces overhead)
                "/NC"          # No class info (reduces overhead)
                "/NS"          # No size info (reduces overhead)
            )
            $result = Start-Process -FilePath "robocopy" -ArgumentList $robocopyArgs -Wait -PassThru -NoNewWindow
            if ($result.ExitCode -le 7) {
                Write-Color "  SUCCESS: Folder copied successfully" "Green"
                return $true
            } else {
                Write-Color "  ERROR: Robocopy failed with exit code: $($result.ExitCode)" "Red"
                return $false
            }
        } else {
            # Create destination directory for file
            $destDir = Split-Path $Destination -Parent
            if (-not (Test-Path $destDir)) {
                New-Item -Path $destDir -ItemType Directory -Force | Out-Null
            }
            
            # Copy single file
            Copy-Item -Path $Source -Destination $Destination -Force
            Write-Color "  SUCCESS: File copied successfully" "Green"
            return $true
        }
    }
    catch {
        Write-Color "  ERROR: Copy failed: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Main execution
try {
    Show-Banner "NETWORK FILES COPY UTILITY" "Green"
    
    if ($WhatIf) {
        Write-Color "WHAT-IF MODE: No files will actually be copied" "Yellow"
        Write-Host ""
    }
    
    # Define source and destination paths
    $networkBase = "\\storagehd\Desktopshare\Scripts\Andrew\Needed"
    $usbBase = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Usb drive files"

    # All files to copy (will be copied to both network and USB locations)
    # Sources match the original network paths from working scripts
    $allFiles = @(
        @{ Name = "Splashtop"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\Splashtop_Push"; IsFolder = $true },
        @{ Name = ".NET 3.5 SXS"; Source = "\\bay-msfsnas01\data\FS\PUBLIC\installdotnet3_5\Win11\sxs"; IsFolder = $true },
        @{ Name = "Citrix Receiver 4.9"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\4.9_LTSR2"; IsFolder = $true },
        @{ Name = "Citrix Workspace"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\2203_LTSR"; IsFolder = $true },
        @{ Name = "Nuance Drivers"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\CustomCitrix\4.9_LTSR2\Nuance"; IsFolder = $true },
        @{ Name = "Cisco VPN"; Source = "\\mhc-msassccm1\sources\apps\msi\Cisco\Secure_Client\5_1_0_136"; IsFolder = $true },
        @{ Name = "Lexmark GDI Driver"; Source = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\LexmarkGDI"; IsFolder = $true },
        @{ Name = "Zebra Driver"; Source = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\ZebraDriver"; IsFolder = $true },
        @{ Name = "Xerox Driver"; Source = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Xerox"; IsFolder = $true },
        @{ Name = "Imprivata Agent"; Source = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\ImprivataAgent_x64.msi"; IsFolder = $false },
        @{ Name = "Photo Viewer Registry"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Win_Photo_Viewer.reg"; IsFolder = $false },
        @{ Name = "Volume Script"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Volume.ps1"; IsFolder = $false },
        @{ Name = "BitLocker Script"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\BitLockerAD.ps1"; IsFolder = $false },
        @{ Name = "Dell Command Update"; Source = "\\bay-msfsnas01\data\FS\Source\Commandupdate.EXE"; IsFolder = $false },
        @{ Name = "Java Runtime"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\WSP\7\jre1.7.0_45.msi"; IsFolder = $false },
        @{ Name = "Google Chrome"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\Google\GoogleChromeStandaloneEnterprise64.msi"; IsFolder = $false },
        @{ Name = "Office 365"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\o365"; IsFolder = $true }
    )

    # Generate network and USB file lists with proper destinations
    $networkFiles = @()
    $usbFiles = @()

    foreach ($file in $allFiles) {
        # Determine destination folder name
        $destName = switch ($file.Name) {
            "Splashtop" { "Splashtop" }
            ".NET 3.5 SXS" { "sxs" }
            "Citrix Receiver 4.9" { "4.9_LTSR2" }
            "Citrix Workspace" { "2203_LTSR" }
            "Nuance Drivers" { "Nuance121" }
            "Cisco VPN" { "CiscoVPN" }
            "Lexmark GDI Driver" { "LexmarkGDI" }
            "Zebra Driver" { "ZebraDriver" }
            "Xerox Driver" { "Xerox" }
            "Imprivata Agent" { "ImprivataAgent_x64.msi" }
            "Photo Viewer Registry" { "Win_Photo_Viewer.reg" }
            "Volume Script" { "Volume.ps1" }
            "BitLocker Script" { "BitLockerAD.ps1" }
            "Dell Command Update" { "Commandupdate.EXE" }
            "Java Runtime" { "jre1.7.0_45.msi" }
            "Google Chrome" { "GoogleChromeStandaloneEnterprise64.msi" }
            "Office 365" { "o365" }
        }

        # Add to network files
        $networkFiles += @{ Name = $file.Name; Source = $file.Source; Dest = "$networkBase\$destName"; IsFolder = $file.IsFolder }

        # Add to USB files
        $usbFiles += @{ Name = $file.Name; Source = $file.Source; Dest = "$usbBase\$destName"; IsFolder = $file.IsFolder }
    }
    
    # Copy to network location
    Write-Color "[1/2] Copying all files to network location..." "Cyan"
    $networkSuccess = 0
    $networkTotal = $networkFiles.Count

    foreach ($file in $networkFiles) {
        if (Copy-WithVerification -Source $file.Source -Destination $file.Dest -Name "$($file.Name) (Network)" -IsFolder $file.IsFolder) {
            $networkSuccess++
        }
        Write-Host ""
    }

    # Copy to USB staging location
    Write-Color "[2/2] Copying all files to USB staging location..." "Cyan"
    $usbSuccess = 0
    $usbTotal = $usbFiles.Count

    foreach ($file in $usbFiles) {
        if (Copy-WithVerification -Source $file.Source -Destination $file.Dest -Name "$($file.Name) (USB)" -IsFolder $file.IsFolder) {
            $usbSuccess++
        }
        Write-Host ""
    }
    
    # Summary
    Show-Banner "COPY OPERATION SUMMARY" "Green"
    Write-Color "Network Files: $networkSuccess/$networkTotal successful" "$(if ($networkSuccess -eq $networkTotal) { 'Green' } else { 'Yellow' })"
    Write-Color "USB Files: $usbSuccess/$usbTotal successful" "$(if ($usbSuccess -eq $usbTotal) { 'Green' } else { 'Yellow' })"
    Write-Color "Total: $($networkSuccess + $usbSuccess)/$($networkTotal + $usbTotal) files copied successfully" "$(if (($networkSuccess + $usbSuccess) -eq ($networkTotal + $usbTotal)) { 'Green' } else { 'Yellow' })"
    
    if ($WhatIf) {
        Write-Host ""
        Write-Color "This was a WHAT-IF run. Use without -WhatIf to actually copy files." "Yellow"
    }
}
catch {
    Write-Color "Script failed: $($_.Exception.Message)" "Red"
    exit 1
}

Write-Host ""
Write-Color "Script completed. Press Enter to exit..." "Gray"
Read-Host
