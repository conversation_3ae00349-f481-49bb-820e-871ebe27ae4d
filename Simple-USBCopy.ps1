# Simple Reliable USB Copy Script
# Developed by: The greatest technician that ever lived
# Purpose: 100% reliable USB copying using PowerShell

param([switch]$WhatIf)

function Write-Status($Message, $Type = "Info") {
    $color = switch ($Type) {
        "Success" { "Green" }; "Warning" { "Yellow" }; "Error" { "Red" }; "Info" { "Cyan" }
        default { "White" }
    }
    Write-Host $Message -ForegroundColor $color
}

function Format-Size([long]$Bytes) {
    if ($Bytes -eq 0) { return "0 B" }
    $sizes = @("B", "KB", "MB", "GB")
    $index = 0; $size = [double]$Bytes
    while ($size -ge 1024 -and $index -lt 3) { $size /= 1024; $index++ }
    return "{0:N2} {1}" -f $size, $sizes[$index]
}

Write-Status "*** SIMPLE USB COPY UTILITY ***" "Info"
if ($WhatIf) { Write-Status "WHAT-IF MODE" "Warning" }
Write-Host ""

# Find USB drives
Write-Status "Finding USB drives..." "Info"
$usbDrives = @()
Get-WmiObject Win32_LogicalDisk | Where-Object { $_.DriveType -eq 2 -and $_.Size -gt 0 } | ForEach-Object {
    $usbDrives += [PSCustomObject]@{
        Drive = $_.DeviceID
        Label = if ($_.VolumeName) { $_.VolumeName } else { "No Label" }
        SizeGB = [math]::Round($_.Size / 1GB, 2)
        FreeGB = [math]::Round($_.FreeSpace / 1GB, 2)
        Display = "$($_.DeviceID) [$($_.VolumeName)] ($([math]::Round($_.Size / 1GB, 2)) GB, $([math]::Round($_.FreeSpace / 1GB, 2)) GB free)"
    }
}

if ($usbDrives.Count -eq 0) {
    Write-Status "No USB drives found!" "Error"
    exit 1
}

Write-Status "Found USB drives:" "Success"
for ($i = 0; $i -lt $usbDrives.Count; $i++) {
    Write-Host "  [$i] $($usbDrives[$i].Display)" -ForegroundColor Gray
}

# Get selection
do {
    $selection = Read-Host "`nSelect USB drive (0-$($usbDrives.Count - 1))"
    $index = $null
} while (-not ([int]::TryParse($selection, [ref]$index) -and $index -ge 0 -and $index -lt $usbDrives.Count))

$selectedUSB = $usbDrives[$index]
Write-Status "Selected: $($selectedUSB.Display)" "Success"

# Check source
Write-Host ""
Write-Status "Checking source..." "Info"
$sourcePath = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Usb drive files"

if (-not (Test-Path $sourcePath)) {
    Write-Status "Source not found: $sourcePath" "Error"
    exit 1
}

$sourceItems = Get-ChildItem $sourcePath -ErrorAction SilentlyContinue
$totalSize = (Get-ChildItem $sourcePath -Recurse -File -ErrorAction SilentlyContinue | Measure-Object Length -Sum).Sum
$totalSizeGB = [math]::Round($totalSize / 1GB, 2)

Write-Status "Source ready: $($sourceItems.Count) items, $(Format-Size $totalSize)" "Success"

# Check space
$requiredGB = $totalSizeGB * 1.1
if ($selectedUSB.FreeGB -lt $requiredGB) {
    Write-Status "Insufficient space! Need: $requiredGB GB, Have: $($selectedUSB.FreeGB) GB" "Error"
    exit 1
}
Write-Status "Space OK: $($selectedUSB.FreeGB) GB available" "Success"

# Confirm
if (-not $WhatIf) {
    Write-Host ""
    Write-Status "Ready to copy $totalSizeGB GB to $($selectedUSB.Drive)" "Warning"
    $confirm = Read-Host "Continue? (Y/N)"
    if ($confirm -notmatch '^[Yy]$') { exit 0 }
}

# Copy files
Write-Host ""
Write-Status "Copying files..." "Info"
$destination = $selectedUSB.Drive + "\"
$successCount = 0
$errorCount = 0
$startTime = Get-Date

foreach ($item in $sourceItems) {
    $itemName = $item.Name
    $sourceFull = $item.FullName
    $destFull = Join-Path $destination $itemName
    
    Write-Host "Copying: $itemName" -ForegroundColor Yellow
    
    if ($WhatIf) {
        Write-Host "  WHAT-IF: Would copy to $destFull" -ForegroundColor Cyan
        $successCount++
        continue
    }
    
    try {
        if ($item.PSIsContainer) {
            # Copy folder
            Write-Host "  Copying folder..." -ForegroundColor Gray
            Copy-Item -Path $sourceFull -Destination $destination -Recurse -Force
            Write-Host "  ✅ Folder copied" -ForegroundColor Green
        } else {
            # Copy file
            Write-Host "  Copying file..." -ForegroundColor Gray
            Copy-Item -Path $sourceFull -Destination $destFull -Force
            Write-Host "  ✅ File copied" -ForegroundColor Green
        }
        $successCount++
    }
    catch {
        Write-Host "  ❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
    }
}

# Summary
$endTime = Get-Date
$duration = ($endTime - $startTime).TotalSeconds

Write-Host ""
Write-Status "*** COPY COMPLETE ***" "Info"
Write-Status "Successful: $successCount items" "Success"
Write-Status "Failed: $errorCount items" "$(if ($errorCount -gt 0) { 'Error' } else { 'Success' })"
Write-Status "Time: $([math]::Round($duration, 1)) seconds" "Info"

if (-not $WhatIf -and $errorCount -eq 0) {
    $finalDrive = Get-WmiObject Win32_LogicalDisk | Where-Object { $_.DeviceID -eq $selectedUSB.Drive }
    $finalFreeGB = [math]::Round($finalDrive.FreeSpace / 1GB, 2)
    $usedGB = $selectedUSB.FreeGB - $finalFreeGB
    
    Write-Status "Space used: $(Format-Size ($usedGB * 1GB))" "Info"
    Write-Status "Space remaining: $finalFreeGB GB" "Info"
    Write-Status "🎉 USB drive ready!" "Success"
}

Write-Host "`nPress Enter to exit..." -ForegroundColor Gray
Read-Host
