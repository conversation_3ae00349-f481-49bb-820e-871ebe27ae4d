# Hybrid USB Installer Copy Script - Uses your working batch commands with PowerShell drive detection
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

function Write-Color {
    param([string]$Text, [string]$Color = "White")
    Write-Host $Text -ForegroundColor $Color
}

function Get-AvailableDrives {
    Write-Color "Detecting available drives..." "Cyan"

    $drives = @()
    $driveCount = 0

    $allDrives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -ne "C:" }

    Write-Color "REMOVABLE DRIVES (USB/External):" "Yellow"
    $removableDrives = $allDrives | Where-Object { $_.DriveType -eq 2 }

    foreach ($drive in $removableDrives) {
        $driveCount++
        $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
        $volumeLabel = if ($drive.VolumeName) { "`"$($drive.VolumeName)`"" } else { "(No Label)" }

        Write-Color "[$driveCount] $($drive.DeviceID) $volumeLabel (USB/Removable) - $freeSpaceGB GB free / $totalSpaceGB GB total" "White"

        $drives += @{
            Number = $driveCount
            DeviceID = $drive.DeviceID
            VolumeName = $drive.VolumeName
            FreeSpace = $drive.FreeSpace
            Size = $drive.Size
            Type = "USB/Removable"
        }
    }

    Write-Host ""
    Write-Color "FIXED DRIVES (Local Hard Drives):" "Yellow"
    $fixedDrives = $allDrives | Where-Object { $_.DriveType -eq 3 }

    foreach ($drive in $fixedDrives) {
        $driveCount++
        $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
        $volumeLabel = if ($drive.VolumeName) { "`"$($drive.VolumeName)`"" } else { "(No Label)" }

        Write-Color "[$driveCount] $($drive.DeviceID) $volumeLabel (Fixed Drive) - $freeSpaceGB GB free / $totalSpaceGB GB total" "White"

        $drives += @{
            Number = $driveCount
            DeviceID = $drive.DeviceID
            VolumeName = $drive.VolumeName
            FreeSpace = $drive.FreeSpace
            Size = $drive.Size
            Type = "Fixed Drive"
        }
    }

    if ($driveCount -eq 0) {
        Write-Color "ERROR: No suitable drives found!" "Red"
        Read-Host "Press Enter to exit"
        exit 1
    }

    Write-Host ""
    Write-Color "[0] Exit" "Gray"
    Write-Host ""

    return $drives
}

function Select-Drive {
    param([array]$Drives)

    do {
        $choice = Read-Host "Please select a drive (1-$($Drives.Count) or 0 to exit)"

        if ($choice -eq "0") {
            Write-Color "Operation cancelled by user." "Yellow"
            exit 0
        }

        $selectedDrive = $Drives | Where-Object { $_.Number -eq [int]$choice }

        if (-not $selectedDrive) {
            Write-Color "Invalid selection. Please try again." "Red"
        }
    } while (-not $selectedDrive)

    return $selectedDrive
}

function Copy-UsingBatchCommands {
    param([string]$DestRoot)

    Write-Color "Creating directory structure..." "Cyan"
    cmd.exe /c "md `"$DestRoot\dcu`" 2>nul"

    Write-Color "Copying folders using your exact batch file commands..." "Cyan"

    # Use optimized robocopy commands for faster copying
    # /E = Copy subdirectories including empty ones
    # /MT:16 = Use 16 threads for multi-threaded copying (much faster)
    # /R:3 = Retry only 3 times on failed copies (instead of default 1 million)
    # /W:10 = Wait only 10 seconds between retries (instead of default 30)
    # /NFL = No File List (don't log file names - faster)
    # /NDL = No Directory List (don't log directory names - faster)
    # /NJH = No Job Header (suppress header - cleaner output)
    # /NJS = No Job Summary (suppress summary - cleaner output)
    # /NC = No Class (don't show file classes - faster)
    # /NS = No Size (don't show file sizes - faster)
    # /NP = No Progress (don't show % progress per file - much faster)
    $robocopyArgs = "/E /MT:16 /R:3 /W:10 /NFL /NDL /NJH /NJS /NC /NS /NP"

    Write-Color "Copying Splashtop..." "Yellow"
    cmd.exe /c "robocopy `"\\bay-msfsnas01\data\FS\SFILES\__install__\Source\Splashtop_Push`" `"$DestRoot\Splashtop`" $robocopyArgs"
    Write-Color "Splashtop: Exit code $LASTEXITCODE" "Gray"

    Write-Color "Copying .NET 3.5 SXS..." "Yellow"
    cmd.exe /c "robocopy `"\\bay-msfsnas01\data\FS\PUBLIC\installdotnet3_5\Win11\sxs`" `"$DestRoot\sxs`" $robocopyArgs"
    Write-Color ".NET SXS: Exit code $LASTEXITCODE" "Gray"

    Write-Color "Copying Lexmark Driver..." "Yellow"
    cmd.exe /c "robocopy `"\\bay-msfsnas01\data\FS\SFILES\__install__\source\Lexmark\Lexmark_Universal_v2_UD1_PostScript_3_Emulation`" `"$DestRoot\Lexmark_Universal_v2_UD1_PostScript_3_Emulation`" $robocopyArgs"
    Write-Color "Lexmark Driver: Exit code $LASTEXITCODE" "Gray"

    Write-Color "Copying FSTools..." "Yellow"
    cmd.exe /c "robocopy `"\\bay-msfsnas01\data\FS\SFILES\__install__\Source\FSTools`" `"$DestRoot\FSTools`" $robocopyArgs"
    Write-Color "FSTools: Exit code $LASTEXITCODE" "Gray"

    Write-Color "Copying Nuance..." "Yellow"
    cmd.exe /c "robocopy `"\\bay-msfsnas01\data\FS\SFILES\__install__\Source\CustomCitrix\4.9_LTSR2\Nuance`" `"$DestRoot\Nuance121`" $robocopyArgs"
    Write-Color "Nuance: Exit code $LASTEXITCODE" "Gray"

    Write-Color "Copying Office 365..." "Yellow"
    cmd.exe /c "robocopy `"\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\o365`" `"$DestRoot\o365`" $robocopyArgs"
    Write-Color "Office 365: Exit code $LASTEXITCODE" "Gray"

    Write-Color "Copying Citrix 4.9 LTSR2..." "Yellow"
    cmd.exe /c "robocopy `"\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\4.9_LTSR2`" `"$DestRoot\4.9_LTSR2`" $robocopyArgs"
    Write-Color "Citrix: Exit code $LASTEXITCODE" "Gray"

    Write-Color "Copying Lexmark GDI..." "Yellow"
    cmd.exe /c "robocopy `"\\storagehd\Desktopshare\Scripts\Andrew\Needed\LexmarkGDI`" `"$DestRoot\LexmarkGDI`" $robocopyArgs"
    Write-Color "Lexmark GDI: Exit code $LASTEXITCODE" "Gray"

    Write-Color "Copying Imprivata Agent..." "Yellow"
    cmd.exe /c "copy `"\\storagehd\Desktopshare\Scripts\Andrew\Needed\Imprivata_push\ImprivataAgent_x64.msi`" `"$DestRoot\ImprivataAgent_x64.msi`""
    Write-Color "Imprivata Agent: Exit code $LASTEXITCODE" "Gray"

    Write-Host ""
    Write-Color "Copying individual files using your exact batch file commands..." "Cyan"

    # Use your exact copy commands from the working batch file
    Write-Color "Copying Java RE 7..." "Yellow"
    cmd.exe /c "copy `"\\bay-msfsnas01\data\FS\SFILES\__install__\Source\WSP\7\jre1.7.0_45.msi`" `"$DestRoot\jre1.7.0_45.msi`""

    Write-Color "Copying Google Chrome..." "Yellow"
    cmd.exe /c "copy `"\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\Google\GoogleChromeStandaloneEnterprise64.msi`" `"$DestRoot\GoogleChromeStandaloneEnterprise64.msi`""

    Write-Color "Copying Citrix Receiver..." "Yellow"
    cmd.exe /c "copy `"\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\4.9_LTSR2\CitrixReceiver.cmd`" `"$DestRoot\4.9_LTSR2\CitrixReceiver.cmd`""

    Write-Color "Copying Photo Viewer Registry..." "Yellow"
    cmd.exe /c "robocopy `"\\bay-msfsnas01\data\FS\SFILES\__install__\PS`" `"$DestRoot`" Win_Photo_Viewer.reg /MT:16 /R:3 /W:10 /NFL /NDL /NJH /NJS /NC /NS /NP"

    Write-Color "Copying Volume Script..." "Yellow"
    cmd.exe /c "robocopy `"\\bay-msfsnas01\data\FS\SFILES\__install__\PS`" `"$DestRoot`" Volume.ps1 /MT:16 /R:3 /W:10 /NFL /NDL /NJH /NJS /NC /NS /NP"

    Write-Color "Copying BitLocker Script..." "Yellow"
    cmd.exe /c "robocopy `"\\bay-msfsnas01\data\FS\SFILES\__install__\PS`" `"$DestRoot`" BitLockerAD.ps1 /MT:16 /R:3 /W:10 /NFL /NDL /NJH /NJS /NC /NS /NP"

    Write-Color "Copying Dell Command Update..." "Yellow"
    cmd.exe /c "copy `"\\bay-msfsnas01\data\FS\Source\Commandupdate.EXE`" `"$DestRoot\dcu\Commandupdate.EXE`""
}

# Main execution
Clear-Host
Write-Color "********************************************************************************" "Magenta"
Write-Color "*                    INSTALLER FILES COPY UTILITY                             *" "Magenta"
Write-Color "********************************************************************************" "Magenta"
Write-Host ""

$availableDrives = Get-AvailableDrives
$selectedDrive = Select-Drive -Drives $availableDrives

Write-Host ""
Write-Color "Selected drive: $($selectedDrive.DeviceID)" "Green"

$confirm = Read-Host "Do you want to proceed with copying installer files to $($selectedDrive.DeviceID)? (Y/N)"
if ($confirm -notmatch '^(Y|y)$') {
    Write-Color "Operation cancelled by user." "Yellow"
    Read-Host "Press Enter to exit"
    exit 0
}

Write-Host ""
Write-Color "Starting copy operations using your exact batch file commands..." "Cyan"

# Use your working batch file commands
Copy-UsingBatchCommands -DestRoot $selectedDrive.DeviceID

Write-Host ""
Write-Color "********************************************************************************" "Green"
Write-Color "*                           COPY OPERATION COMPLETE                           *" "Green"
Write-Color "********************************************************************************" "Green"
Write-Host ""
Write-Color "All installer files have been copied to $($selectedDrive.DeviceID)." "Green"
Write-Host ""
Write-Color "NEXT STEPS:" "Yellow"
Write-Color "IMPORTANT: Files are now on $($selectedDrive.DeviceID) of THIS computer" "Cyan"
Write-Host ""
if ($selectedDrive.Type -eq "USB/Removable") {
    Write-Color "USB DRIVE WORKFLOW:" "Yellow"
    Write-Color "1. Take the USB drive ($($selectedDrive.DeviceID)) to the TARGET computer" "White"
    Write-Color "2. Copy ALL files from USB drive to D:\ on the TARGET computer" "White"
    Write-Color "3. Run migration script on TARGET computer and select 'Yes' for local files" "White"
} else {
    Write-Color "LOCAL DRIVE WORKFLOW:" "Yellow"
    Write-Color "1. Files are on $($selectedDrive.DeviceID) of THIS computer" "White"
    Write-Color "2. Copy ALL files from $($selectedDrive.DeviceID) to D:\ on TARGET computer" "White"
    Write-Color "3. Run migration script on TARGET computer and select 'Yes' for local files" "White"
}
Write-Host ""
Write-Color "The migration script expects files to be on D:\ of the TARGET computer!" "Red"
Write-Host ""
Read-Host "Press Enter to exit"
