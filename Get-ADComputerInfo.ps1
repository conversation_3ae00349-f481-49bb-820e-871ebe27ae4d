# Simple script to get AD computer information
# Usage: .\Get-ADComputerInfo.ps1 -ComputerName "mgbr307tus18l"

param(
    [Parameter(Mandatory=$false)]
    [string]$ComputerName = "mgbr307tus18l"
)

Write-Host "Getting AD information for computer: $ComputerName" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

try {
    # Import Active Directory module
    Import-Module ActiveDirectory -ErrorAction Stop
    Write-Host "Active Directory module loaded successfully" -ForegroundColor Green
}
catch {
    Write-Host "Error loading Active Directory module: $_" -ForegroundColor Red
    Write-Host "Make sure RSAT-AD-PowerShell feature is installed" -ForegroundColor Yellow
    exit 1
}

try {
    # Get computer with all properties
    Write-Host "`nRetrieving computer information..." -ForegroundColor Yellow
    
    $computer = Get-ADComputer $ComputerName -Properties * -ErrorAction Stop
    
    Write-Host "`nBASIC INFORMATION:" -ForegroundColor Cyan
    Write-Host "Name: $($computer.Name)" -ForegroundColor White
    Write-Host "Distinguished Name: $($computer.DistinguishedName)" -ForegroundColor White
    Write-Host "Enabled: $($computer.Enabled)" -ForegroundColor White
    Write-Host "Created: $($computer.Created)" -ForegroundColor White
    Write-Host "Modified: $($computer.Modified)" -ForegroundColor White
    Write-Host "Last Logon: $($computer.LastLogonDate)" -ForegroundColor White
    Write-Host "Operating System: $($computer.OperatingSystem)" -ForegroundColor White
    Write-Host "OS Version: $($computer.OperatingSystemVersion)" -ForegroundColor White
    
    Write-Host "`nDESCRIPTION INFORMATION:" -ForegroundColor Cyan
    Write-Host "Description Property:" -ForegroundColor Yellow
    if ($computer.Description) {
        Write-Host "  Value: '$($computer.Description)'" -ForegroundColor Green
        Write-Host "  Length: $($computer.Description.Length)" -ForegroundColor Green
        Write-Host "  Type: $($computer.Description.GetType().Name)" -ForegroundColor Green
    } else {
        Write-Host "  Value: [NULL or EMPTY]" -ForegroundColor Red
        Write-Host "  Type: $($computer.Description.GetType().Name)" -ForegroundColor Red -ErrorAction SilentlyContinue
    }
    
    # Check for other comment-related properties
    Write-Host "`nOTHER COMMENT PROPERTIES:" -ForegroundColor Cyan
    $commentProperties = @('Info', 'Comment', 'Notes', 'AdminDescription')
    foreach ($prop in $commentProperties) {
        if ($computer.PSObject.Properties.Name -contains $prop) {
            $value = $computer.$prop
            if ($value) {
                Write-Host "$prop`: '$value'" -ForegroundColor Green
            } else {
                Write-Host "$prop`: [NULL or EMPTY]" -ForegroundColor Gray
            }
        } else {
            Write-Host "$prop`: [Property not found]" -ForegroundColor Gray
        }
    }
    
    Write-Host "`nLOCATION INFORMATION:" -ForegroundColor Cyan
    Write-Host "Organizational Unit: $($computer.DistinguishedName -replace '^CN=[^,]+,','')" -ForegroundColor White
    
    Write-Host "`nGROUP MEMBERSHIPS:" -ForegroundColor Cyan
    $groups = Get-ADPrincipalGroupMembership $ComputerName | Select-Object Name, DistinguishedName
    if ($groups) {
        foreach ($group in $groups) {
            Write-Host "  $($group.Name)" -ForegroundColor White
        }
    } else {
        Write-Host "  No group memberships found" -ForegroundColor Gray
    }
    
    Write-Host "`nRAW DESCRIPTION CHECK:" -ForegroundColor Cyan
    # Try different ways to get the description
    Write-Host "Method 1 - Direct property access:" -ForegroundColor Yellow
    Write-Host "  '$($computer.Description)'" -ForegroundColor White
    
    Write-Host "Method 2 - Get-ADComputer with Description property:" -ForegroundColor Yellow
    $comp2 = Get-ADComputer $ComputerName -Properties Description
    Write-Host "  '$($comp2.Description)'" -ForegroundColor White
    
    Write-Host "Method 3 - LDAP search:" -ForegroundColor Yellow
    try {
        $searcher = [adsisearcher]"(&(objectClass=computer)(name=$ComputerName))"
        $searcher.PropertiesToLoad.Add("description") | Out-Null
        $result = $searcher.FindOne()
        if ($result -and $result.Properties["description"]) {
            Write-Host "  '$($result.Properties["description"][0])'" -ForegroundColor White
        } else {
            Write-Host "  [No description found via LDAP]" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "  [LDAP search failed: $_]" -ForegroundColor Red
    }
    
    Write-Host "`n" + "=" * 60 -ForegroundColor Gray
    Write-Host "AD information retrieval completed successfully!" -ForegroundColor Green
    
}
catch {
    Write-Host "`nError retrieving computer information: $_" -ForegroundColor Red
    Write-Host "Make sure:" -ForegroundColor Yellow
    Write-Host "1. Computer name is correct" -ForegroundColor Yellow
    Write-Host "2. You have permissions to read AD" -ForegroundColor Yellow
    Write-Host "3. Computer exists in Active Directory" -ForegroundColor Yellow
    exit 1
}

Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
