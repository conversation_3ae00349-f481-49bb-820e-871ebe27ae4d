# Quick test to show local applications
Write-Host "Testing local application detection..." -ForegroundColor Yellow

# Get applications from registry (64-bit)
$apps64 = Get-ItemProperty "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
    Where-Object { $_.DisplayName -and $_.DisplayName -notlike "KB*" } |
    Select-Object DisplayName, DisplayVersion, Publisher

# Get applications from registry (32-bit on 64-bit systems)
$apps32 = Get-ItemProperty "HKLM:\Software\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
    Where-Object { $_.DisplayName -and $_.DisplayName -notlike "KB*" } |
    Select-Object DisplayName, DisplayVersion, Publisher

$allApps = ($apps64 + $apps32) | Sort-Object DisplayName -Unique

Write-Host "Found $($allApps.Count) applications:" -ForegroundColor Green
$allApps | Select-Object -First 10 | ForEach-Object {
    Write-Host "  - $($_.DisplayName) ($($_.DisplayVersion))" -ForegroundColor White
}

if ($allApps.Count -gt 10) {
    Write-Host "  ... and $($allApps.Count - 10) more applications" -ForegroundColor Cyan
}
