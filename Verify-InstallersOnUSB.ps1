# ===============================================================================
# INSTALLER FILES VERIFICATION UTILITY - PowerShell Version
# This script verifies that all required installer files are present on
# a selected USB or local drive for use with the migration script
# ===============================================================================

# Set execution policy for current session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Function to display colored text
function Write-Color {
    param(
        [string]$Text,
        [string]$Color = "White",
        [switch]$NoNewline
    )
    if ($NoNewline) {
        Write-Host $Text -ForegroundColor $Color -NoNewline
    } else {
        Write-Host $Text -ForegroundColor $Color
    }
}

# Function to display banner
function Show-Banner {
    param([string]$Text, [string]$Color = "Yellow")
    Write-Host ""
    Write-Color "********************************************************************************" $Color
    $padding = [math]::Max(0, (78 - $Text.Length) / 2)
    $leftPad = ' ' * [math]::Floor($padding)
    $rightPad = ' ' * [math]::Ceiling($padding)
    Write-Color "*$leftPad$Text$rightPad*" $Color
    Write-Color "********************************************************************************" $Color
    Write-Host ""
}

# Function to detect and display available drives
function Get-AvailableDrives {
    Write-Color "Detecting available drives..." "Cyan"
    Write-Host ""

    $drives = @()
    $driveCount = 0

    # Get all logical drives
    $allDrives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -ne "C:" }

    Write-Color "REMOVABLE DRIVES (USB/External):" "Yellow"
    $removableDrives = $allDrives | Where-Object { $_.DriveType -eq 2 }

    foreach ($drive in $removableDrives) {
        $driveCount++
        $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
        $volumeLabel = if ($drive.VolumeName) { "`"$($drive.VolumeName)`"" } else { "(No Label)" }

        Write-Color "[$driveCount] $($drive.DeviceID) $volumeLabel (USB/Removable) - $freeSpaceGB GB free / $totalSpaceGB GB total" "White"

        $drives += @{
            Number = $driveCount
            DeviceID = $drive.DeviceID
            VolumeName = $drive.VolumeName
            FreeSpace = $drive.FreeSpace
            Size = $drive.Size
            Type = "USB/Removable"
        }
    }

    Write-Host ""
    Write-Color "FIXED DRIVES (Local Hard Drives):" "Yellow"
    $fixedDrives = $allDrives | Where-Object { $_.DriveType -eq 3 }

    foreach ($drive in $fixedDrives) {
        $driveCount++
        $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
        $volumeLabel = if ($drive.VolumeName) { "`"$($drive.VolumeName)`"" } else { "(No Label)" }

        Write-Color "[$driveCount] $($drive.DeviceID) $volumeLabel (Fixed Drive) - $freeSpaceGB GB free / $totalSpaceGB GB total" "White"

        $drives += @{
            Number = $driveCount
            DeviceID = $drive.DeviceID
            VolumeName = $drive.VolumeName
            FreeSpace = $drive.FreeSpace
            Size = $drive.Size
            Type = "Fixed Drive"
        }
    }

    if ($driveCount -eq 0) {
        Write-Color "ERROR: No suitable drives found!" "Red"
        Write-Color "Please connect a USB drive or ensure other drives are available." "Red"
        Read-Host "Press Enter to exit"
        exit 1
    }

    Write-Host ""
    Write-Color "[0] Exit" "Gray"
    Write-Host ""

    return $drives
}

# Function to select drive
function Select-Drive {
    param([array]$Drives)

    do {
        $choice = Read-Host "Please select a drive to verify (1-$($Drives.Count) or 0 to exit)"

        if ($choice -eq "0") {
            Write-Color "Operation cancelled by user." "Yellow"
            exit 0
        }

        $selectedDrive = $Drives | Where-Object { $_.Number -eq [int]$choice }

        if (-not $selectedDrive) {
            Write-Color "Invalid selection. Please try again." "Red"
        }
    } while (-not $selectedDrive)

    return $selectedDrive
}

# Function to verify installer files
function Test-InstallerFiles {
    param([string]$TargetDrive)

    Write-Color "Checking for required installer files on $TargetDrive..." "Cyan"
    Write-Host ""

    $missingFiles = 0
    $totalFiles = 0
    $presentFiles = 0

    # Define required files and folders
    $requiredItems = @(
        @{ Path = "$TargetDrive\Splashtop_Push"; Name = "Splashtop"; IsFolder = $true },
        @{ Path = "$TargetDrive\sxs"; Name = ".NET 3.5 SXS"; IsFolder = $true },
        @{ Path = "$TargetDrive\Lexmark_Universal_v2_UD1_PostScript_3_Emulation"; Name = "Lexmark Universal Driver"; IsFolder = $true },
        @{ Path = "$TargetDrive\o365"; Name = "Office 365"; IsFolder = $true },
        @{ Path = "$TargetDrive\4.9_LTSR2"; Name = "Citrix 4.9 LTSR2"; IsFolder = $true },
        @{ Path = "$TargetDrive\Nuance121"; Name = "Nuance"; IsFolder = $true },
        @{ Path = "$TargetDrive\FSTools"; Name = "FSTools"; IsFolder = $true },
        @{ Path = "$TargetDrive\LexmarkGDI"; Name = "Lexmark GDI Driver"; IsFolder = $true },
        @{ Path = "$TargetDrive\Imprivata_push"; Name = "Imprivata Push"; IsFolder = $true },
        @{ Path = "$TargetDrive\jre1.7.0_45.msi"; Name = "Java RE 7"; IsFolder = $false },
        @{ Path = "$TargetDrive\GoogleChromeStandaloneEnterprise64.msi"; Name = "Google Chrome"; IsFolder = $false },
        @{ Path = "$TargetDrive\Win_Photo_Viewer.reg"; Name = "Windows Photo Viewer Registry"; IsFolder = $false },
        @{ Path = "$TargetDrive\Volume.ps1"; Name = "Volume PowerShell Script"; IsFolder = $false },
        @{ Path = "$TargetDrive\BitLockerAD.ps1"; Name = "BitLocker PowerShell Script"; IsFolder = $false },
        @{ Path = "$TargetDrive\Commandupdate.EXE"; Name = "Dell Command Update"; IsFolder = $false }
    )

    foreach ($item in $requiredItems) {
        $totalFiles++

        if ($item.IsFolder) {
            if (Test-Path $item.Path -PathType Container) {
                Write-Color "✓ FOUND: $($item.Name) folder" "Green"
                $presentFiles++
            } else {
                Write-Color "✗ MISSING: $($item.Name) folder ($($item.Path))" "Red"
                $missingFiles++
            }
        } else {
            if (Test-Path $item.Path -PathType Leaf) {
                Write-Color "✓ FOUND: $($item.Name) file" "Green"
                $presentFiles++
            } else {
                Write-Color "✗ MISSING: $($item.Name) file ($($item.Path))" "Red"
                $missingFiles++
            }
        }
    }

    Write-Host ""
    Show-Banner "VERIFICATION RESULTS" "Cyan"

    Write-Color "Total items checked: $totalFiles" "White"
    Write-Color "Missing items: $missingFiles" "Red"
    Write-Color "Present items: $presentFiles" "Green"
    Write-Host ""

    if ($missingFiles -eq 0) {
        Write-Color "✓ SUCCESS: All required installer files are present on $TargetDrive!" "Green"
        Write-Color "  You can now use these files with the migration script." "Green"
        Write-Host ""

        # Show folder sizes for major components
        Write-Color "Estimated disk usage:" "Yellow"
        $majorFolders = @("$TargetDrive\o365", "$TargetDrive\4.9_LTSR2", "$TargetDrive\Splashtop_Push", "$TargetDrive\Lexmark_Universal_v2_UD1_PostScript_3_Emulation")

        foreach ($folder in $majorFolders) {
            if (Test-Path $folder) {
                try {
                    $size = (Get-ChildItem $folder -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
                    $sizeGB = [math]::Round($size / 1GB, 2)
                    $folderName = Split-Path $folder -Leaf
                    Write-Color "  $folderName`: $sizeGB GB" "Gray"
                }
                catch {
                    # Ignore errors calculating folder size
                }
            }
        }
    } else {
        Write-Color "✗ WARNING: $missingFiles required files/folders are missing!" "Red"
        Write-Color "  Please run the copy utility to download missing files to $TargetDrive." "Red"
        Write-Host ""
        Write-Color "  Missing files need to be copied before using local installer option." "Yellow"
    }

    Write-Host ""
    Write-Color "Verification completed: $(Get-Date)" "Gray"
    Write-Color "Selected drive: $TargetDrive" "Gray"
    Write-Host ""

    Write-Color "NEXT STEPS:" "Yellow"
    if ($missingFiles -eq 0) {
        Write-Color "1. If files are on USB drive, copy them to D:\ on target computer" "White"
        Write-Color "2. Run migration script and select 'Yes' for local installer files" "White"
        Write-Color "3. Migration script will automatically use D:\ drive" "White"
    } else {
        Write-Color "1. Run copy utility to download missing files" "White"
        Write-Color "2. Verify again before proceeding with migration" "White"
    }
}

# Main script execution
Clear-Host

Show-Banner "INSTALLER FILES VERIFICATION UTILITY" "Magenta"

Write-Color "This utility verifies that all required installer files are present" "Green"
Write-Color "on a selected drive for use with the migration script." "Green"
Write-Host ""

# Detect available drives
$availableDrives = Get-AvailableDrives

# Select drive
$selectedDrive = Select-Drive -Drives $availableDrives

Write-Host ""
Write-Color "Selected drive for verification: $($selectedDrive.DeviceID)" "Green"
Write-Host ""

# Verify files
Test-InstallerFiles -TargetDrive $selectedDrive.DeviceID

Write-Host ""
Read-Host "Press Enter to exit"
