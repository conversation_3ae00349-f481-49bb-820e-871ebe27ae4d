# Driver Export and Install Script
# Exports drivers from source PC and installs them on target PC
# Developed by: The greatest technician that ever lived

param(
    [string]$SourcePC,
    [string]$TargetPC,
    [PSCredential]$Credential,
    [string]$ExportPath = "C:\DriverExport",
    [switch]$ExportOnly,
    [switch]$InstallOnly
)

# Function to display banner
function Show-DriverBanner {
    Clear-Host
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "    Driver Export & Install Tool" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "Developed by: The greatest technician that ever lived" -ForegroundColor Green
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host ""
}

# Function to export drivers from source PC
function Export-DriversFromPC {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential,
        [string]$ExportPath
    )
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "EXPORTING DRIVERS FROM $ComputerName" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            # Local export
            Write-Host "Exporting drivers locally..." -ForegroundColor Yellow
            
            # Create export directory
            if (-not (Test-Path $ExportPath)) {
                New-Item -Path $ExportPath -ItemType Directory -Force | Out-Null
            }
            
            # Export all third-party drivers
            Write-Host "Running: dism /online /export-driver /destination:$ExportPath" -ForegroundColor Cyan
            $result = & dism /online /export-driver /destination:$ExportPath
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Drivers exported successfully to $ExportPath" -ForegroundColor Green
                
                # Get count of exported drivers
                $driverFolders = Get-ChildItem -Path $ExportPath -Directory -ErrorAction SilentlyContinue
                Write-Host "Exported $($driverFolders.Count) driver packages" -ForegroundColor Green
                
                return $true
            } else {
                Write-Host "Driver export failed with exit code: $LASTEXITCODE" -ForegroundColor Red
                return $false
            }
        } else {
            # Remote export
            Write-Host "Exporting drivers from remote computer..." -ForegroundColor Yellow
            
            $scriptBlock = {
                param($exportPath)
                
                # Create export directory
                if (-not (Test-Path $exportPath)) {
                    New-Item -Path $exportPath -ItemType Directory -Force | Out-Null
                }
                
                # Export all third-party drivers
                $result = & dism /online /export-driver /destination:$exportPath
                
                if ($LASTEXITCODE -eq 0) {
                    # Get count of exported drivers
                    $driverFolders = Get-ChildItem -Path $exportPath -Directory -ErrorAction SilentlyContinue
                    return @{
                        Success = $true
                        Count = $driverFolders.Count
                        Message = "Drivers exported successfully"
                    }
                } else {
                    return @{
                        Success = $false
                        Count = 0
                        Message = "Driver export failed with exit code: $LASTEXITCODE"
                    }
                }
            }
            
            if ($Credential) {
                $result = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock -ArgumentList $ExportPath
            } else {
                $result = Invoke-Command -ComputerName $ComputerName -ScriptBlock $scriptBlock -ArgumentList $ExportPath
            }
            
            if ($result.Success) {
                Write-Host "Drivers exported successfully to $ExportPath on $ComputerName" -ForegroundColor Green
                Write-Host "Exported $($result.Count) driver packages" -ForegroundColor Green
                return $true
            } else {
                Write-Host $result.Message -ForegroundColor Red
                return $false
            }
        }
    } catch {
        Write-Host "Error exporting drivers: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to copy drivers from source to target PC using PowerShell remoting
function Copy-DriversToTarget {
    param(
        [string]$SourcePC,
        [string]$TargetPC,
        [PSCredential]$Credential,
        [string]$ExportPath
    )

    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "COPYING DRIVERS TO TARGET PC" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan

    try {
        $targetLocalPath = "C:\DriverImport"

        Write-Host "Copying drivers from $SourcePC to $TargetPC using PowerShell remoting..." -ForegroundColor Yellow

        # Method 1: Use PowerShell remoting to copy files directly
        $scriptBlock = {
            param($sourcePC, $exportPath, $targetPath, $cred)

            # Create target directory
            if (-not (Test-Path $targetPath)) {
                New-Item -Path $targetPath -ItemType Directory -Force | Out-Null
            }

            # Create session to source PC
            $sourceSession = $null
            try {
                if ($cred) {
                    $sourceSession = New-PSSession -ComputerName $sourcePC -Credential $cred
                } else {
                    $sourceSession = New-PSSession -ComputerName $sourcePC
                }

                # Copy files using Copy-Item with session
                Copy-Item -Path "$exportPath\*" -Destination $targetPath -Recurse -Force -FromSession $sourceSession

                # Get count of copied files
                $copiedFiles = Get-ChildItem -Path $targetPath -Recurse -File -ErrorAction SilentlyContinue

                return @{
                    Success = $true
                    Message = "Successfully copied $($copiedFiles.Count) files"
                    FileCount = $copiedFiles.Count
                }
            } catch {
                return @{
                    Success = $false
                    Message = "Copy failed: $($_.Exception.Message)"
                    FileCount = 0
                }
            } finally {
                if ($sourceSession) {
                    Remove-PSSession -Session $sourceSession -ErrorAction SilentlyContinue
                }
            }
        }

        if ($Credential) {
            $result = Invoke-Command -ComputerName $TargetPC -Credential $Credential -ScriptBlock $scriptBlock -ArgumentList $SourcePC, $ExportPath, $targetLocalPath, $Credential
        } else {
            $result = Invoke-Command -ComputerName $TargetPC -ScriptBlock $scriptBlock -ArgumentList $SourcePC, $ExportPath, $targetLocalPath, $null
        }

        if ($result.Success) {
            Write-Host $result.Message -ForegroundColor Green
            return $true
        } else {
            Write-Host $result.Message -ForegroundColor Red

            # Fallback method: Try creating a compressed archive and copying
            Write-Host "Trying fallback method with compressed archive..." -ForegroundColor Yellow
            return Copy-DriversViaArchive -SourcePC $SourcePC -TargetPC $TargetPC -Credential $Credential -ExportPath $ExportPath
        }
    } catch {
        Write-Host "Error copying drivers: $($_.Exception.Message)" -ForegroundColor Red

        # Fallback method
        Write-Host "Trying fallback method with compressed archive..." -ForegroundColor Yellow
        return Copy-DriversViaArchive -SourcePC $SourcePC -TargetPC $TargetPC -Credential $Credential -ExportPath $ExportPath
    }
}

# Fallback function to copy drivers via compressed archive
function Copy-DriversViaArchive {
    param(
        [string]$SourcePC,
        [string]$TargetPC,
        [PSCredential]$Credential,
        [string]$ExportPath
    )

    try {
        $archivePath = "C:\DriverExport.zip"
        $targetLocalPath = "C:\DriverImport"

        Write-Host "Creating compressed archive on source PC..." -ForegroundColor Yellow

        # Create archive on source PC
        $createArchiveScript = {
            param($exportPath, $archivePath)

            if (Test-Path $archivePath) {
                Remove-Item $archivePath -Force
            }

            Compress-Archive -Path "$exportPath\*" -DestinationPath $archivePath -Force

            if (Test-Path $archivePath) {
                $archiveSize = (Get-Item $archivePath).Length / 1MB
                return @{Success = $true; Size = [math]::Round($archiveSize, 2)}
            } else {
                return @{Success = $false; Size = 0}
            }
        }

        if ($Credential) {
            $archiveResult = Invoke-Command -ComputerName $SourcePC -Credential $Credential -ScriptBlock $createArchiveScript -ArgumentList $ExportPath, $archivePath
        } else {
            $archiveResult = Invoke-Command -ComputerName $SourcePC -ScriptBlock $createArchiveScript -ArgumentList $ExportPath, $archivePath
        }

        if (-not $archiveResult.Success) {
            Write-Host "Failed to create archive on source PC" -ForegroundColor Red
            return $false
        }

        Write-Host "Archive created successfully ($($archiveResult.Size) MB)" -ForegroundColor Green

        # Copy archive to target PC and extract
        $copyAndExtractScript = {
            param($sourcePC, $archivePath, $targetPath, $cred)

            try {
                # Create target directory
                if (-not (Test-Path $targetPath)) {
                    New-Item -Path $targetPath -ItemType Directory -Force | Out-Null
                }

                # Create session to source PC and copy archive
                $sourceSession = $null
                if ($cred) {
                    $sourceSession = New-PSSession -ComputerName $sourcePC -Credential $cred
                } else {
                    $sourceSession = New-PSSession -ComputerName $sourcePC
                }

                $localArchivePath = "C:\DriverImport.zip"
                Copy-Item -Path $archivePath -Destination $localArchivePath -FromSession $sourceSession
                Remove-PSSession -Session $sourceSession

                # Extract archive
                Expand-Archive -Path $localArchivePath -DestinationPath $targetPath -Force

                # Clean up
                Remove-Item $localArchivePath -Force -ErrorAction SilentlyContinue

                # Get count of extracted files
                $extractedFiles = Get-ChildItem -Path $targetPath -Recurse -File -ErrorAction SilentlyContinue

                return @{
                    Success = $true
                    Message = "Successfully copied and extracted $($extractedFiles.Count) files"
                }
            } catch {
                return @{
                    Success = $false
                    Message = "Copy/extract failed: $($_.Exception.Message)"
                }
            }
        }

        if ($Credential) {
            $copyResult = Invoke-Command -ComputerName $TargetPC -Credential $Credential -ScriptBlock $copyAndExtractScript -ArgumentList $SourcePC, $archivePath, $targetLocalPath, $Credential
        } else {
            $copyResult = Invoke-Command -ComputerName $TargetPC -ScriptBlock $copyAndExtractScript -ArgumentList $SourcePC, $archivePath, $targetLocalPath, $null
        }

        if ($copyResult.Success) {
            Write-Host $copyResult.Message -ForegroundColor Green

            # Clean up archive on source PC
            if ($Credential) {
                Invoke-Command -ComputerName $SourcePC -Credential $Credential -ScriptBlock { param($path) Remove-Item $path -Force -ErrorAction SilentlyContinue } -ArgumentList $archivePath
            } else {
                Invoke-Command -ComputerName $SourcePC -ScriptBlock { param($path) Remove-Item $path -Force -ErrorAction SilentlyContinue } -ArgumentList $archivePath
            }

            return $true
        } else {
            Write-Host $copyResult.Message -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "Archive method failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to install drivers on target PC
function Install-DriversOnTarget {
    param(
        [string]$TargetPC,
        [PSCredential]$Credential,
        [string]$ImportPath = "C:\DriverImport"
    )
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "INSTALLING DRIVERS ON $TargetPC" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    try {
        if ($TargetPC -eq $env:COMPUTERNAME -or $TargetPC -eq "localhost" -or $TargetPC -eq ".") {
            # Local installation
            Write-Host "Installing drivers locally..." -ForegroundColor Yellow
            
            # Get all .inf files in the import directory
            $infFiles = Get-ChildItem -Path $ImportPath -Filter "*.inf" -Recurse -ErrorAction SilentlyContinue
            
            if ($infFiles.Count -eq 0) {
                Write-Host "No .inf files found in $ImportPath" -ForegroundColor Red
                return $false
            }
            
            Write-Host "Found $($infFiles.Count) driver .inf files" -ForegroundColor Cyan
            
            $successCount = 0
            $failCount = 0
            
            foreach ($infFile in $infFiles) {
                Write-Host "Installing driver: $($infFile.Name)" -ForegroundColor Yellow
                
                # Use pnputil to add driver
                $result = & pnputil /add-driver $infFile.FullName /install
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "  Successfully installed: $($infFile.Name)" -ForegroundColor Green
                    $successCount++
                } else {
                    Write-Host "  Failed to install: $($infFile.Name) (Exit code: $LASTEXITCODE)" -ForegroundColor Red
                    $failCount++
                }
            }
            
            Write-Host "`nInstallation Summary:" -ForegroundColor Cyan
            Write-Host "  Successfully installed: $successCount drivers" -ForegroundColor Green
            Write-Host "  Failed to install: $failCount drivers" -ForegroundColor Red
            
            return $successCount -gt 0
        } else {
            # Remote installation
            Write-Host "Installing drivers on remote computer..." -ForegroundColor Yellow
            
            $scriptBlock = {
                param($importPath)
                
                # Get all .inf files in the import directory
                $infFiles = Get-ChildItem -Path $importPath -Filter "*.inf" -Recurse -ErrorAction SilentlyContinue
                
                if ($infFiles.Count -eq 0) {
                    return @{Success = $false; Message = "No .inf files found in $importPath"}
                }
                
                $successCount = 0
                $failCount = 0
                $results = @()
                
                foreach ($infFile in $infFiles) {
                    # Use pnputil to add driver
                    $result = & pnputil /add-driver $infFile.FullName /install
                    
                    if ($LASTEXITCODE -eq 0) {
                        $successCount++
                        $results += "SUCCESS: $($infFile.Name)"
                    } else {
                        $failCount++
                        $results += "FAILED: $($infFile.Name) (Exit code: $LASTEXITCODE)"
                    }
                }
                
                return @{
                    Success = $successCount -gt 0
                    SuccessCount = $successCount
                    FailCount = $failCount
                    Results = $results
                    Message = "Processed $($infFiles.Count) driver files"
                }
            }
            
            if ($Credential) {
                $result = Invoke-Command -ComputerName $TargetPC -Credential $Credential -ScriptBlock $scriptBlock -ArgumentList $ImportPath
            } else {
                $result = Invoke-Command -ComputerName $TargetPC -ScriptBlock $scriptBlock -ArgumentList $ImportPath
            }
            
            Write-Host $result.Message -ForegroundColor Cyan
            Write-Host "Successfully installed: $($result.SuccessCount) drivers" -ForegroundColor Green
            Write-Host "Failed to install: $($result.FailCount) drivers" -ForegroundColor Red
            
            # Show detailed results
            foreach ($resultLine in $result.Results) {
                if ($resultLine.StartsWith("SUCCESS")) {
                    Write-Host "  $resultLine" -ForegroundColor Green
                } else {
                    Write-Host "  $resultLine" -ForegroundColor Red
                }
            }
            
            return $result.Success
        }
    } catch {
        Write-Host "Error installing drivers: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
Show-DriverBanner

# Get parameters if not provided
if (-not $SourcePC -and -not $InstallOnly) {
    $SourcePC = Read-Host "Enter the source PC name (to export drivers from)"
}

if (-not $TargetPC -and -not $ExportOnly) {
    $TargetPC = Read-Host "Enter the target PC name (to install drivers on)"
}

# Get credentials if needed
$needsCredentials = ($SourcePC -and $SourcePC -ne $env:COMPUTERNAME -and $SourcePC -ne "localhost" -and $SourcePC -ne ".") -or 
                   ($TargetPC -and $TargetPC -ne $env:COMPUTERNAME -and $TargetPC -ne "localhost" -and $TargetPC -ne ".")

if ($needsCredentials -and -not $Credential) {
    Write-Host "Remote computer access detected. Credentials required." -ForegroundColor Yellow
    $Credential = Get-Credential -Message "Enter credentials for remote computer access"
}

Write-Host "Operation Mode:" -ForegroundColor Cyan
if ($ExportOnly) {
    Write-Host "  Export drivers only from $SourcePC" -ForegroundColor Yellow
} elseif ($InstallOnly) {
    Write-Host "  Install drivers only on $TargetPC" -ForegroundColor Yellow
} else {
    Write-Host "  Full operation: Export from $SourcePC and install on $TargetPC" -ForegroundColor Yellow
}

$success = $true

# Step 1: Export drivers from source PC
if (-not $InstallOnly) {
    $exportSuccess = Export-DriversFromPC -ComputerName $SourcePC -Credential $Credential -ExportPath $ExportPath
    if (-not $exportSuccess) {
        Write-Host "Driver export failed. Cannot proceed with installation." -ForegroundColor Red
        $success = $false
    }
}

# Step 2: Copy drivers to target PC (if different from source)
if ($success -and -not $ExportOnly -and -not $InstallOnly -and $SourcePC -ne $TargetPC) {
    $copySuccess = Copy-DriversToTarget -SourcePC $SourcePC -TargetPC $TargetPC -Credential $Credential -ExportPath $ExportPath
    if (-not $copySuccess) {
        Write-Host "Driver copy failed. Cannot proceed with installation." -ForegroundColor Red
        $success = $false
    }
}

# Step 3: Install drivers on target PC
if ($success -and -not $ExportOnly) {
    $importPath = if ($SourcePC -eq $TargetPC) { $ExportPath } else { "C:\DriverImport" }
    $installSuccess = Install-DriversOnTarget -TargetPC $TargetPC -Credential $Credential -ImportPath $importPath
    if (-not $installSuccess) {
        Write-Host "Driver installation completed with some failures." -ForegroundColor Yellow
    }
}

Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "DRIVER OPERATION COMPLETE" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

if ($success) {
    Write-Host "Operation completed successfully!" -ForegroundColor Green
} else {
    Write-Host "Operation completed with errors." -ForegroundColor Red
}

Read-Host "`nPress Enter to exit"
