# Simple USB Test Script
# Developed by: The greatest technician that ever lived
# Purpose: Quick test of USB detection and source access

Write-Host "=== SIMPLE USB TEST ===" -ForegroundColor Cyan
Write-Host ""

# Test 1: Check if running as admin
Write-Host "1. Checking permissions..." -ForegroundColor Yellow
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if ($isAdmin) {
    Write-Host "   ✅ Running as Administrator" -ForegroundColor Green
} else {
    Write-Host "   ⚠️ Not running as Administrator (may cause issues)" -ForegroundColor Yellow
}

# Test 2: Find USB drives
Write-Host "2. Looking for USB drives..." -ForegroundColor Yellow
$usbDrives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 2 -and $_.Size -gt 0 }

if ($usbDrives.Count -eq 0) {
    Write-Host "   ❌ No USB drives found" -ForegroundColor Red
    Write-Host "   All drives found:" -ForegroundColor Yellow
    $allDrives = Get-WmiObject -Class Win32_LogicalDisk
    foreach ($drive in $allDrives) {
        $type = switch ($drive.DriveType) {
            2 { "USB/Removable" }
            3 { "Hard Drive" }
            4 { "Network" }
            5 { "CD/DVD" }
            default { "Other" }
        }
        $size = if ($drive.Size) { [math]::Round($drive.Size / 1GB, 2) } else { 0 }
        Write-Host "     $($drive.DeviceID) - $type - $size GB" -ForegroundColor Gray
    }
} else {
    Write-Host "   ✅ Found $($usbDrives.Count) USB drive(s):" -ForegroundColor Green
    foreach ($drive in $usbDrives) {
        $label = if ($drive.VolumeName) { $drive.VolumeName } else { "No Label" }
        $sizeGB = [math]::Round($drive.Size / 1GB, 2)
        $freeGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        Write-Host "     $($drive.DeviceID) [$label] ($sizeGB GB, $freeGB GB free)" -ForegroundColor Gray
    }
}

# Test 3: Check source directory
Write-Host "3. Checking source directory..." -ForegroundColor Yellow
$sourceBase = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Usb drive files"

if (Test-Path $sourceBase) {
    Write-Host "   ✅ Source directory found: $sourceBase" -ForegroundColor Green
    
    $items = Get-ChildItem -Path $sourceBase -ErrorAction SilentlyContinue
    if ($items) {
        Write-Host "   ✅ Found $($items.Count) items in source directory" -ForegroundColor Green
        
        # Calculate total size
        $totalSize = 0
        foreach ($item in $items) {
            if ($item.PSIsContainer) {
                $folderSize = (Get-ChildItem -Path $item.FullName -Recurse -File -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
                $totalSize += $folderSize
            } else {
                $totalSize += $item.Length
            }
        }
        $totalSizeGB = [math]::Round($totalSize / 1GB, 2)
        Write-Host "   📊 Total size: $totalSizeGB GB" -ForegroundColor Cyan
    } else {
        Write-Host "   ⚠️ Source directory is empty" -ForegroundColor Yellow
    }
} else {
    Write-Host "   ❌ Source directory not found: $sourceBase" -ForegroundColor Red
    
    # Check if base network path exists
    $networkBase = "\\storagehd\Desktopshare\Scripts\Andrew\Needed"
    if (Test-Path $networkBase) {
        Write-Host "   ✅ Base network path accessible: $networkBase" -ForegroundColor Green
        Write-Host "   💡 Run Copy-NetworkFiles.ps1 to create the USB files folder" -ForegroundColor Yellow
    } else {
        Write-Host "   ❌ Base network path not accessible: $networkBase" -ForegroundColor Red
        Write-Host "   💡 Check network connectivity and permissions" -ForegroundColor Yellow
    }
}

# Test 4: Test basic file operations
Write-Host "4. Testing file operations..." -ForegroundColor Yellow
$testFile = "$env:TEMP\usb-test-$(Get-Random).txt"
try {
    "Test content" | Out-File -FilePath $testFile
    $content = Get-Content -Path $testFile
    Remove-Item -Path $testFile -Force
    Write-Host "   ✅ File operations working" -ForegroundColor Green
}
catch {
    Write-Host "   ❌ File operations failed: $_" -ForegroundColor Red
}

# Summary and recommendations
Write-Host ""
Write-Host "=== SUMMARY ===" -ForegroundColor Cyan

if ($usbDrives.Count -eq 0) {
    Write-Host "❌ USB ISSUE: No USB drives detected" -ForegroundColor Red
    Write-Host "Solutions:" -ForegroundColor Yellow
    Write-Host "  1. Make sure USB drive is properly inserted" -ForegroundColor Gray
    Write-Host "  2. Try a different USB port" -ForegroundColor Gray
    Write-Host "  3. Check if drive is formatted (NTFS/FAT32)" -ForegroundColor Gray
    Write-Host "  4. Run as Administrator" -ForegroundColor Gray
}

if (-not (Test-Path $sourceBase)) {
    Write-Host "❌ SOURCE ISSUE: Source directory not accessible" -ForegroundColor Red
    Write-Host "Solutions:" -ForegroundColor Yellow
    Write-Host "  1. Run Copy-NetworkFiles.ps1 first" -ForegroundColor Gray
    Write-Host "  2. Check network connectivity" -ForegroundColor Gray
    Write-Host "  3. Verify network permissions" -ForegroundColor Gray
}

if ($usbDrives.Count -gt 0 -and (Test-Path $sourceBase)) {
    Write-Host "✅ ALL GOOD: USB drives found and source accessible" -ForegroundColor Green
    Write-Host "Copy-USBFiles.ps1 should work properly" -ForegroundColor Green
}

Write-Host ""
Write-Host "Press Enter to exit..." -ForegroundColor Gray
Read-Host
