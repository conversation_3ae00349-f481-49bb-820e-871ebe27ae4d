# Network Files Sync Script
# Developed by: The greatest technician that ever lived
# Purpose: Sync and update installer files in network locations

param(
    [Parameter(Mandatory=$false)]
    [switch]$WhatIf,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

# Color output function
function Write-Color {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

# Banner function
function Show-Banner {
    param([string]$Message, [string]$Color = "Cyan")
    $width = [Math]::Max(60, $Message.Length + 10)
    Write-Host ""
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ("*" + (" " * (($width - $Message.Length - 2) / 2)) + $Message + (" " * (($width - $Message.Length - 2) / 2)) + "*") -ForegroundColor $Color
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ""
}

# Check if file needs update
function Test-FileNeedsUpdate {
    param(
        [string]$Source,
        [string]$Destination
    )
    
    if (-not (Test-Path $Destination)) {
        return $true  # Destination doesn't exist
    }
    
    if (-not (Test-Path $Source)) {
        return $false  # Source doesn't exist
    }
    
    $sourceInfo = Get-Item $Source
    $destInfo = Get-Item $Destination
    
    # Compare last write time
    if ($sourceInfo.LastWriteTime -gt $destInfo.LastWriteTime) {
        return $true  # Source is newer
    }
    
    # Compare size for files
    if (-not $sourceInfo.PSIsContainer -and $sourceInfo.Length -ne $destInfo.Length) {
        return $true  # Different size
    }
    
    return $false  # No update needed
}

# Sync function with verification
function Sync-WithVerification {
    param(
        [string]$Source,
        [string]$Destination,
        [string]$Name,
        [bool]$IsFolder = $false
    )
    
    Write-Color "Checking $Name..." "Yellow"
    
    if (-not (Test-Path $Source)) {
        Write-Color "  ❌ Source not found: $Source" "Red"
        return $false
    }
    
    # Check if update is needed
    $needsUpdate = $Force -or (Test-FileNeedsUpdate -Source $Source -Destination $Destination)
    
    if (-not $needsUpdate) {
        Write-Color "  ✅ Up to date (skipped)" "Green"
        return $true
    }
    
    Write-Color "  📁 Updating..." "Cyan"
    Write-Color "    Source: $Source" "Gray"
    Write-Color "    Destination: $Destination" "Gray"
    
    if ($WhatIf) {
        Write-Color "    ℹ️ WHAT-IF: Would update $Destination" "Cyan"
        return $true
    }
    
    try {
        if ($IsFolder) {
            # Create destination directory
            if (-not (Test-Path $Destination)) {
                New-Item -Path $Destination -ItemType Directory -Force | Out-Null
            }
            
            # Use robocopy with mirror option for folders
            $result = Start-Process -FilePath "robocopy" -ArgumentList "`"$Source`"", "`"$Destination`"", "/MIR", "/R:3", "/W:10", "/NP" -Wait -PassThru -NoNewWindow
            if ($result.ExitCode -le 7) {
                Write-Color "    ✅ Folder synced successfully" "Green"
                return $true
            } else {
                Write-Color "    ❌ Robocopy failed with exit code: $($result.ExitCode)" "Red"
                return $false
            }
        } else {
            # Create destination directory for file
            $destDir = Split-Path $Destination -Parent
            if (-not (Test-Path $destDir)) {
                New-Item -Path $destDir -ItemType Directory -Force | Out-Null
            }
            
            # Copy single file
            Copy-Item -Path $Source -Destination $Destination -Force
            Write-Color "    ✅ File synced successfully" "Green"
            return $true
        }
    }
    catch {
        Write-Color "    ❌ Sync failed: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Main execution
try {
    Show-Banner "NETWORK FILES SYNC UTILITY" "Green"
    
    if ($WhatIf) {
        Write-Color "WHAT-IF MODE: No files will actually be synced" "Yellow"
    }
    if ($Force) {
        Write-Color "FORCE MODE: All files will be updated regardless of timestamps" "Yellow"
    }
    Write-Host ""
    
    # Define source and destination paths
    $networkBase = "\\storagehd\Desktopshare\Scripts\Andrew\Needed"
    $usbBase = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Usb drive files"

    # Known source mappings for original files (from external sources)
    $knownSources = @{
        "Splashtop" = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\Splashtop_Push"
        "sxs" = "\\bay-msfsnas01\data\FS\PUBLIC\installdotnet3_5\Win11\sxs"
        "4.9_LTSR2" = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\4.9_LTSR2"
        "2203_LTSR" = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\2203_LTSR"
        "Nuance" = "\\bay-msfsnas01\data\FS\Source\nuance"
        "CiscoVPN" = "\\mhc-msassccm1\sources\apps\msi\Cisco\Secure_Client\5_1_0_136"
        "Win_Photo_Viewer.reg" = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Win_Photo_Viewer.reg"
        "Volume.ps1" = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Volume.ps1"
        "BitLockerAD.ps1" = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\BitLockerAD.ps1"
        "Commandupdate.EXE" = "\\bay-msfsnas01\data\FS\Source\Commandupdate.EXE"
        "jre1.7.0_45.msi" = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\WSP\7\jre1.7.0_45.msi"
        "GoogleChromeStandaloneEnterprise64.msi" = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\Google\GoogleChromeStandaloneEnterprise64.msi"
        "o365" = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\o365"
        "ImprivataAgent_x64.msi" = "$networkBase\ImprivataAgent_x64.msi"
        "LexmarkGDI" = "$networkBase\LexmarkGDI"
        "ZebraDriver" = "$networkBase\ZebraDriver"
    }

    # Auto-discover files in network base and USB base
    Write-Color "Discovering files in network and USB locations..." "Yellow"

    $allFiles = @()

    # Discover files in network base
    if (Test-Path $networkBase) {
        $networkItems = Get-ChildItem -Path $networkBase -ErrorAction SilentlyContinue | Where-Object { $_.Name -ne "Usb drive files" }
        foreach ($item in $networkItems) {
            $itemName = $item.Name
            $networkPath = $item.FullName
            $usbPath = "$usbBase\$itemName"

            # Determine source (external source or network location)
            $source = if ($knownSources.ContainsKey($itemName)) { $knownSources[$itemName] } else { $networkPath }

            # Add to network sync (from external source to network)
            if ($knownSources.ContainsKey($itemName)) {
                $allFiles += @{ Name = $itemName; Source = $source; Dest = $networkPath; IsFolder = $item.PSIsContainer; Category = "Network" }
            }

            # Add to USB sync (from network to USB)
            $allFiles += @{ Name = "$itemName (USB)"; Source = $networkPath; Dest = $usbPath; IsFolder = $item.PSIsContainer; Category = "USB" }
        }
    }

    # Discover additional files in USB base that might not be in network base
    if (Test-Path $usbBase) {
        $usbItems = Get-ChildItem -Path $usbBase -ErrorAction SilentlyContinue
        foreach ($item in $usbItems) {
            $itemName = $item.Name
            $networkPath = "$networkBase\$itemName"
            $usbPath = $item.FullName

            # If this item doesn't exist in network base, add it as USB-only
            if (-not (Test-Path $networkPath)) {
                Write-Color "  Found USB-only item: $itemName" "Cyan"
                # For USB-only items, we can't sync from external source, so we skip network sync
                # This allows for manual additions to USB folder
            }
        }
    }
    
    # Sync network files (from external sources to network location)
    $networkFiles = $allFiles | Where-Object { $_.Category -eq "Network" }
    Write-Color "[1/2] Syncing from external sources to network location..." "Cyan"
    Write-Color "Found $($networkFiles.Count) items with external sources" "Gray"
    $networkSuccess = 0

    foreach ($file in $networkFiles) {
        if (Sync-WithVerification -Source $file.Source -Destination $file.Dest -Name $file.Name -IsFolder $file.IsFolder) {
            $networkSuccess++
        }
        Write-Host ""
    }

    # Sync USB files (from network location to USB staging)
    $usbFiles = $allFiles | Where-Object { $_.Category -eq "USB" }
    Write-Color "[2/2] Syncing from network location to USB staging..." "Cyan"
    Write-Color "Found $($usbFiles.Count) items to sync to USB staging" "Gray"
    $usbSuccess = 0

    foreach ($file in $usbFiles) {
        if (Sync-WithVerification -Source $file.Source -Destination $file.Dest -Name $file.Name -IsFolder $file.IsFolder) {
            $usbSuccess++
        }
        Write-Host ""
    }

    # Check for new items in USB folder that aren't in network
    Write-Color "[3/3] Checking for new items in USB staging area..." "Cyan"
    $newItemsCount = 0

    if (Test-Path $usbBase) {
        $usbItems = Get-ChildItem -Path $usbBase -ErrorAction SilentlyContinue
        foreach ($item in $usbItems) {
            $networkEquivalent = "$networkBase\$($item.Name)"
            if (-not (Test-Path $networkEquivalent)) {
                Write-Color "  📁 New item found in USB staging: $($item.Name)" "Yellow"
                Write-Color "    Consider adding this to the main network location if it should be managed" "Gray"
                $newItemsCount++
            }
        }
    }

    if ($newItemsCount -eq 0) {
        Write-Color "No new items found in USB staging area" "Green"
    } else {
        Write-Color "Found $newItemsCount new items in USB staging area" "Yellow"
    }
    
    # Summary
    Show-Banner "SYNC OPERATION SUMMARY" "Green"
    Write-Color "External → Network: $networkSuccess/$($networkFiles.Count) successful" "$(if ($networkSuccess -eq $networkFiles.Count) { 'Green' } else { 'Yellow' })"
    Write-Color "Network → USB Staging: $usbSuccess/$($usbFiles.Count) successful" "$(if ($usbSuccess -eq $usbFiles.Count) { 'Green' } else { 'Yellow' })"
    Write-Color "New Items Detected: $newItemsCount" "$(if ($newItemsCount -eq 0) { 'Green' } else { 'Yellow' })"
    Write-Color "Total Operations: $($networkSuccess + $usbSuccess)/$($allFiles.Count) successful" "$(if (($networkSuccess + $usbSuccess) -eq $allFiles.Count) { 'Green' } else { 'Yellow' })"
    
    if ($WhatIf) {
        Write-Host ""
        Write-Color "This was a WHAT-IF run. Use without -WhatIf to actually sync files." "Yellow"
    }
}
catch {
    Write-Color "Script failed: $($_.Exception.Message)" "Red"
    exit 1
}

Write-Host ""
Write-Color "Script completed. Press Enter to exit..." "Gray"
Read-Host
