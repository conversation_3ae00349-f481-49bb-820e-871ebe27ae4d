@echo off
title Adobe Acrobat DC Remote Installer

REM Silent installation for remote deployment
REM This script is designed to run silently without user interaction

echo [%date% %time%] Starting Adobe Acrobat DC installation...

REM Create temp directory
if not exist "%TEMP%\AdobeInstaller" (
    md "%TEMP%\AdobeInstaller"
)

echo [%date% %time%] Downloading Adobe Acrobat DC...
curl --ssl-no-revoke --silent --output "%TEMP%\AdobeInstaller\Acrobat_DC_Web_x64_WWMUI.zip" https://trials.adobe.com/AdobeProducts/APRO/Acrobat_HelpX/win32/Acrobat_DC_Web_x64_WWMUI.zip

REM Check if download was successful
if not exist "%TEMP%\AdobeInstaller\Acrobat_DC_Web_x64_WWMUI.zip" (
    echo [%date% %time%] ERROR: Failed to download Adobe Acrobat DC
    exit /b 1
)

echo [%date% %time%] Download completed successfully
echo [%date% %time%] Extracting installer...
tar -xf "%TEMP%\AdobeInstaller\Acrobat_DC_Web_x64_WWMUI.zip" -C "%TEMP%\AdobeInstaller" >nul 2>&1
del /f "%TEMP%\AdobeInstaller\Acrobat_DC_Web_x64_WWMUI.zip" >nul 2>&1

echo [%date% %time%] Starting silent installation...
REM Run silent installation
"%TEMP%\AdobeInstaller\Adobe Acrobat\setup.exe" /sAll /rs /msi EULA_ACCEPT=YES

REM Wait for installation to complete
echo [%date% %time%] Waiting for installation to complete...
:WaitForInstall
timeout /t 10 /nobreak >nul 2>&1
tasklist /fi "imagename eq setup.exe" 2>nul | find /i "setup.exe" >nul
if %errorlevel%==0 goto WaitForInstall

REM Additional wait for MSI processes
:WaitForMSI
timeout /t 5 /nobreak >nul 2>&1
tasklist /fi "imagename eq msiexec.exe" 2>nul | find /i "msiexec.exe" >nul
if %errorlevel%==0 goto WaitForMSI

echo [%date% %time%] Installation completed
echo [%date% %time%] Cleaning up temporary files...
rmdir /s /q "%TEMP%\AdobeInstaller" >nul 2>&1

REM Verify installation
if exist "C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe" (
    echo [%date% %time%] Adobe Acrobat DC installation successful
    exit /b 0
) else (
    echo [%date% %time%] Adobe Acrobat DC installation may have failed
    exit /b 1
)
