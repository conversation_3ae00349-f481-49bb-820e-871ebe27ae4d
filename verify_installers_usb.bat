@echo off
setlocal enabledelayedexpansion

:: ===============================================================================
:: INSTALLER FILES VERIFICATION UTILITY WITH USB DRIVE SELECTION
:: Verifies that all required installer files are present on selected drive
:: ===============================================================================

echo.
echo ********************************************************************************
echo *                 INSTALLER FILES VERIFICATION UTILITY                       *
echo *                                                                              *
echo * This utility verifies that all required installer files are present        *
echo * on a selected drive for use with the migration script.                      *
echo ********************************************************************************
echo.

:: Detect and select drive
call :DetectDrives
if %errorlevel% neq 0 exit /b 1

set "missing_files=0"
set "total_files=0"
set "present_files=0"

echo.
echo Checking for required installer files on %SELECTED_DRIVE%...
echo.

:: Check folders
call :CheckFolder "%SELECTED_DRIVE%\Splashtop_Push" "Splashtop"
call :CheckFolder "%SELECTED_DRIVE%\sxs" ".NET 3.5 SXS"
call :CheckFolder "%SELECTED_DRIVE%\Lexmark_Universal_v2_UD1_PostScript_3_Emulation" "Lexmark Universal Driver"
call :CheckFolder "%SELECTED_DRIVE%\o365" "Office 365"
call :CheckFolder "%SELECTED_DRIVE%\4.9_LTSR2" "Citrix 4.9 LTSR2"
call :CheckFolder "%SELECTED_DRIVE%\Nuance121" "Nuance"
call :CheckFolder "%SELECTED_DRIVE%\FSTools" "FSTools"
call :CheckFolder "%SELECTED_DRIVE%\LexmarkGDI" "Lexmark GDI Driver"
call :CheckFolder "%SELECTED_DRIVE%\Imprivata_push" "Imprivata Push"

:: Check individual files
call :CheckFile "%SELECTED_DRIVE%\jre1.7.0_45.msi" "Java RE 7"
call :CheckFile "%SELECTED_DRIVE%\GoogleChromeStandaloneEnterprise64.msi" "Google Chrome"
call :CheckFile "%SELECTED_DRIVE%\Win_Photo_Viewer.reg" "Windows Photo Viewer Registry"
call :CheckFile "%SELECTED_DRIVE%\Volume.ps1" "Volume PowerShell Script"
call :CheckFile "%SELECTED_DRIVE%\BitLockerAD.ps1" "BitLocker PowerShell Script"
call :CheckFile "%SELECTED_DRIVE%\Commandupdate.EXE" "Dell Command Update"

echo.
echo ********************************************************************************
echo *                           VERIFICATION RESULTS                              *
echo ********************************************************************************
echo.
echo Total items checked: %total_files%
echo Missing items: %missing_files%
echo Present items: %present_files%
echo.

if %missing_files% equ 0 (
    echo ✓ SUCCESS: All required installer files are present on %SELECTED_DRIVE%!
    echo   You can now use these files with the migration script.
    echo.
    echo   Estimated total size on disk:
    call :GetFolderSize "%SELECTED_DRIVE%\Splashtop_Push"
    call :GetFolderSize "%SELECTED_DRIVE%\o365"
    call :GetFolderSize "%SELECTED_DRIVE%\4.9_LTSR2"
    call :GetFolderSize "%SELECTED_DRIVE%\Lexmark_Universal_v2_UD1_PostScript_3_Emulation"
    call :GetFolderSize "%SELECTED_DRIVE%\LexmarkGDI"
) else (
    echo ✗ WARNING: %missing_files% required files/folders are missing!
    echo   Please run the copy utility to download missing files to %SELECTED_DRIVE%.
    echo.
    echo   Missing files need to be copied before using local installer option.
)

echo.
echo Verification completed: %date% %time%
echo Selected drive: %SELECTED_DRIVE%
echo.
echo NEXT STEPS:
if %missing_files% equ 0 (
    echo 1. If files are on USB drive, copy them to D:\ on target computer
    echo 2. Run migration script and select "Yes" for local installer files
    echo 3. Migration script will automatically use D:\ drive
) else (
    echo 1. Run copy utility to download missing files
    echo 2. Verify again before proceeding with migration
)
echo.
pause
goto :eof

:: ===============================================================================
:: FUNCTIONS
:: ===============================================================================

:DetectDrives
echo Detecting available drives...
echo.
set "drive_count=0"
set "drives_list="

:: Check for removable drives (USB) first
echo REMOVABLE DRIVES (USB/External):
call :CheckDriveType 2 "USB/Removable"

:: Check for fixed drives (excluding C:)
echo.
echo FIXED DRIVES (Local Hard Drives):
call :CheckDriveType 3 "Fixed Drive"

if %drive_count% equ 0 (
    echo ERROR: No suitable drives found!
    echo Please connect a USB drive or ensure other drives are available.
    pause
    exit /b 1
)

echo.
echo [0] Exit
echo.
set /p drive_choice="Please select a drive to verify (1-%drive_count% or 0 to exit): "

if "%drive_choice%"=="0" (
    echo Operation cancelled by user.
    exit /b 1
)

if %drive_choice% lss 1 if %drive_choice% gtr %drive_count% (
    echo Invalid selection. Please try again.
    goto :DetectDrives
)

:: Get selected drive
set "counter=0"
for %%d in (%drives_list%) do (
    set /a counter+=1
    if !counter! equ %drive_choice% (
        set "SELECTED_DRIVE=%%d"
        goto :DriveSelected
    )
)

:DriveSelected
echo.
echo Selected drive for verification: %SELECTED_DRIVE%
goto :eof

:CheckDriveType
set "drivetype=%~1"
set "typename=%~2"

:: Loop through all drive letters
for %%d in (D E F G H I J K L M N O P Q R S T U V W X Y Z) do (
    if exist %%d:\ (
        :: Get drive type using fsutil
        for /f "tokens=2 delims=:" %%t in ('fsutil fsinfo drivetype %%d: 2^>nul') do (
            set "detected_type=%%t"
            set "detected_type=!detected_type: =!"

            :: Check if this matches the requested drive type
            if "%drivetype%"=="2" if /i "!detected_type!"=="Removable" (
                call :AddDrive "%%d:" "%typename%"
            )
            if "%drivetype%"=="3" if /i "!detected_type!"=="Fixed" (
                if /i not "%%d:"=="C:" (
                    call :AddDrive "%%d:" "%typename%"
                )
            )
        )
    )
)
goto :eof

:AddDrive
set "drive=%~1"
set "type=%~2"

:: Get volume label and size info
for /f "tokens=3" %%s in ('dir %drive% ^| find "bytes free"') do set "freespace=%%s"
for /f "tokens=1" %%s in ('dir %drive% ^| find "bytes free"') do set "totalspace=%%s"

:: Get volume label
for /f "tokens=5*" %%v in ('vol %drive% 2^>nul') do set "volumename=%%v %%w"

:: Clean up the values
set "freespace=%freespace:,=%"
set "totalspace=%totalspace:,=%"
if "%volumename%"==" " set "volumename="

set /a drive_count+=1
set "drives_list=!drives_list! %drive%"

call :DisplayDriveInfo "!drive_count!" "%drive%" "!freespace!" "!totalspace!" "%type%" "!volumename!"
goto :eof

:DisplayDriveInfo
set "num=%~1"
set "drive=%~2"
set "freespace=%~3"
set "totalspace=%~4"
set "type=%~5"
set "volumename=%~6"

:: Convert bytes to GB
set /a freespace_gb=%freespace%/1073741824 2>nul
set /a totalspace_gb=%totalspace%/1073741824 2>nul

:: Handle conversion errors
if "%freespace_gb%"=="" set "freespace_gb=0"
if "%totalspace_gb%"=="" set "totalspace_gb=0"

:: Display drive info with volume name if available
if "%volumename%" neq "" (
    echo [%num%] %drive% "%volumename%" (%type%) - %freespace_gb% GB free / %totalspace_gb% GB total
) else (
    echo [%num%] %drive% (%type%) - %freespace_gb% GB free / %totalspace_gb% GB total
)
goto :eof

:CheckFolder
set /a total_files+=1
if exist "%~1\*" (
    echo ✓ FOUND: %~2 folder
    set /a present_files+=1
) else (
    echo ✗ MISSING: %~2 folder (%~1)
    set /a missing_files+=1
)
goto :eof

:CheckFile
set /a total_files+=1
if exist "%~1" (
    echo ✓ FOUND: %~2 file
    set /a present_files+=1
) else (
    echo ✗ MISSING: %~2 file (%~1)
    set /a missing_files+=1
)
goto :eof

:GetFolderSize
if exist "%~1" (
    for /f "tokens=3" %%a in ('dir "%~1" /s /-c ^| find "File(s)"') do (
        set size=%%a
        set size=!size:,=!
        if !size! gtr 1073741824 (
            set /a size_gb=!size!/1073741824
            echo   %~1: ~!size_gb! GB
        ) else if !size! gtr 1048576 (
            set /a size_mb=!size!/1048576
            echo   %~1: ~!size_mb! MB
        ) else (
            set /a size_kb=!size!/1024
            echo   %~1: ~!size_kb! KB
        )
    )
)
goto :eof
