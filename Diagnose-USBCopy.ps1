# USB Copy Diagnostic Script
# Developed by: The greatest technician that ever lived
# Purpose: Diagnose issues with USB copy operations

# Color output function
function Write-Color {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

Write-Color "=== USB COPY DIAGNOSTIC TOOL ===" "Cyan"
Write-Host ""

# Test 1: PowerShell Version and Permissions
Write-Color "Test 1: PowerShell Environment" "Yellow"
Write-Color "PowerShell Version: $($PSVersionTable.PSVersion)" "Gray"
Write-Color "Execution Policy: $(Get-ExecutionPolicy)" "Gray"

try {
    $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object System.Security.Principal.WindowsPrincipal($currentUser)
    $isAdmin = $principal.IsInRole([System.Security.Principal.WindowsBuiltInRole]::Administrator)
    Write-Color "Running as Administrator: $isAdmin" "$(if ($isAdmin) { 'Green' } else { 'Yellow' })"
}
catch {
    Write-Color "Could not check admin status: $_" "Red"
}
Write-Host ""

# Test 2: USB Drive Detection
Write-Color "Test 2: USB Drive Detection" "Yellow"

# Method 1: WMI
try {
    $wmiDrives = Get-WmiObject -Class Win32_LogicalDisk -ErrorAction Stop | Where-Object {
        $_.DriveType -eq 2 -and $_.Size -gt 0
    }
    Write-Color "WMI Method: Found $($wmiDrives.Count) USB drives" "Green"
    foreach ($drive in $wmiDrives) {
        $label = if ($drive.VolumeName) { $drive.VolumeName } else { "No Label" }
        $sizeGB = [math]::Round($drive.Size / 1GB, 2)
        $freeGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        Write-Color "  $($drive.DeviceID) [$label] ($sizeGB GB, $freeGB GB free)" "Gray"
    }
}
catch {
    Write-Color "WMI Method Failed: $_" "Red"
}

# Method 2: Get-Volume
try {
    $volumes = Get-Volume -ErrorAction Stop | Where-Object {
        $_.DriveType -eq "Removable" -and $_.DriveLetter -and $_.Size -gt 0
    }
    Write-Color "Get-Volume Method: Found $($volumes.Count) removable volumes" "Green"
    foreach ($vol in $volumes) {
        $sizeGB = [math]::Round($vol.Size / 1GB, 2)
        $label = if ($vol.FileSystemLabel) { $vol.FileSystemLabel } else { "No Label" }
        Write-Color "  $($vol.DriveLetter): [$label] ($sizeGB GB)" "Gray"
    }
}
catch {
    Write-Color "Get-Volume Method Failed: $_" "Red"
}

# Show all drives for reference
Write-Color "All Logical Drives:" "Cyan"
try {
    $allDrives = Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, DriveType, VolumeName, Size, FreeSpace
    foreach ($drive in $allDrives) {
        $driveTypeText = switch ($drive.DriveType) {
            2 { "Removable (USB)" }
            3 { "Fixed (Hard Drive)" }
            4 { "Network" }
            5 { "CD-ROM" }
            default { "Unknown ($($drive.DriveType))" }
        }
        $sizeText = if ($drive.Size) { "$(([math]::Round($drive.Size / 1GB, 2))) GB" } else { "No Size" }
        $freeText = if ($drive.FreeSpace) { "$(([math]::Round($drive.FreeSpace / 1GB, 2))) GB free" } else { "No Free Space" }
        $label = if ($drive.VolumeName) { $drive.VolumeName } else { "No Label" }
        Write-Color "  $($drive.DeviceID) - $driveTypeText - [$label] - $sizeText - $freeText" "Gray"
    }
}
catch {
    Write-Color "Could not enumerate all drives: $_" "Red"
}
Write-Host ""

# Test 3: Source Directory Check
Write-Color "Test 3: Source Directory Access" "Yellow"
$sourceBase = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Usb drive files"
Write-Color "Testing path: $sourceBase" "Gray"

if (Test-Path $sourceBase) {
    Write-Color "✅ Source directory exists and is accessible" "Green"
    
    try {
        $items = Get-ChildItem -Path $sourceBase -ErrorAction Stop
        Write-Color "Found $($items.Count) items in source directory" "Green"
        
        # Calculate total size
        $totalSize = 0
        $fileCount = 0
        $folderCount = 0
        
        foreach ($item in $items) {
            if ($item.PSIsContainer) {
                $folderCount++
                try {
                    $folderSize = (Get-ChildItem -Path $item.FullName -Recurse -File -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
                    $totalSize += $folderSize
                    $sizeText = "$(([math]::Round($folderSize / 1MB, 2))) MB"
                    Write-Color "  📁 $($item.Name) - $sizeText" "Gray"
                }
                catch {
                    Write-Color "  📁 $($item.Name) - Could not calculate size" "Yellow"
                }
            } else {
                $fileCount++
                $totalSize += $item.Length
                $sizeText = "$(([math]::Round($item.Length / 1MB, 2))) MB"
                Write-Color "  📄 $($item.Name) - $sizeText" "Gray"
            }
        }
        
        $totalSizeGB = [math]::Round($totalSize / 1GB, 2)
        Write-Color "Summary: $folderCount folders, $fileCount files, $totalSizeGB GB total" "Cyan"
    }
    catch {
        Write-Color "❌ Error reading source directory contents: $_" "Red"
    }
} else {
    Write-Color "❌ Source directory not accessible" "Red"
    Write-Color "Possible causes:" "Yellow"
    Write-Color "1. Network path not available" "Gray"
    Write-Color "2. Insufficient permissions" "Gray"
    Write-Color "3. Copy-NetworkFiles.ps1 not run yet" "Gray"
    
    # Test network connectivity
    Write-Color "Testing network connectivity..." "Yellow"
    $networkBase = "\\storagehd\Desktopshare\Scripts\Andrew\Needed"
    if (Test-Path $networkBase) {
        Write-Color "✅ Base network path is accessible" "Green"
        $usbFolder = "$networkBase\Usb drive files"
        if (Test-Path $usbFolder) {
            Write-Color "✅ USB drive files folder exists" "Green"
        } else {
            Write-Color "❌ USB drive files folder missing" "Red"
            Write-Color "Run Copy-NetworkFiles.ps1 to create this folder" "Yellow"
        }
    } else {
        Write-Color "❌ Base network path not accessible" "Red"
    }
}
Write-Host ""

# Test 4: File Operations Test
Write-Color "Test 4: File Operations Test" "Yellow"
$testFile = "$env:TEMP\usb-copy-test.txt"
$testContent = "USB Copy Test - $(Get-Date)"

try {
    # Test file creation
    $testContent | Out-File -FilePath $testFile -Encoding UTF8
    Write-Color "✅ Can create test files" "Green"
    
    # Test file reading
    $readContent = Get-Content -Path $testFile -Raw
    if ($readContent.Trim() -eq $testContent) {
        Write-Color "✅ Can read test files" "Green"
    } else {
        Write-Color "❌ File read/write mismatch" "Red"
    }
    
    # Clean up
    Remove-Item -Path $testFile -Force -ErrorAction SilentlyContinue
    Write-Color "✅ Can delete test files" "Green"
}
catch {
    Write-Color "❌ File operations failed: $_" "Red"
}
Write-Host ""

# Test 5: Robocopy Availability
Write-Color "Test 5: Robocopy Availability" "Yellow"
try {
    $robocopyPath = Get-Command robocopy -ErrorAction Stop
    Write-Color "✅ Robocopy found at: $($robocopyPath.Source)" "Green"
    
    # Test robocopy version
    $robocopyVersion = & robocopy /? 2>&1 | Select-String "ROBOCOPY" | Select-Object -First 1
    Write-Color "Version: $robocopyVersion" "Gray"
}
catch {
    Write-Color "❌ Robocopy not found or not accessible" "Red"
}
Write-Host ""

Write-Color "=== DIAGNOSTIC COMPLETE ===" "Cyan"
Write-Color "If issues were found above, address them before running Copy-USBFiles.ps1" "Yellow"

Write-Host ""
Write-Color "Press Enter to exit..." "Gray"
Read-Host
