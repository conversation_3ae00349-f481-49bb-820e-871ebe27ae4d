# 3rd Party Driver Comparison and Transfer Script
# Compares 3rd party add-on drivers (scanners, printers, dongles, etc.) between two PCs and transfers missing ones
# Also checks for TWAIN software since TWAIN drivers often don't appear in standard driver enumeration
# Filters out Microsoft, Intel, AMD, NVIDIA and generic hardware drivers
# Developed by: The greatest technician that ever lived

param(
    [string]$CopyFromPC,
    [string]$CopyToPC,
    [PSCredential]$Credential
)

# Function to display banner
function Show-DriverBanner {
    Clear-Host
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "  3rd Party Driver Comparison & Transfer" -ForegroundColor Yellow
    Write-Host "  (Scanners, TWAIN, Printers, Dongles, etc.)" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "Developed by: The greatest technician that ever lived" -ForegroundColor Green
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host ""
}

# Function to test PC connectivity
function Test-PCConnectivity {
    param([string]$ComputerName)
    
    Write-Host "Testing connectivity to $ComputerName..." -ForegroundColor Yellow
    
    if (Test-Connection -ComputerName $ComputerName -Count 2 -Quiet) {
        Write-Host "[SUCCESS] $ComputerName is online" -ForegroundColor Green
        return $true
    } else {
        Write-Host "[ERROR] $ComputerName is offline or unreachable" -ForegroundColor Red
        return $false
    }
}

# Function to get installed drivers
function Get-InstalledDrivers {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )
    
    Write-Host "Gathering installed drivers from $ComputerName..." -ForegroundColor Yellow
    
    try {
        $drivers = @()
        
        # Check if we're querying the local computer
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            Write-Host "  Querying local drivers..." -ForegroundColor Cyan
            
            # Get drivers locally - filter for 3rd party add-on devices only
            $drivers = Get-WmiObject -Class Win32_PnPSignedDriver -ErrorAction SilentlyContinue |
                Where-Object {
                    $_.DeviceName -and
                    $_.DriverVersion -and
                    $_.Manufacturer -and
                    # Include 3rd party manufacturers
                    $_.Manufacturer -notlike "Microsoft*" -and
                    $_.Manufacturer -notlike "Intel*" -and
                    $_.Manufacturer -notlike "AMD*" -and
                    $_.Manufacturer -notlike "NVIDIA*" -and
                    $_.Manufacturer -notlike "*Standard*" -and
                    $_.Manufacturer -notlike "*Generic*" -and
                    # Include specific device types (add-ons)
                    ($_.DeviceName -like "*Scanner*" -or
                     $_.DeviceName -like "*Printer*" -or
                     $_.DeviceName -like "*TWAIN*" -or
                     $_.DeviceName -like "*Imaging*" -or
                     $_.DeviceName -like "*Document*" -or
                     $_.DeviceName -like "*USB*" -or
                     $_.DeviceName -like "*Dongle*" -or
                     $_.DeviceName -like "*Adapter*" -or
                     $_.DeviceName -like "*Converter*" -or
                     $_.DeviceName -like "*Reader*" -or
                     $_.DeviceName -like "*Webcam*" -or
                     $_.DeviceName -like "*Camera*" -or
                     $_.DeviceName -like "*Headset*" -or
                     $_.DeviceName -like "*Microphone*" -or
                     $_.DeviceName -like "*Speaker*" -or
                     $_.DeviceName -like "*Audio*" -or
                     $_.DeviceName -like "*Bluetooth*" -or
                     $_.DeviceName -like "*Wireless*" -or
                     $_.DeviceName -like "*Network*" -or
                     $_.DeviceName -like "*Modem*" -or
                     $_.DeviceName -like "*Serial*" -or
                     $_.DeviceName -like "*Parallel*" -or
                     $_.DeviceName -like "*HID*" -or
                     $_.DeviceName -like "*Touch*" -or
                     $_.DeviceName -like "*Tablet*" -or
                     $_.DeviceName -like "*Pen*" -or
                     $_.DeviceName -like "*Stylus*")
                } |
                Select-Object DeviceName, DriverVersion, DriverDate, Manufacturer
        } else {
            Write-Host "  Trying WMI query on remote computer..." -ForegroundColor Cyan
            
            # Try WMI for remote computer
            try {
                if ($Credential) {
                    $drivers = Get-WmiObject -Class Win32_PnPSignedDriver -ComputerName $ComputerName -Credential $Credential -ErrorAction Stop |
                        Where-Object {
                            $_.DeviceName -and
                            $_.DriverVersion -and
                            $_.Manufacturer -and
                            # Include 3rd party manufacturers
                            $_.Manufacturer -notlike "Microsoft*" -and
                            $_.Manufacturer -notlike "Intel*" -and
                            $_.Manufacturer -notlike "AMD*" -and
                            $_.Manufacturer -notlike "NVIDIA*" -and
                            $_.Manufacturer -notlike "*Standard*" -and
                            $_.Manufacturer -notlike "*Generic*" -and
                            # Include specific device types (add-ons)
                            ($_.DeviceName -like "*Scanner*" -or
                             $_.DeviceName -like "*Printer*" -or
                             $_.DeviceName -like "*TWAIN*" -or
                             $_.DeviceName -like "*Imaging*" -or
                             $_.DeviceName -like "*Document*" -or
                             $_.DeviceName -like "*USB*" -or
                             $_.DeviceName -like "*Dongle*" -or
                             $_.DeviceName -like "*Adapter*" -or
                             $_.DeviceName -like "*Converter*" -or
                             $_.DeviceName -like "*Reader*" -or
                             $_.DeviceName -like "*Webcam*" -or
                             $_.DeviceName -like "*Camera*" -or
                             $_.DeviceName -like "*Headset*" -or
                             $_.DeviceName -like "*Microphone*" -or
                             $_.DeviceName -like "*Speaker*" -or
                             $_.DeviceName -like "*Audio*" -or
                             $_.DeviceName -like "*Bluetooth*" -or
                             $_.DeviceName -like "*Wireless*" -or
                             $_.DeviceName -like "*Network*" -or
                             $_.DeviceName -like "*Modem*" -or
                             $_.DeviceName -like "*Serial*" -or
                             $_.DeviceName -like "*Parallel*" -or
                             $_.DeviceName -like "*HID*" -or
                             $_.DeviceName -like "*Touch*" -or
                             $_.DeviceName -like "*Tablet*" -or
                             $_.DeviceName -like "*Pen*" -or
                             $_.DeviceName -like "*Stylus*")
                        } |
                        Select-Object DeviceName, DriverVersion, DriverDate, Manufacturer
                } else {
                    $drivers = Get-WmiObject -Class Win32_PnPSignedDriver -ComputerName $ComputerName -ErrorAction Stop |
                        Where-Object {
                            $_.DeviceName -and
                            $_.DriverVersion -and
                            $_.Manufacturer -and
                            # Include 3rd party manufacturers
                            $_.Manufacturer -notlike "Microsoft*" -and
                            $_.Manufacturer -notlike "Intel*" -and
                            $_.Manufacturer -notlike "AMD*" -and
                            $_.Manufacturer -notlike "NVIDIA*" -and
                            $_.Manufacturer -notlike "*Standard*" -and
                            $_.Manufacturer -notlike "*Generic*" -and
                            # Include specific device types (add-ons)
                            ($_.DeviceName -like "*Scanner*" -or
                             $_.DeviceName -like "*Printer*" -or
                             $_.DeviceName -like "*TWAIN*" -or
                             $_.DeviceName -like "*Imaging*" -or
                             $_.DeviceName -like "*Document*" -or
                             $_.DeviceName -like "*USB*" -or
                             $_.DeviceName -like "*Dongle*" -or
                             $_.DeviceName -like "*Adapter*" -or
                             $_.DeviceName -like "*Converter*" -or
                             $_.DeviceName -like "*Reader*" -or
                             $_.DeviceName -like "*Webcam*" -or
                             $_.DeviceName -like "*Camera*" -or
                             $_.DeviceName -like "*Headset*" -or
                             $_.DeviceName -like "*Microphone*" -or
                             $_.DeviceName -like "*Speaker*" -or
                             $_.DeviceName -like "*Audio*" -or
                             $_.DeviceName -like "*Bluetooth*" -or
                             $_.DeviceName -like "*Wireless*" -or
                             $_.DeviceName -like "*Network*" -or
                             $_.DeviceName -like "*Modem*" -or
                             $_.DeviceName -like "*Serial*" -or
                             $_.DeviceName -like "*Parallel*" -or
                             $_.DeviceName -like "*HID*" -or
                             $_.DeviceName -like "*Touch*" -or
                             $_.DeviceName -like "*Tablet*" -or
                             $_.DeviceName -like "*Pen*" -or
                             $_.DeviceName -like "*Stylus*")
                        } |
                        Select-Object DeviceName, DriverVersion, DriverDate, Manufacturer
                }
                Write-Host "  WMI query successful!" -ForegroundColor Green
            } catch {
                Write-Host "  WMI query failed: $($_.Exception.Message)" -ForegroundColor Red
                
                # Fallback to PowerShell remoting
                Write-Host "  Trying PowerShell remoting..." -ForegroundColor Yellow
                try {
                    if ($Credential) {
                        $drivers = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock {
                            Get-WmiObject -Class Win32_PnPSignedDriver |
                            Where-Object {
                                $_.DeviceName -and
                                $_.DriverVersion -and
                                $_.Manufacturer -and
                                # Include 3rd party manufacturers
                                $_.Manufacturer -notlike "Microsoft*" -and
                                $_.Manufacturer -notlike "Intel*" -and
                                $_.Manufacturer -notlike "AMD*" -and
                                $_.Manufacturer -notlike "NVIDIA*" -and
                                $_.Manufacturer -notlike "*Standard*" -and
                                $_.Manufacturer -notlike "*Generic*" -and
                                # Include specific device types (add-ons)
                                ($_.DeviceName -like "*Scanner*" -or
                                 $_.DeviceName -like "*Printer*" -or
                                 $_.DeviceName -like "*TWAIN*" -or
                                 $_.DeviceName -like "*Imaging*" -or
                                 $_.DeviceName -like "*Document*" -or
                                 $_.DeviceName -like "*USB*" -or
                                 $_.DeviceName -like "*Dongle*" -or
                                 $_.DeviceName -like "*Adapter*" -or
                                 $_.DeviceName -like "*Converter*" -or
                                 $_.DeviceName -like "*Reader*" -or
                                 $_.DeviceName -like "*Webcam*" -or
                                 $_.DeviceName -like "*Camera*" -or
                                 $_.DeviceName -like "*Headset*" -or
                                 $_.DeviceName -like "*Microphone*" -or
                                 $_.DeviceName -like "*Speaker*" -or
                                 $_.DeviceName -like "*Audio*" -or
                                 $_.DeviceName -like "*Bluetooth*" -or
                                 $_.DeviceName -like "*Wireless*" -or
                                 $_.DeviceName -like "*Network*" -or
                                 $_.DeviceName -like "*Modem*" -or
                                 $_.DeviceName -like "*Serial*" -or
                                 $_.DeviceName -like "*Parallel*" -or
                                 $_.DeviceName -like "*HID*" -or
                                 $_.DeviceName -like "*Touch*" -or
                                 $_.DeviceName -like "*Tablet*" -or
                                 $_.DeviceName -like "*Pen*" -or
                                 $_.DeviceName -like "*Stylus*")
                            } |
                            Select-Object DeviceName, DriverVersion, DriverDate, Manufacturer
                        } -ErrorAction Stop
                    }
                    Write-Host "  PowerShell remoting successful!" -ForegroundColor Green
                } catch {
                    Write-Host "  PowerShell remoting failed: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
        
        Write-Host "Found $($drivers.Count) drivers on $ComputerName" -ForegroundColor Green
        
        if ($drivers.Count -eq 0) {
            Write-Host "WARNING: No drivers found. This might indicate permission issues." -ForegroundColor Yellow
        }
        
        return $drivers
        
    } catch {
        Write-Host "Error gathering drivers from $ComputerName`: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

# Function to compare drivers
function Compare-Drivers {
    param(
        [array]$SourceDrivers,
        [array]$TargetDrivers,
        [string]$SourcePCName,
        [string]$TargetPCName
    )
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "DRIVER COMPARISON RESULTS" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    # Find drivers missing on target PC
    $missingDrivers = @()
    foreach ($sourceDriver in $SourceDrivers) {
        $found = $false
        foreach ($targetDriver in $TargetDrivers) {
            if ($sourceDriver.DeviceName -eq $targetDriver.DeviceName) {
                $found = $true
                break
            }
        }
        if (-not $found) {
            $missingDrivers += $sourceDriver
        }
    }
    
    Write-Host "Drivers on ${SourcePCName} (Copy FROM): $($SourceDrivers.Count)" -ForegroundColor Cyan
    Write-Host "Drivers on ${TargetPCName} (Copy TO): $($TargetDrivers.Count)" -ForegroundColor Cyan
    Write-Host "Missing on ${TargetPCName}: $($missingDrivers.Count)" -ForegroundColor Red
    
    if ($missingDrivers.Count -gt 0) {
        Write-Host "`nMissing drivers on ${TargetPCName}:" -ForegroundColor Red
        $missingDrivers | Sort-Object DeviceName | ForEach-Object {
            Write-Host "  - $($_.DeviceName) (v$($_.DriverVersion))" -ForegroundColor White
        }
    } else {
        Write-Host "`nNo missing drivers found!" -ForegroundColor Green
    }
    
    return $missingDrivers
}

# Function to export drivers from source PC
function Export-DriversFromSource {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential,
        [string]$ExportPath = "C:\DriverExport"
    )
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "EXPORTING DRIVERS FROM $ComputerName" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    Write-Host "Export path: $ExportPath" -ForegroundColor Cyan
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            # Local export
            Write-Host "Exporting drivers locally..." -ForegroundColor Yellow
            
            # Create export directory
            if (-not (Test-Path $ExportPath)) {
                New-Item -Path $ExportPath -ItemType Directory -Force | Out-Null
                Write-Host "  Created export directory: $ExportPath" -ForegroundColor Green
            }
            
            # Export all third-party drivers
            Write-Host "  Running DISM export command..." -ForegroundColor Cyan
            $result = & dism /online /export-driver /destination:$ExportPath
            
            if ($LASTEXITCODE -eq 0) {
                # Get count of exported drivers
                $driverFolders = Get-ChildItem -Path $ExportPath -Directory -ErrorAction SilentlyContinue
                Write-Host "  Successfully exported $($driverFolders.Count) driver packages" -ForegroundColor Green
                return @{Success = $true; Count = $driverFolders.Count; Path = $ExportPath}
            } else {
                Write-Host "  Driver export failed with exit code: $LASTEXITCODE" -ForegroundColor Red
                return @{Success = $false; Count = 0; Path = $ExportPath}
            }
        } else {
            # Remote export
            Write-Host "Exporting drivers from remote computer..." -ForegroundColor Yellow
            
            $scriptBlock = {
                param($exportPath)
                
                # Create export directory
                if (-not (Test-Path $exportPath)) {
                    New-Item -Path $exportPath -ItemType Directory -Force | Out-Null
                }
                
                # Export all third-party drivers
                $result = & dism /online /export-driver /destination:$exportPath
                
                if ($LASTEXITCODE -eq 0) {
                    # Get count of exported drivers
                    $driverFolders = Get-ChildItem -Path $exportPath -Directory -ErrorAction SilentlyContinue
                    return @{
                        Success = $true
                        Count = $driverFolders.Count
                        Message = "Drivers exported successfully"
                        Path = $exportPath
                    }
                } else {
                    return @{
                        Success = $false
                        Count = 0
                        Message = "Driver export failed with exit code: $LASTEXITCODE"
                        Path = $exportPath
                    }
                }
            }
            
            if ($Credential) {
                $result = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock -ArgumentList $ExportPath
            } else {
                $result = Invoke-Command -ComputerName $ComputerName -ScriptBlock $scriptBlock -ArgumentList $ExportPath
            }
            
            if ($result.Success) {
                Write-Host "  Successfully exported $($result.Count) driver packages" -ForegroundColor Green
                return $result
            } else {
                Write-Host "  $($result.Message)" -ForegroundColor Red
                return $result
            }
        }
    } catch {
        Write-Host "  Error exporting drivers: $($_.Exception.Message)" -ForegroundColor Red
        return @{Success = $false; Count = 0; Path = $ExportPath}
    }
}

# Function to copy drivers to target PC
function Copy-DriversToTarget {
    param(
        [string]$SourcePC,
        [string]$TargetPC,
        [PSCredential]$Credential,
        [string]$SourcePath,
        [string]$TargetPath = "C:\DriverImport"
    )
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "COPYING DRIVERS TO TARGET PC" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    Write-Host "Source: $SourcePC`:$SourcePath" -ForegroundColor Cyan
    Write-Host "Target: $TargetPC`:$TargetPath" -ForegroundColor Cyan
    
    try {
        # Create compressed archive on source PC
        $archivePath = "C:\DriverExport.zip"
        
        Write-Host "Creating compressed archive on source PC..." -ForegroundColor Yellow
        
        $createArchiveScript = {
            param($sourcePath, $archivePath)
            
            if (Test-Path $archivePath) {
                Remove-Item $archivePath -Force
            }
            
            Compress-Archive -Path "$sourcePath\*" -DestinationPath $archivePath -Force
            
            if (Test-Path $archivePath) {
                $archiveSize = (Get-Item $archivePath).Length / 1MB
                return @{Success = $true; Size = [math]::Round($archiveSize, 2)}
            } else {
                return @{Success = $false; Size = 0}
            }
        }
        
        if ($Credential) {
            $archiveResult = Invoke-Command -ComputerName $SourcePC -Credential $Credential -ScriptBlock $createArchiveScript -ArgumentList $SourcePath, $archivePath
        } else {
            $archiveResult = Invoke-Command -ComputerName $SourcePC -ScriptBlock $createArchiveScript -ArgumentList $SourcePath, $archivePath
        }
        
        if (-not $archiveResult.Success) {
            Write-Host "  Failed to create archive on source PC" -ForegroundColor Red
            return $false
        }
        
        Write-Host "  Archive created successfully ($($archiveResult.Size) MB)" -ForegroundColor Green
        
        # Copy archive to target PC and extract
        Write-Host "Copying and extracting archive on target PC..." -ForegroundColor Yellow
        
        $copyAndExtractScript = {
            param($sourcePC, $archivePath, $targetPath, $cred)
            
            try {
                # Create target directory
                if (-not (Test-Path $targetPath)) {
                    New-Item -Path $targetPath -ItemType Directory -Force | Out-Null
                }
                
                # Create session to source PC and copy archive
                $sourceSession = $null
                if ($cred) {
                    $sourceSession = New-PSSession -ComputerName $sourcePC -Credential $cred
                } else {
                    $sourceSession = New-PSSession -ComputerName $sourcePC
                }
                
                $localArchivePath = "C:\DriverImport.zip"
                Copy-Item -Path $archivePath -Destination $localArchivePath -FromSession $sourceSession
                Remove-PSSession -Session $sourceSession
                
                # Extract archive
                Expand-Archive -Path $localArchivePath -DestinationPath $targetPath -Force
                
                # Clean up
                Remove-Item $localArchivePath -Force -ErrorAction SilentlyContinue
                
                # Get count of extracted files
                $extractedFiles = Get-ChildItem -Path $targetPath -Recurse -File -ErrorAction SilentlyContinue
                
                return @{
                    Success = $true
                    Message = "Successfully copied and extracted $($extractedFiles.Count) files"
                    FileCount = $extractedFiles.Count
                }
            } catch {
                return @{
                    Success = $false
                    Message = "Copy/extract failed: $($_.Exception.Message)"
                    FileCount = 0
                }
            }
        }
        
        if ($Credential) {
            $copyResult = Invoke-Command -ComputerName $TargetPC -Credential $Credential -ScriptBlock $copyAndExtractScript -ArgumentList $SourcePC, $archivePath, $TargetPath, $Credential
        } else {
            $copyResult = Invoke-Command -ComputerName $TargetPC -ScriptBlock $copyAndExtractScript -ArgumentList $SourcePC, $archivePath, $TargetPath, $null
        }
        
        if ($copyResult.Success) {
            Write-Host "  $($copyResult.Message)" -ForegroundColor Green
            
            # Clean up archive on source PC
            Write-Host "Cleaning up temporary files..." -ForegroundColor Yellow
            if ($Credential) {
                Invoke-Command -ComputerName $SourcePC -Credential $Credential -ScriptBlock { param($path) Remove-Item $path -Force -ErrorAction SilentlyContinue } -ArgumentList $archivePath
            } else {
                Invoke-Command -ComputerName $SourcePC -ScriptBlock { param($path) Remove-Item $path -Force -ErrorAction SilentlyContinue } -ArgumentList $archivePath
            }
            
            return @{Success = $true; TargetPath = $TargetPath; FileCount = $copyResult.FileCount}
        } else {
            Write-Host "  $($copyResult.Message)" -ForegroundColor Red
            return @{Success = $false; TargetPath = $TargetPath; FileCount = 0}
        }
    } catch {
        Write-Host "  Copy operation failed: $($_.Exception.Message)" -ForegroundColor Red
        return @{Success = $false; TargetPath = $TargetPath; FileCount = 0}
    }
}

# Function to install drivers on target PC
function Install-DriversOnTarget {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential,
        [string]$ImportPath = "C:\DriverImport"
    )

    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "INSTALLING DRIVERS ON $ComputerName" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan

    Write-Host "Import path: $ImportPath" -ForegroundColor Cyan

    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            # Local installation
            Write-Host "Installing drivers locally..." -ForegroundColor Yellow

            # Get all .inf files in the import directory
            $infFiles = Get-ChildItem -Path $ImportPath -Filter "*.inf" -Recurse -ErrorAction SilentlyContinue

            if ($infFiles.Count -eq 0) {
                Write-Host "  No .inf files found in $ImportPath" -ForegroundColor Red
                return @{Success = $false; SuccessCount = 0; FailCount = 0}
            }

            Write-Host "  Found $($infFiles.Count) driver .inf files" -ForegroundColor Cyan

            $successCount = 0
            $failCount = 0

            foreach ($infFile in $infFiles) {
                Write-Host "  Installing driver: $($infFile.Name)" -ForegroundColor Yellow

                # Use pnputil to add driver
                $result = & pnputil /add-driver $infFile.FullName /install 2>&1

                if ($LASTEXITCODE -eq 0) {
                    Write-Host "    SUCCESS: $($infFile.Name)" -ForegroundColor Green
                    $successCount++
                } else {
                    Write-Host "    FAILED: $($infFile.Name) (Exit code: $LASTEXITCODE)" -ForegroundColor Red
                    $failCount++
                }
            }

            return @{Success = $successCount -gt 0; SuccessCount = $successCount; FailCount = $failCount}
        } else {
            # Remote installation
            Write-Host "Installing drivers on remote computer..." -ForegroundColor Yellow

            $scriptBlock = {
                param($importPath)

                # Get all .inf files in the import directory
                $infFiles = Get-ChildItem -Path $importPath -Filter "*.inf" -Recurse -ErrorAction SilentlyContinue

                if ($infFiles.Count -eq 0) {
                    return @{Success = $false; Message = "No .inf files found in $importPath"; SuccessCount = 0; FailCount = 0}
                }

                $successCount = 0
                $failCount = 0
                $results = @()

                foreach ($infFile in $infFiles) {
                    # Use pnputil to add driver
                    $result = & pnputil /add-driver $infFile.FullName /install 2>&1

                    if ($LASTEXITCODE -eq 0) {
                        $successCount++
                        $results += "SUCCESS: $($infFile.Name)"
                    } else {
                        $failCount++
                        $results += "FAILED: $($infFile.Name) (Exit code: $LASTEXITCODE)"
                    }
                }

                return @{
                    Success = $successCount -gt 0
                    SuccessCount = $successCount
                    FailCount = $failCount
                    Results = $results
                    Message = "Processed $($infFiles.Count) driver files"
                }
            }

            if ($Credential) {
                $result = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock -ArgumentList $ImportPath
            } else {
                $result = Invoke-Command -ComputerName $ComputerName -ScriptBlock $scriptBlock -ArgumentList $ImportPath
            }

            Write-Host "  $($result.Message)" -ForegroundColor Cyan

            # Show detailed results
            foreach ($resultLine in $result.Results) {
                if ($resultLine.StartsWith("SUCCESS")) {
                    Write-Host "    $resultLine" -ForegroundColor Green
                } else {
                    Write-Host "    $resultLine" -ForegroundColor Red
                }
            }

            return $result
        }
    } catch {
        Write-Host "  Error installing drivers: $($_.Exception.Message)" -ForegroundColor Red
        return @{Success = $false; SuccessCount = 0; FailCount = 0}
    }
}

# Main execution
Show-DriverBanner

# Get PC names if not provided
if (-not $CopyFromPC) {
    Write-Host "Enter the computer names for driver comparison and transfer:" -ForegroundColor Cyan
    $CopyFromPC = Read-Host "Copy FROM PC (source - has the drivers)"
}

if (-not $CopyToPC) {
    $CopyToPC = Read-Host "Copy TO PC (target - needs the drivers)"
}

# Get credentials if needed
$needsCredentials = ($CopyFromPC -ne $env:COMPUTERNAME -and $CopyFromPC -ne "localhost" -and $CopyFromPC -ne ".") -or
                   ($CopyToPC -ne $env:COMPUTERNAME -and $CopyToPC -ne "localhost" -and $CopyToPC -ne ".")

if ($needsCredentials -and -not $Credential) {
    Write-Host "`nRemote computer access detected. Credentials required." -ForegroundColor Yellow
    $Credential = Get-Credential -Message "Enter credentials for remote computer access"
    if (-not $Credential) {
        Write-Host "Credentials are required for remote access. Exiting." -ForegroundColor Red
        exit 1
    }
}

Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "CONFIGURATION SUMMARY" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host "Copy FROM PC (source): $CopyFromPC" -ForegroundColor Cyan
Write-Host "Copy TO PC (target): $CopyToPC" -ForegroundColor Cyan
Write-Host "Credentials provided: $(if($Credential){'Yes'}else{'No'})" -ForegroundColor Cyan

# Test connectivity to both PCs
Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "CONNECTIVITY TEST" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

$sourceOnline = Test-PCConnectivity -ComputerName $CopyFromPC
$targetOnline = Test-PCConnectivity -ComputerName $CopyToPC

if (-not $sourceOnline -or -not $targetOnline) {
    Write-Host "`nCannot proceed - one or both PCs are offline" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Gather drivers from both PCs
Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "GATHERING DRIVER DATA" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

$sourceDrivers = Get-InstalledDrivers -ComputerName $CopyFromPC -Credential $Credential
$targetDrivers = Get-InstalledDrivers -ComputerName $CopyToPC -Credential $Credential

if ($sourceDrivers.Count -eq 0) {
    Write-Host "No drivers found on source PC. Cannot proceed." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Compare drivers
$missingDrivers = Compare-Drivers -SourceDrivers $sourceDrivers -TargetDrivers $targetDrivers -SourcePCName $CopyFromPC -TargetPCName $CopyToPC

if ($missingDrivers.Count -eq 0) {
    Write-Host "`nNo missing drivers found. Both computers have the same drivers!" -ForegroundColor Green
    Read-Host "Press Enter to exit"
    exit 0
}

# Ask for confirmation to proceed with driver transfer
Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "DRIVER TRANSFER CONFIRMATION" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

Write-Host "The following actions will be performed:" -ForegroundColor White
Write-Host "1. Export ALL drivers from $CopyFromPC" -ForegroundColor White
Write-Host "2. Copy driver files to $CopyToPC" -ForegroundColor White
Write-Host "3. Install missing drivers on $CopyToPC" -ForegroundColor White
Write-Host ""
Write-Host "Missing drivers to be installed: $($missingDrivers.Count)" -ForegroundColor Yellow

$proceed = Read-Host "Do you want to proceed with driver export and transfer? (y/n)"

if ($proceed -ne 'y') {
    Write-Host "Operation cancelled by user." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 0
}

# Step 1: Export drivers from source PC
$exportResult = Export-DriversFromSource -ComputerName $CopyFromPC -Credential $Credential

if (-not $exportResult.Success) {
    Write-Host "Driver export failed. Cannot proceed with transfer." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 2: Copy drivers to target PC
$copyResult = Copy-DriversToTarget -SourcePC $CopyFromPC -TargetPC $CopyToPC -Credential $Credential -SourcePath $exportResult.Path

if (-not $copyResult.Success) {
    Write-Host "Driver copy failed. Cannot proceed with installation." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 3: Install drivers on target PC
$installConfirm = Read-Host "`nProceed with driver installation on $CopyToPC? (y/n)"

if ($installConfirm -eq 'y') {
    $installResult = Install-DriversOnTarget -ComputerName $CopyToPC -Credential $Credential -ImportPath $copyResult.TargetPath

    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "INSTALLATION SUMMARY" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan

    Write-Host "Successfully installed: $($installResult.SuccessCount) drivers" -ForegroundColor Green
    Write-Host "Failed installations: $($installResult.FailCount) drivers" -ForegroundColor Red

    if ($installResult.SuccessCount -gt 0) {
        $restartChoice = Read-Host "`nRestart $CopyToPC to complete driver installation? (y/n)"
        if ($restartChoice -eq 'y') {
            Write-Host "Restarting $CopyToPC..." -ForegroundColor Yellow
            try {
                if ($Credential) {
                    Invoke-Command -ComputerName $CopyToPC -Credential $Credential -ScriptBlock { Restart-Computer -Force }
                } else {
                    Invoke-Command -ComputerName $CopyToPC -ScriptBlock { Restart-Computer -Force }
                }
                Write-Host "Restart command sent successfully" -ForegroundColor Green
            } catch {
                Write-Host "Failed to restart: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
} else {
    Write-Host "Driver installation skipped." -ForegroundColor Yellow
}

Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "DRIVER TRANSFER COMPLETE" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

Write-Host "Operation completed successfully!" -ForegroundColor Green
Write-Host "Drivers exported: $($exportResult.Count)" -ForegroundColor White
Write-Host "Files copied: $($copyResult.FileCount)" -ForegroundColor White

Read-Host "`nPress Enter to exit"
