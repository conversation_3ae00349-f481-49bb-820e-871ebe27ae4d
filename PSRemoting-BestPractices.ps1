# PSRemoting Best Practices for Persistent Sessions
# This shows better ways to maintain remote connections

# Example of improved session management
function Connect-WithPersistence {
    param([string]$TargetComputer, [PSCredential]$Credential)
    
    # Test if PSRemoting is already configured
    try {
        $testSession = New-PSSession -ComputerName $TargetComputer -Credential $Credential -ErrorAction Stop
        Remove-PSSession $testSession
        Write-Host "✓ PSRemoting already configured on $TargetComputer" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "PSRemoting needs setup on $TargetComputer" -ForegroundColor Yellow
        return $false
    }
}

# One-time PSRemoting configuration (run this once per computer)
function Configure-PSRemotingPermanently {
    param([string]$TargetComputer, [PSCredential]$Credential)
    
    try {
        $SessionArgs = @{
            ComputerName = $TargetComputer
            Credential = $Credential
            SessionOption = New-CimSessionOption -Protocol Dcom
        }
        $cimSession = New-CimSession @SessionArgs
        
        # Configure PSRemoting to stay enabled
        $configScript = @"
Enable-PSRemoting -Force -SkipNetworkProfileCheck
Set-Service -Name WinRM -StartupType Automatic
Set-Item WSMan:\localhost\Shell\IdleTimeout -Value 0
Set-Item WSMan:\localhost\Shell\MaxShellRunTime -Value 0
Restart-Service WinRM -Force
"@
        
        $MethodArgs = @{
            ClassName = 'Win32_Process'
            MethodName = 'Create'
            CimSession = $cimSession
            Arguments = @{ CommandLine = "powershell -Command `"$configScript`"" }
        }
        
        Invoke-CimMethod @MethodArgs
        Remove-CimSession $cimSession
        Start-Sleep -Seconds 20
        
        Write-Host "✓ PSRemoting configured permanently on $TargetComputer" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ Failed to configure PSRemoting: $_" -ForegroundColor Red
        return $false
    }
}

# Enhanced session creation with maximum persistence
function New-PersistentSession {
    param([string]$TargetComputer, [PSCredential]$Credential)
    
    $sessionOption = New-PSSessionOption `
        -IdleTimeout ([int]::MaxValue) `
        -OpenTimeout 60000 `
        -OperationTimeout 600000 `
        -MaximumReceivedDataSizePerCommand 500MB `
        -NoMachineProfile
    
    try {
        $session = New-PSSession -ComputerName $TargetComputer -Credential $Credential -SessionOption $sessionOption
        Write-Host "✓ Persistent session created (ID: $($session.Id))" -ForegroundColor Green
        return $session
    }
    catch {
        Write-Host "✗ Failed to create session: $_" -ForegroundColor Red
        return $null
    }
}

# Example usage
Write-Host "PSRemoting Best Practices" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Key Improvements:" -ForegroundColor Yellow
Write-Host "1. Configure PSRemoting once and leave it enabled" -ForegroundColor White
Write-Host "2. Use persistent sessions with maximum timeouts" -ForegroundColor White
Write-Host "3. Test existing configuration before enabling" -ForegroundColor White
Write-Host "4. Don't disable PSRemoting after use" -ForegroundColor White
Write-Host ""
Write-Host "Benefits:" -ForegroundColor Yellow
Write-Host "• Faster connections (no setup delay)" -ForegroundColor Green
Write-Host "• More reliable operations" -ForegroundColor Green
Write-Host "• Better session reuse" -ForegroundColor Green
Write-Host "• Reduced network overhead" -ForegroundColor Green
