# Test script to verify credential-based remote access
param(
    [string]$ComputerName = "brbr319edt724d"
)

Write-Host "Testing credential-based remote access to: $ComputerName" -ForegroundColor Cyan
Write-Host "============================================" -ForegroundColor Cyan

# Get credentials
Write-Host "`nPlease enter credentials with administrative access:" -ForegroundColor Yellow
$Credential = Get-Credential -Message "Enter domain credentials"

if (-not $Credential) {
    Write-Host "Credentials required. Exiting." -ForegroundColor Red
    exit 1
}

# Test 1: WMI with credentials
Write-Host "`n1. Testing WMI with credentials..." -ForegroundColor Yellow
try {
    $os = Get-WmiObject -Class Win32_OperatingSystem -ComputerName $ComputerName -Credential $Credential -ErrorAction Stop
    Write-Host "   SUCCESS: WMI access with credentials works!" -ForegroundColor Green
    Write-Host "   OS: $($os.Caption)" -ForegroundColor White
    Write-Host "   Computer: $($os.CSName)" -ForegroundColor White
} catch {
    Write-Host "   FAILED: WMI with credentials failed - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: PowerShell Remoting with credentials
Write-Host "`n2. Testing PowerShell Remoting with credentials..." -ForegroundColor Yellow
try {
    $result = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock { 
        $env:COMPUTERNAME 
    } -ErrorAction Stop
    Write-Host "   SUCCESS: PowerShell Remoting with credentials works!" -ForegroundColor Green
    Write-Host "   Connected to: $result" -ForegroundColor White
} catch {
    Write-Host "   FAILED: PowerShell Remoting with credentials failed - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Get applications with credentials
Write-Host "`n3. Testing application enumeration with credentials..." -ForegroundColor Yellow
try {
    $apps = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock {
        Get-ItemProperty "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue |
        Where-Object { $_.DisplayName -and $_.DisplayName -notlike "KB*" } |
        Select-Object DisplayName, DisplayVersion -First 5
    } -ErrorAction Stop
    
    Write-Host "   SUCCESS: Application enumeration works!" -ForegroundColor Green
    Write-Host "   Sample applications found:" -ForegroundColor White
    $apps | ForEach-Object {
        Write-Host "     - $($_.DisplayName) ($($_.DisplayVersion))" -ForegroundColor White
    }
} catch {
    Write-Host "   FAILED: Application enumeration failed - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Get drivers with credentials
Write-Host "`n4. Testing driver enumeration with credentials..." -ForegroundColor Yellow
try {
    $drivers = Get-WmiObject -Class Win32_PnPSignedDriver -ComputerName $ComputerName -Credential $Credential -ErrorAction Stop |
        Where-Object { $_.DeviceName -and $_.DriverVersion } |
        Select-Object DeviceName, DriverVersion -First 5
    
    Write-Host "   SUCCESS: Driver enumeration works!" -ForegroundColor Green
    Write-Host "   Sample drivers found:" -ForegroundColor White
    $drivers | ForEach-Object {
        Write-Host "     - $($_.DeviceName) (v$($_.DriverVersion))" -ForegroundColor White
    }
} catch {
    Write-Host "   FAILED: Driver enumeration failed - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n============================================" -ForegroundColor Cyan
Write-Host "Credential test complete" -ForegroundColor Cyan
