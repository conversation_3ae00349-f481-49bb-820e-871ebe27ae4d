# Simple USB Copy Test Script
# Developed by: The greatest technician that ever lived
# Purpose: Test USB drive detection and basic copy functionality

# Color output function
function Write-Color {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

Write-Color "=== USB DRIVE DETECTION TEST ===" "Cyan"
Write-Host ""

# Test 1: Basic drive detection
Write-Color "Test 1: Detecting all drives..." "Yellow"
try {
    $allDrives = Get-WmiObject -Class Win32_LogicalDisk
    Write-Color "Found $($allDrives.Count) total drives:" "Green"
    
    foreach ($drive in $allDrives) {
        $driveTypeText = switch ($drive.DriveType) {
            2 { "Removable (USB)" }
            3 { "Fixed (Hard Drive)" }
            4 { "Network" }
            5 { "CD-ROM" }
            default { "Unknown ($($drive.DriveType))" }
        }
        $sizeText = if ($drive.Size) { "$(([math]::Round($drive.Size / 1GB, 2))) GB" } else { "No Size" }
        $freeText = if ($drive.FreeSpace) { "$(([math]::Round($drive.FreeSpace / 1GB, 2))) GB free" } else { "No Free Space Info" }
        $label = if ($drive.VolumeName) { $drive.VolumeName } else { "No Label" }
        
        Write-Color "  $($drive.DeviceID) - $driveTypeText - [$label] - $sizeText - $freeText" "Gray"
    }
}
catch {
    Write-Color "❌ Error detecting drives: $_" "Red"
}

Write-Host ""

# Test 2: USB-specific detection
Write-Color "Test 2: Detecting USB drives only..." "Yellow"
try {
    $usbDrives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {
        $_.DriveType -eq 2 -and $_.Size -gt 0
    }
    
    if ($usbDrives.Count -eq 0) {
        Write-Color "❌ No USB drives found with DriveType=2" "Red"
        
        # Try alternative method
        Write-Color "Trying alternative detection method..." "Yellow"
        try {
            $volumes = Get-Volume | Where-Object { $_.DriveType -eq "Removable" -and $_.DriveLetter }
            Write-Color "Found $($volumes.Count) removable volumes:" "Green"
            foreach ($vol in $volumes) {
                Write-Color "  $($vol.DriveLetter): - $($vol.FileSystemLabel) - $(([math]::Round($vol.Size / 1GB, 2))) GB" "Gray"
            }
        }
        catch {
            Write-Color "❌ Alternative method also failed: $_" "Red"
        }
    } else {
        Write-Color "Found $($usbDrives.Count) USB drives:" "Green"
        foreach ($drive in $usbDrives) {
            $label = if ($drive.VolumeName) { $drive.VolumeName } else { "No Label" }
            $sizeGB = [math]::Round($drive.Size / 1GB, 2)
            $freeGB = [math]::Round($drive.FreeSpace / 1GB, 2)
            Write-Color "  $($drive.DeviceID) [$label] ($sizeGB GB, $freeGB GB free)" "Gray"
        }
    }
}
catch {
    Write-Color "❌ Error detecting USB drives: $_" "Red"
}

Write-Host ""

# Test 3: Source directory check
Write-Color "Test 3: Checking source directory..." "Yellow"
$sourceBase = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Usb drive files"
Write-Color "Source path: $sourceBase" "Gray"

if (Test-Path $sourceBase) {
    Write-Color "✅ Source directory exists" "Green"
    try {
        $items = Get-ChildItem -Path $sourceBase -ErrorAction Stop
        Write-Color "Found $($items.Count) items in source directory:" "Green"
        
        $totalSize = 0
        foreach ($item in $items) {
            if ($item.PSIsContainer) {
                $folderSize = (Get-ChildItem -Path $item.FullName -Recurse -File -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
                $totalSize += $folderSize
                $sizeText = "$(([math]::Round($folderSize / 1MB, 2))) MB"
                Write-Color "  📁 $($item.Name) - $sizeText" "Gray"
            } else {
                $totalSize += $item.Length
                $sizeText = "$(([math]::Round($item.Length / 1MB, 2))) MB"
                Write-Color "  📄 $($item.Name) - $sizeText" "Gray"
            }
        }
        
        $totalSizeGB = [math]::Round($totalSize / 1GB, 2)
        Write-Color "Total source size: $totalSizeGB GB" "Cyan"
    }
    catch {
        Write-Color "❌ Error reading source directory: $_" "Red"
    }
} else {
    Write-Color "❌ Source directory not found" "Red"
    Write-Color "Please run Copy-NetworkFiles.ps1 first to populate the source location." "Yellow"
}

Write-Host ""

# Test 4: Permission check
Write-Color "Test 4: Checking permissions..." "Yellow"
try {
    $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object System.Security.Principal.WindowsPrincipal($currentUser)
    $isAdmin = $principal.IsInRole([System.Security.Principal.WindowsBuiltInRole]::Administrator)
    
    if ($isAdmin) {
        Write-Color "✅ Running as Administrator" "Green"
    } else {
        Write-Color "⚠️ Not running as Administrator" "Yellow"
        Write-Color "Some USB operations may require elevated permissions" "Yellow"
    }
}
catch {
    Write-Color "❌ Error checking permissions: $_" "Red"
}

Write-Host ""
Write-Color "=== TEST COMPLETE ===" "Cyan"
Write-Color "If USB drives are not detected, try:" "Yellow"
Write-Color "1. Running as Administrator" "Gray"
Write-Color "2. Checking if the USB drive is properly formatted" "Gray"
Write-Color "3. Trying a different USB port" "Gray"
Write-Color "4. Checking Windows Device Manager for drive issues" "Gray"

Write-Host ""
Write-Color "Press Enter to exit..." "Gray"
Read-Host
