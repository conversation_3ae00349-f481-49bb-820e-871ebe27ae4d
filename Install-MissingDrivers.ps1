# Install Missing Drivers Script
# Installs specific missing drivers on target PC
# Developed by: The greatest technician that ever lived

param(
    [string]$TargetPC = "BRBR3196ES724D",
    [PSCredential]$Credential
)

# Function to display banner
function Show-DriverInstallBanner {
    Clear-Host
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "    Missing Driver Installer" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "Developed by: The greatest technician that ever lived" -ForegroundColor Green
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host ""
}

# Function to install network printer drivers
function Install-NetworkPrinterDrivers {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )
    
    Write-Host "`nInstalling Network Printer Drivers..." -ForegroundColor Yellow
    
    $scriptBlock = {
        $results = @()
        
        # Method 1: Try to install generic network printer drivers
        try {
            # Add generic network printer driver
            $result1 = & pnputil /add-driver "C:\Windows\System32\DriverStore\FileRepository\*\ntprint.inf" /install 2>&1
            $results += "Generic printer driver: $result1"
        } catch {
            $results += "Generic printer driver failed: $($_.Exception.Message)"
        }
        
        # Method 2: Try Windows Update for printer drivers
        try {
            # Trigger Windows Update for drivers
            $updateSession = New-Object -ComObject Microsoft.Update.Session
            $updateSearcher = $updateSession.CreateUpdateSearcher()
            $searchResult = $updateSearcher.Search("IsInstalled=0 and Type='Driver' and CategoryIDs contains '4d36e979-e325-11ce-bfc1-08002be10318'")
            
            if ($searchResult.Updates.Count -gt 0) {
                $results += "Found $($searchResult.Updates.Count) printer driver updates available via Windows Update"
            } else {
                $results += "No printer driver updates found via Windows Update"
            }
        } catch {
            $results += "Windows Update search failed: $($_.Exception.Message)"
        }
        
        # Method 3: Enable Print Spooler service if not running
        try {
            $spooler = Get-Service -Name "Spooler" -ErrorAction SilentlyContinue
            if ($spooler.Status -ne "Running") {
                Start-Service -Name "Spooler"
                $results += "Print Spooler service started"
            } else {
                $results += "Print Spooler service already running"
            }
        } catch {
            $results += "Print Spooler service check failed: $($_.Exception.Message)"
        }
        
        return $results
    }
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            $results = & $scriptBlock
        } else {
            if ($Credential) {
                $results = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock
            } else {
                $results = Invoke-Command -ComputerName $ComputerName -ScriptBlock $scriptBlock
            }
        }
        
        foreach ($result in $results) {
            Write-Host "  $result" -ForegroundColor White
        }
        
        return $true
    } catch {
        Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to install USB drivers
function Install-USBDrivers {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )
    
    Write-Host "`nInstalling USB Drivers..." -ForegroundColor Yellow
    
    $scriptBlock = {
        $results = @()
        
        # Method 1: Scan for hardware changes
        try {
            # Use devcon or pnputil to scan for hardware changes
            $scanResult = & pnputil /scan-devices 2>&1
            $results += "Hardware scan result: $scanResult"
        } catch {
            $results += "Hardware scan failed: $($_.Exception.Message)"
        }
        
        # Method 2: Try to install generic USB drivers
        try {
            # Look for USB driver files in Windows
            $usbDrivers = Get-ChildItem -Path "C:\Windows\System32\DriverStore\FileRepository" -Filter "*usb*" -Directory -ErrorAction SilentlyContinue
            
            foreach ($usbDriver in $usbDrivers | Select-Object -First 3) {
                $infFiles = Get-ChildItem -Path $usbDriver.FullName -Filter "*.inf" -ErrorAction SilentlyContinue
                foreach ($infFile in $infFiles | Select-Object -First 1) {
                    try {
                        $installResult = & pnputil /add-driver $infFile.FullName /install 2>&1
                        $results += "USB driver install ($($infFile.Name)): $installResult"
                    } catch {
                        $results += "USB driver install failed ($($infFile.Name)): $($_.Exception.Message)"
                    }
                }
            }
        } catch {
            $results += "USB driver search failed: $($_.Exception.Message)"
        }
        
        # Method 3: Enable USB services
        try {
            $services = @("PlugPlay", "UsbStor")
            foreach ($serviceName in $services) {
                $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
                if ($service -and $service.Status -ne "Running") {
                    Start-Service -Name $serviceName
                    $results += "$serviceName service started"
                } else {
                    $results += "$serviceName service already running or not found"
                }
            }
        } catch {
            $results += "USB service check failed: $($_.Exception.Message)"
        }
        
        return $results
    }
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            $results = & $scriptBlock
        } else {
            if ($Credential) {
                $results = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock
            } else {
                $results = Invoke-Command -ComputerName $ComputerName -ScriptBlock $scriptBlock
            }
        }
        
        foreach ($result in $results) {
            Write-Host "  $result" -ForegroundColor White
        }
        
        return $true
    } catch {
        Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to run Windows Update for drivers
function Start-WindowsUpdateDriverScan {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )
    
    Write-Host "`nRunning Windows Update Driver Scan..." -ForegroundColor Yellow
    
    $scriptBlock = {
        try {
            # Method 1: Use Windows Update PowerShell module if available
            if (Get-Module -ListAvailable -Name PSWindowsUpdate) {
                Import-Module PSWindowsUpdate
                $updates = Get-WUList -Category "Drivers" -ErrorAction SilentlyContinue
                if ($updates) {
                    return "Found $($updates.Count) driver updates available via PSWindowsUpdate module"
                }
            }
            
            # Method 2: Use COM object for Windows Update
            $updateSession = New-Object -ComObject Microsoft.Update.Session
            $updateSearcher = $updateSession.CreateUpdateSearcher()
            $searchResult = $updateSearcher.Search("IsInstalled=0 and Type='Driver'")
            
            if ($searchResult.Updates.Count -gt 0) {
                $driverUpdates = @()
                foreach ($update in $searchResult.Updates) {
                    $driverUpdates += $update.Title
                }
                return "Found $($searchResult.Updates.Count) driver updates available: $($driverUpdates -join ', ')"
            } else {
                return "No driver updates found via Windows Update"
            }
        } catch {
            return "Windows Update scan failed: $($_.Exception.Message)"
        }
    }
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            $result = & $scriptBlock
        } else {
            if ($Credential) {
                $result = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock
            } else {
                $result = Invoke-Command -ComputerName $ComputerName -ScriptBlock $scriptBlock
            }
        }
        
        Write-Host "  $result" -ForegroundColor White
        return $true
    } catch {
        Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
Show-DriverInstallBanner

if (-not $Credential -and $TargetPC -ne $env:COMPUTERNAME -and $TargetPC -ne "localhost" -and $TargetPC -ne ".") {
    Write-Host "Remote computer access detected. Credentials required." -ForegroundColor Yellow
    $Credential = Get-Credential -Message "Enter credentials for remote computer access"
    if (-not $Credential) {
        Write-Host "Credentials are required for remote access. Exiting." -ForegroundColor Red
        exit 1
    }
}

Write-Host "Target PC: $TargetPC" -ForegroundColor Cyan
Write-Host "Installing missing drivers based on comparison results..." -ForegroundColor Cyan

Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "MISSING DRIVERS TO INSTALL" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host "Based on comparison, the following drivers are missing:" -ForegroundColor White
Write-Host "  - 6x Network Printer Connection drivers" -ForegroundColor Red
Write-Host "  - 3x WinUsb Device drivers" -ForegroundColor Red

# Install network printer drivers
$printerSuccess = Install-NetworkPrinterDrivers -ComputerName $TargetPC -Credential $Credential

# Install USB drivers
$usbSuccess = Install-USBDrivers -ComputerName $TargetPC -Credential $Credential

# Run Windows Update driver scan
$updateSuccess = Start-WindowsUpdateDriverScan -ComputerName $TargetPC -Credential $Credential

Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "DRIVER INSTALLATION SUMMARY" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

Write-Host "Network Printer Drivers: $(if($printerSuccess){'Processed'}else{'Failed'})" -ForegroundColor $(if($printerSuccess){'Green'}else{'Red'})
Write-Host "USB Drivers: $(if($usbSuccess){'Processed'}else{'Failed'})" -ForegroundColor $(if($usbSuccess){'Green'}else{'Red'})
Write-Host "Windows Update Scan: $(if($updateSuccess){'Completed'}else{'Failed'})" -ForegroundColor $(if($updateSuccess){'Green'}else{'Red'})

Write-Host "`nRecommendations:" -ForegroundColor Cyan
Write-Host "1. Restart the computer to complete driver installation" -ForegroundColor White
Write-Host "2. Check Device Manager for any remaining unknown devices" -ForegroundColor White
Write-Host "3. Run Windows Update manually to install any available driver updates" -ForegroundColor White
Write-Host "4. For network printers, add them through Settings > Printers & Scanners" -ForegroundColor White

$restartChoice = Read-Host "`nWould you like to restart $TargetPC now? (y/n)"
if ($restartChoice -eq 'y') {
    Write-Host "Restarting $TargetPC..." -ForegroundColor Yellow
    try {
        if ($TargetPC -eq $env:COMPUTERNAME -or $TargetPC -eq "localhost" -or $TargetPC -eq ".") {
            Restart-Computer -Force
        } else {
            if ($Credential) {
                Invoke-Command -ComputerName $TargetPC -Credential $Credential -ScriptBlock { Restart-Computer -Force }
            } else {
                Invoke-Command -ComputerName $TargetPC -ScriptBlock { Restart-Computer -Force }
            }
        }
        Write-Host "Restart command sent successfully" -ForegroundColor Green
    } catch {
        Write-Host "Failed to restart: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "Remember to restart the computer manually to complete driver installation" -ForegroundColor Yellow
}

Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "DRIVER INSTALLATION COMPLETE" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

Read-Host "`nPress Enter to exit"
