# Get and store credentials at script start
$script:storedCredentials = $null

function Initialize-Credentials {
    $credentialPath = "$env:USERPROFILE\.migration_creds.xml"

    # Try to load saved credentials first
    if (Test-Path $credentialPath) {
        try {
            Write-Color "Found saved credentials. Loading..." "Green"
            $script:storedCredentials = Import-Clixml -Path $credentialPath

            # Test the credentials by trying to access a domain resource
            Write-Color "Testing saved credentials..." "Yellow"
            try {
                # Try a simple domain operation to validate credentials
                Invoke-Command -ComputerName "localhost" -Credential $script:storedCredentials -ScriptBlock { $env:COMPUTERNAME } -ErrorAction Stop | Out-Null
                Write-Color "Saved credentials are valid!" "Green"

                # Ask if user wants to use saved credentials
                $useSaved = Read-Host "Use saved credentials? (Y/N)"
                if ($useSaved -match '^(Y|y)$') {
                    Write-Color "Using saved credentials" "Green"
                    return $true
                } else {
                    Write-Color "Getting new credentials..." "Yellow"
                }
            }
            catch {
                Write-Color "Saved credentials are invalid or expired. Getting new credentials..." "Yellow"
                Remove-Item -Path $credentialPath -Force -ErrorAction SilentlyContinue
            }
        }
        catch {
            Write-Color "Error loading saved credentials. Getting new credentials..." "Yellow"
            Remove-Item -Path $credentialPath -Force -ErrorAction SilentlyContinue
        }
    }

    # Get new credentials
    Write-Color "Initializing credentials for script execution..." "Cyan"
    try {
        $script:storedCredentials = Get-Credential -Message "Enter domain credentials for script operations" -UserName "mclaren\"
        if ($script:storedCredentials) {
            Write-Color "Credentials stored successfully" "Green"

            # Ask if user wants to save credentials
            $saveChoice = Read-Host "Save credentials for future use? (Y/N)"
            if ($saveChoice -match '^(Y|y)$') {
                try {
                    $script:storedCredentials | Export-Clixml -Path $credentialPath -Force
                    Write-Color "Credentials saved securely to: $credentialPath" "Green"
                    Write-Color "Note: Credentials are encrypted and can only be used by your user account on this computer." "Gray"
                }
                catch {
                    Write-Color "Warning: Could not save credentials - $_" "Yellow"
                }
            }
            return $true
        }
        Write-Color "No credentials provided" "Red"
        return $false
    }
    catch {
        Write-Color "Error storing credentials: $_" "Red"
        return $false
    }
}

function Clear-SavedCredentials {
    $credentialPath = "$env:USERPROFILE\.migration_creds.xml"
    if (Test-Path $credentialPath) {
        Remove-Item -Path $credentialPath -Force
        Write-Color "Saved credentials cleared successfully" "Green"
    } else {
        Write-Color "No saved credentials found to clear" "Yellow"
    }
}

function Write-Color {
    param([string]$Message, [string]$Color = "Gray", [switch]$NoNewline)
    if ($NoNewline) { Write-Host $Message -ForegroundColor $Color -NoNewline }
    else { Write-Host $Message -ForegroundColor $Color }
}

function Show-Banner {
    param([string]$Title, [string]$Color = "Cyan")
    $width = 80
    $padding = [math]::Max(0, ($width - $Title.Length - 4) / 2)
    $leftPad = [math]::Floor($padding)
    $rightPad = [math]::Ceiling($padding)
    Write-Host ""
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ("*" + " " * ($width - 2) + "*") -ForegroundColor $Color
    Write-Host ("*" + " " * $leftPad + $Title + " " * $rightPad + "*") -ForegroundColor $Color
    Write-Host ("*" + " " * ($width - 2) + "*") -ForegroundColor $Color
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ""
}

function Show-Progress {
    param([string]$StepText, [int]$StepNumber, [int]$TotalSteps, [string]$Color = "Yellow")
    $progressBar = "[" + ("#" * $StepNumber) + (" " * ($TotalSteps - $StepNumber)) + "]"
    $percentage = [math]::Round(($StepNumber / $TotalSteps) * 100)
    $symbols = @("*", "+", "=", "~", ">")
    $symbol = $symbols[($StepNumber - 1) % $symbols.Count]
    Write-Host ""
    Write-Host "  $progressBar $percentage% Complete" -ForegroundColor $Color
    Write-Host "  $symbol $StepText" -ForegroundColor "White"
    Write-Host ""
}

function Show-Spinner {
    param([string]$Message, [int]$Seconds = 3)
    $spinner = @('|', '/', '-', '\')
    $startTime = Get-Date
    $endTime = $startTime.AddSeconds($Seconds)
    $i = 0
    Write-Host "  " -NoNewline
    while ((Get-Date) -lt $endTime) {
        Write-Host "`r  $($spinner[$i % $spinner.Length]) $Message" -NoNewline -ForegroundColor Cyan
        Start-Sleep -Milliseconds 100
        $i++
    }
    Write-Host "`r  + $Message" -ForegroundColor Green
}

function Show-CompletionAnimation {
    $colors = @("Red", "Yellow", "Green", "Cyan", "Blue", "Magenta")
    $message = "MIGRATION COMPLETE!"
    1..3 | ForEach-Object { Write-Host "" }
    1..3 | ForEach-Object {
        $dots = "." * $_
        Write-Host "  Finalizing$dots"
        Start-Sleep -Milliseconds 300
    }
    foreach ($color in $colors) {
        Write-Host $message -ForegroundColor $color
        Start-Sleep -Milliseconds 100
    }
    Write-Host ""
}

function Test-PCOnlineAndDNS {
    param([Parameter(Mandatory=$true)][string]$computerName)
    try {
        if (Test-Connection -ComputerName $computerName -Count 1 -Quiet) {
            $ip = [System.Net.Dns]::GetHostAddresses($computerName) | Where-Object { $_.AddressFamily -eq 'InterNetwork' } | Select-Object -First 1 -ExpandProperty IPAddressToString
            if ($ip) {
                $nsLookupResult = [System.Net.Dns]::GetHostEntry($ip)
                $nsLookupName = $nsLookupResult.HostName -replace '\..*$', ''
                return @{ Online = $true; DNSStatus = if ($nsLookupName -eq $computerName) { "OK" } else { "Mismatch" }; NSLookupName = $nsLookupName; IP = $ip }
            } else {
                return @{ Online = $true; DNSStatus = "Error"; NSLookupName = ""; IP = ""; Error = "Failed to resolve IP to DNS name" }
            }
        } else {
            return @{ Online = $false; DNSStatus = "N/A"; NSLookupName = ""; IP = ""; Error = "Computer is offline" }
        }
    } catch {
        return @{ Online = $false; DNSStatus = "N/A"; NSLookupName = ""; IP = ""; Error = $_.Exception.Message }
    }
}

function Move-ADComputerToSameOU {
    param([Parameter(Mandatory=$true)][string]$SourceComputer, [Parameter(Mandatory=$true)][string]$DestinationComputer)
    $oldPCObj = Get-ADComputer $SourceComputer
    $ouDN = ($oldPCObj.DistinguishedName -split ',')[1..($oldPCObj.DistinguishedName.Length)] -join ','
    $newPCObj = Get-ADComputer $DestinationComputer
    Move-ADObject -Identity $newPCObj.DistinguishedName -TargetPath $ouDN
}

function Get-InstallerPath {
    param([string]$NetworkPath, [string]$LocalPath = "")

    if ($script:useLocalInstallers -and $LocalPath) {
        return $LocalPath
    } else {
        return $NetworkPath
    }
}

function Get-InstallerPaths {
    param([string]$TargetComputer = "")

    # Define all installer paths based on local vs network mode
    if ($script:useLocalInstallers) {
        # Use local D:\ drive paths (these will be used in remote commands on target computer)
        return @{
            Splashtop = "D:\Splashtop_Push"
            DotNetSxs = "D:\sxs"
            LexmarkDriver = "D:\Lexmark_Universal_v2_UD1_PostScript_3_Emulation"
            Office365 = "D:\o365"
            Citrix = "D:\4.9_LTSR2"
            Nuance = "D:\Nuance121"
            Java = "D:\jre1.7.0_45.msi"
            Chrome = "D:\GoogleChromeStandaloneEnterprise64.msi"
            CitrixReceiver = "D:\4.9_LTSR2\CitrixReceiver.cmd"
            PhotoViewer = "D:\Win_Photo_Viewer.reg"
            VolumeScript = "D:\Volume.ps1"
            BitLockerScript = "D:\BitLockerAD.ps1"
            DellCommandUpdate = "D:\Commandupdate.EXE"
            FSTools = "D:\FSTools"
            LexmarkGDI = "D:\LexmarkGDI"
            ImprivataAgent = "D:\ImprivataAgent_x64.msi"
        }
    } else {
        return @{
            Splashtop = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\Splashtop_Push"
            DotNetSxs = "\\bay-msfsnas01\data\FS\PUBLIC\installdotnet3_5\Win11\sxs"
            LexmarkDriver = "\\bay-msfsnas01\data\FS\SFILES\__install__\source\Lexmark\Lexmark_Universal_v2_UD1_PostScript_3_Emulation"
            Office365 = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\o365"
            Citrix = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\4.9_LTSR2"
            Nuance = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\CustomCitrix\4.9_LTSR2\Nuance"
            Java = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\WSP\7\jre1.7.0_45.msi"
            Chrome = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\Google\GoogleChromeStandaloneEnterprise64.msi"
            CitrixReceiver = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\4.9_LTSR2\CitrixReceiver.cmd"
            PhotoViewer = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Win_Photo_Viewer.reg"
            VolumeScript = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Volume.ps1"
            BitLockerScript = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\BitLockerAD.ps1"
            DellCommandUpdate = "\\bay-msfsnas01\data\FS\Source\Commandupdate.EXE"
            FSTools = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\FSTools"
            LexmarkGDI = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\LexmarkGDI"
            ImprivataAgent = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Imprivata_push\ImprivataAgent_x64.msi"
        }
    }
}

function Update-LexmarkDrivers {
    Write-Color "Checking for Lexmark printers and drivers..." "Cyan"
    try {
        $lexmarkPrinters = Get-Printer | Where-Object { $_.Name -like "*Lexmark*" -or $_.DriverName -like "*Lexmark*" }
        if ($lexmarkPrinters) {
            Write-Color "Found $($lexmarkPrinters.Count) Lexmark printer(s)" "Yellow"
            foreach ($printer in $lexmarkPrinters) {
                Write-Color "Updating driver for $($printer.Name)..." "Gray"
                Set-Printer -Name $printer.Name -DriverName "Lexmark Universal v2 PostScript 3 Emulation"
                Write-Color "Updated driver for $($printer.Name)" "Green"
            }
        } else {
            Write-Color "No Lexmark printers found" "Yellow"
        }
    }
    catch {
        Write-Color "Error updating Lexmark drivers: $_" "Red"
    }
}
function Invoke-OfficeConfiguration {
    param(
        [Parameter(Mandatory=$true)][string]$copyToDeviceId,
        [Parameter(Mandatory=$true)][System.Management.Automation.PSCredential]$Credentials
    )

    # Check and enable PSRemoting if needed
    Write-Color "Checking PSRemoting status..." "Cyan"
    $winrm = Get-WmiObject -Class Win32_Service -ComputerName $copyToDeviceId | Where-Object { $_.Name -eq 'WinRM' }
    if ($winrm.Status -ne "Running") {
        Write-Color "Enabling PSRemoting..." "Yellow"
        $SessionArgs = @{
            ComputerName = $copyToDeviceId
            SessionOption = New-CimSessionOption -Protocol Dcom
        }
        $MethodArgsEnablePS = @{
            ClassName = 'Win32_Process'
            MethodName = 'Create'
            CimSession = New-CimSession @SessionArgs
            Arguments = @{ CommandLine = 'powershell -Command "Enable-PSRemoting -Force"' }
        }
        Invoke-CimMethod @MethodArgsEnablePS
        Start-Sleep -Seconds 5
    }

    Write-Color "Creating PSSession..." "Cyan"
    $maxAttempts = 3
    $attempt = 0
    $session = $null

    while ($attempt -lt $maxAttempts -and $null -eq $session) {
        $attempt++
        try {
            $session = New-PSSession -ComputerName $copyToDeviceId -Credential $Credentials -ErrorAction Stop
            break
        }
        catch {
            if ($attempt -lt $maxAttempts) {
                Write-Color "PSSession creation attempt $attempt failed. Retrying in 5 seconds..." "Yellow"
                Start-Sleep -Seconds 5
            }
            else {
                Write-Color "Failed to create PSSession after $maxAttempts attempts: $_" "Red"
                return
            }
        }
    }

    if ($null -eq $session) {
        Write-Color "Could not establish PSSession with $copyToDeviceId" "Red"
        return
    }

    try {
        # Copy required files using dynamic paths
        $installerPaths = Get-InstallerPaths -TargetComputer $copyToDeviceId
        $copyItems = @(
            @{ Name = "Splashtop"; Source = $installerPaths.Splashtop; Dest = "Splashtop"; IsFolder = $true },
            @{ Name = ".NET 3.5 SXS"; Source = $installerPaths.DotNetSxs; Dest = "sxs"; IsFolder = $true },
            @{ Name = "Lexmark Driver"; Source = $installerPaths.LexmarkDriver; Dest = "Lexmark_Universal_v2_UD1_PostScript_3_Emulation"; IsFolder = $true },
            @{ Name = "Office 365"; Source = $installerPaths.Office365; Dest = "o365"; IsFolder = $true },
            @{ Name = "Citrix"; Source = $installerPaths.Citrix; Dest = "4.9_LTSR2"; IsFolder = $true },
            @{ Name = "Nuance"; Source = $installerPaths.Nuance; Dest = "Nuance121"; IsFolder = $true },
            @{ Name = "Java"; Source = $installerPaths.Java; Dest = "jre1.7.0_45.msi" },
            @{ Name = "Chrome"; Source = $installerPaths.Chrome; Dest = "GoogleChromeStandaloneEnterprise64.msi" },
            @{ Name = "Citrix Receiver"; Source = $installerPaths.CitrixReceiver; Dest = "4.9_LTSR2\CitrixReceiver.cmd" },
            @{ Name = "Photo Viewer"; Source = $installerPaths.PhotoViewer; Dest = "Win_Photo_Viewer.reg" },
            @{ Name = "Volume Script"; Source = $installerPaths.VolumeScript; Dest = "Volume.ps1" },
            @{ Name = "BitLocker Script"; Source = $installerPaths.BitLockerScript; Dest = "BitLockerAD.ps1" },
            @{ Name = "Dell Command Update"; Source = $installerPaths.DellCommandUpdate; Dest = "dcu\Commandupdate.EXE" }
        )

        foreach ($item in $copyItems) {
            Write-Color "Copying $($item.Name)" "Cyan"
            try {
                if ($script:useLocalInstallers) {
                    # Use remote command to copy from local D:\ drive on target computer
                    Invoke-Command -ComputerName $copyToDeviceId -Credential $script:storedCredentials -ScriptBlock {
                        param($SourcePath, $DestPath, $IsFolder)

                        if (Test-Path $SourcePath) {
                            # Create destination directory
                            $destFolder = if ($IsFolder) { $DestPath } else { Split-Path $DestPath -Parent }
                            if (-not (Test-Path $destFolder)) {
                                New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
                            }

                            if ($IsFolder) {
                                # Copy folder contents using robocopy
                                $result = Start-Process -FilePath "robocopy" -ArgumentList "`"$SourcePath`"", "`"$DestPath`"", "/E", "/R:3", "/W:10" -Wait -PassThru -NoNewWindow
                                if ($result.ExitCode -le 7) {
                                    Write-Host "Folder copied successfully" -ForegroundColor Green
                                } else {
                                    Write-Host "Warning: Folder copy completed with exit code: $($result.ExitCode)" -ForegroundColor Yellow
                                }
                            } else {
                                # Copy single file
                                Copy-Item -Path $SourcePath -Destination $DestPath -Force
                                Write-Host "File copied successfully" -ForegroundColor Green
                            }
                        } else {
                            Write-Host "Warning: Source not found at $SourcePath" -ForegroundColor Yellow
                        }
                    } -ArgumentList $item.Source, "C:\Files\$($item.Dest)", $item.IsFolder
                } else {
                    # Network mode - use original Copy-Item method
                    if ($item.IsFolder) {
                        # Create destination path with correct folder name
                        $destPath = "\\$copyToDeviceId\c$\Files\$($item.Dest)"
                        if (-not (Test-Path $destPath)) {
                            New-Item -Path $destPath -ItemType Directory -Force | Out-Null
                        }
                        # Copy contents to the correctly named folder
                        Copy-Item -Path "$($item.Source)\*" -Destination $destPath -Recurse -Force
                    } else {
                        # Handle single file copy
                        $destPath = "\\$copyToDeviceId\c$\Files\$($item.Dest)"
                        $destFolder = Split-Path $destPath -Parent
                        if (-not (Test-Path $destFolder)) {
                            New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
                        }
                        Copy-Item -Path $item.Source -Destination $destPath -Force
                    }
                }
                Write-Color "Success: $($item.Name)" "Green"
            }
            catch {
                Write-Color "Failed: $($item.Name) - $_" "Red"
            }
        }

        # Execute remote configuration
        Invoke-Command -Session $session -ScriptBlock {
            # System Information
            $ComputerName = $env:computername
            $ComputerBIOS = Get-WmiObject -Class Win32_Bios
            $ComputerSN = $ComputerBIOS.SerialNumber
            $ComputerModel = (Get-WmiObject -Class Win32_ComputerSystem).Model
            $ComputerMemory = Get-CimInstance Win32_PhysicalMemory | Measure-Object -Property capacity -Sum | Select-Object -ExpandProperty Sum
            $ComputerMemoryGB = [math]::Round($ComputerMemory / 1GB, 2)

            Write-Host "*** Setting Pagefile info"
            Write-Host "*** Computer Name: $ComputerName"
            Write-Host "*** Computer Model: $ComputerModel"
            Write-Host "*** Computer Serial: $ComputerSN"
            Write-Host "*** Computer RAM: $ComputerMemoryGB GB"

            # Install Office 365
            Write-Host "*** Install Office 365"
            Write-Host "*** Running Office 365 installation"
            $p = Start-Process -FilePath "C:\Files\o365\batch_365.bat" -PassThru
            $p.WaitForExit()
            Write-Host "*** Deleting C:\Files\o365 folder"
            Remove-Item -Path "C:\Files\o365" -Recurse -Force

            # Office Ribbon Fix
            Write-Host "***" -ForegroundColor Green
            Write-Output "Office ribbon key fix being applied"
            $RegistryPath = "HKCU:\Software\Microsoft\Office\16.0\Common\ExperimentConfigs\ExternalFeatureOverrides\word"
            $RegistryName = "Microsoft.Office.UXPlatform.FluentSVRefresh"
            $RegistryValue = "false"
            if (-not (Test-Path $RegistryPath)) {
                New-Item -Path $RegistryPath -Force | Out-Null
            }
            Set-ItemProperty -Path $RegistryPath -Name $RegistryName -Value $RegistryValue
            Write-Output "Registry key '$RegistryName' set to '$RegistryValue' in path '$RegistryPath'."
            Write-Host "Office ribbon key and value have been set successfully."

                    # Install Citrix Receiver
            Write-Host "***" -ForegroundColor Green
            Write-Host "*** Installing Citrix Receiver 4.9"
            $p = Start-Process -PassThru "C:\Files\4.9_LTSR2\CitrixReceiver.cmd"
            $p.WaitForExit()

            # Install Nuance
            Write-Host "***" -ForegroundColor Green
            Write-Host "*** Cerner Dragon Mic installation"
            if (-not (Test-Path "C:\Users\<USER>\Package Cache\{5a127842-d23b-42f3-bd3e-4441fc12c4d9}\Nuance Citrix Client Audio Extension.exe" -PathType Leaf)) {
                Write-Host "*** Installing Nuance Citrix Client Audio Extension"
                Start-Process -FilePath "C:\Files\Nuance121\Nuance Citrix Client Audio Extension.msi" -ArgumentList ('/qb REBOOT=REALLYSUPPRESS') -Wait
            }
            if (-not (Test-Path "C:\Program Files (x86)\Common Files\Nuance\PowerMic\PowermicCtrl.dll" -PathType Leaf)) {
                Write-Host "*** Installing Nuance PowerMic Citrix Client Extension"
                Start-Process -FilePath "C:\Files\Nuance121\Nuance PowerMic Citrix Client Extension.msi" -ArgumentList ('/qb REBOOT=REALLYSUPPRESS') -Wait
            }

        }

        # Imprivata Type 1 installation will be handled by external script
    }
    finally {
        if ($null -ne $session) {
            Remove-PSSession $session
        }
    }
}

function Invoke-Type1Configuration {
    param(
        [Parameter(Mandatory=$true)][string]$copyToDeviceId,
        [Parameter(Mandatory=$true)][string]$copyFromDeviceId,
        [Parameter(Mandatory=$true)][System.Management.Automation.PSCredential]$Credentials,
        [Parameter(Mandatory=$true)][System.Management.Automation.Runspaces.PSSession]$session
    )

    Write-Color "`nInstalling Imprivata Type 1..." "Cyan"

    try {
        # Create Imprivata directories
        $localImprivataDir = Join-Path $env:TEMP "Imprivata"
        $remoteImprivataDir = "\\$copyToDeviceId\C$\files\Imprivata"

        New-Item -Path $localImprivataDir -ItemType Directory -Force | Out-Null
        New-Item -Path $remoteImprivataDir -ItemType Directory -Force | Out-Null

        # Copy Imprivata MSI file
        if ($script:useLocalInstallers) {
            # Use remote command to copy MSI from local D:\ drive on target computer
            Invoke-Command -ComputerName $copyToDeviceId -Credential $script:storedCredentials -ScriptBlock {
                $sourcePath = "D:\ImprivataAgent_x64.msi"
                $destPath = "C:\Files\ImprivataAgent_x64.msi"

                if (Test-Path $sourcePath) {
                    # Create destination directory
                    $destFolder = Split-Path $destPath -Parent
                    if (-not (Test-Path $destFolder)) {
                        New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
                    }

                    # Copy MSI file
                    Copy-Item -Path $sourcePath -Destination $destPath -Force
                    Write-Host "Imprivata Agent MSI copied successfully from local D:\ drive" -ForegroundColor Green
                } else {
                    Write-Host "Warning: Imprivata Agent MSI not found at $sourcePath" -ForegroundColor Yellow
                }
            }
        } else {
            # Network mode - copy MSI from network location
            $sourceMSI = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Imprivata_push\ImprivataAgent_x64.msi"
            $destMSI = "\\$copyToDeviceId\C$\Files\ImprivataAgent_x64.msi"

            # Create destination directory
            $destFolder = Split-Path $destMSI -Parent
            if (-not (Test-Path $destFolder)) {
                New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
            }

            Copy-Item -Path $sourceMSI -Destination $destMSI -Force
        }

        # Install Imprivata Type 1
        Invoke-Command -Session $session -ScriptBlock {
            Write-Host "Installing Imprivata Type 1..." -ForegroundColor Green

            # Remove any existing Imprivata installations first
            Write-Host "Removing any existing Imprivata installations..." -ForegroundColor Yellow
            try {
                Start-Process -FilePath "MsiExec.exe" -ArgumentList "/X{E8A87655-4D4C-4ADF-A097-D4FBBB95CA42} /q REBOOT=REALLYSUPPRESS" -Wait -NoNewWindow -ErrorAction SilentlyContinue
                Start-Process -FilePath "MsiExec.exe" -ArgumentList "/X{26B7B974-0DE7-4CD2-93D7-B3D9CBFC91B2} /q REBOOT=REALLYSUPPRESS" -Wait -NoNewWindow -ErrorAction SilentlyContinue
                Write-Host "Old Imprivata installations removed" -ForegroundColor Green
            }
            catch {
                Write-Host "Note: No existing Imprivata installations found to remove" -ForegroundColor Gray
            }

            # Check if Imprivata MSI exists
            $imprivataAgent = "C:\Files\ImprivataAgent_x64.msi"

            if (Test-Path $imprivataAgent) {
                Write-Host "Installing Imprivata Agent Type 1..." -ForegroundColor Yellow
                try {
                    # Use the exact command provided for Type 1 installation
                    $arguments = "/i `"$imprivataAgent`" /q /norestart AGENTTYPE=1 IPTXPRIMSERVER=https://mhc-lxasimp01.mclaren.org/sso/servlet/messagerouter"
                    Start-Process -FilePath "msiexec.exe" -ArgumentList $arguments -Wait -NoNewWindow
                    Write-Host "Imprivata Agent Type 1 installed successfully" -ForegroundColor Green
                }
                catch {
                    Write-Host "Error: Failed to install Imprivata Agent Type 1 - $_" -ForegroundColor Red
                }
            } else {
                Write-Host "Error: ImprivataAgent_x64.msi not found at $imprivataAgent" -ForegroundColor Red

                # List available files for troubleshooting
                Write-Host "Available files in C:\Files\:" -ForegroundColor Yellow
                if (Test-Path "C:\Files\") {
                    Get-ChildItem -Path "C:\Files\" -Filter "*.msi" | ForEach-Object {
                        Write-Host "  $($_.Name)" -ForegroundColor Gray
                    }
                }
            }
        }

        # Cleanup
        Remove-Item -Path $localImprivataDir -Recurse -Force -ErrorAction SilentlyContinue
        Remove-Item -Path $remoteImprivataDir -Recurse -Force -ErrorAction SilentlyContinue

        Write-Color "Type 1 configuration completed successfully" "Green"
    }
    catch {
        Write-Color "Error executing Type 1 configuration: $_" "Red"
    }
}

function Invoke-Type2Configuration {
    param(
        [Parameter(Mandatory=$true)][string]$copyToDeviceId,
        [Parameter(Mandatory=$true)][string]$copyFromDeviceId,
        [Parameter(Mandatory=$true)][System.Management.Automation.PSCredential]$Credentials,
        [Parameter(Mandatory=$true)][System.Management.Automation.Runspaces.PSSession]$session
    )

    Write-Color "`nInstalling Imprivata Type 2..." "Cyan"

    try {
        # Create Imprivata directory
        $imprivataDir = "\\$copyToDeviceId\c$\Files\Imprivata"
        if (-not (Test-Path $imprivataDir)) {
            New-Item -Path $imprivataDir -ItemType Directory -Force | Out-Null
        }

        # Copy Imprivata MSI file
        if ($script:useLocalInstallers) {
            # Use remote command to copy MSI from local D:\ drive on target computer
            Invoke-Command -ComputerName $copyToDeviceId -Credential $script:storedCredentials -ScriptBlock {
                $sourcePath = "D:\ImprivataAgent_x64.msi"
                $destPath = "C:\Files\ImprivataAgent_x64.msi"

                if (Test-Path $sourcePath) {
                    # Create destination directory
                    $destFolder = Split-Path $destPath -Parent
                    if (-not (Test-Path $destFolder)) {
                        New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
                    }

                    # Copy MSI file
                    Copy-Item -Path $sourcePath -Destination $destPath -Force
                    Write-Host "Imprivata Agent MSI copied successfully from local D:\ drive" -ForegroundColor Green
                } else {
                    Write-Host "Warning: Imprivata Agent MSI not found at $sourcePath" -ForegroundColor Yellow
                }
            }
        } else {
            # Network mode - copy MSI from network location
            $sourceMSI = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Imprivata_push\ImprivataAgent_x64.msi"
            $destMSI = "\\$copyToDeviceId\C$\Files\ImprivataAgent_x64.msi"

            # Create destination directory
            $destFolder = Split-Path $destMSI -Parent
            if (-not (Test-Path $destFolder)) {
                New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
            }

            Copy-Item -Path $sourceMSI -Destination $destMSI -Force
        }

        # Get stored credentials from source
        $sourceCreds = Invoke-Command -ComputerName $copyFromDeviceId -Credential $Credentials -ScriptBlock {
            $regPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon"
            if (Test-Path $regPath) {
                $props = Get-ItemProperty -Path $regPath
                [PSCustomObject]@{
                    UserName = $props.DefaultUserName
                    Password = $props.DefaultPassword
                }
            }
        }

        if ($sourceCreds.UserName -and $sourceCreds.Password) {
            Write-Color "  Found stored credentials on source computer" "Green"
            $AutoUsername = $sourceCreds.UserName
            $AutoPassword = ConvertTo-SecureString $sourceCreds.Password -AsPlainText -Force
        } else {
            Write-Color "  No stored credentials found" "Yellow"
            $AutoUsername = Read-Host "Enter Imprivata autologin username"
            $AutoPassword = Read-Host "Enter Imprivata autologin password" -AsSecureString
        }

        # Install Imprivata
        $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($AutoPassword)
        $PlainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
        [Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)

        Invoke-Command -Session $session -ScriptBlock {
            param($Username, $Password)

            Write-Host "Installing Imprivata Type 2..." -ForegroundColor Green

            # Remove any existing Imprivata installations first
            Write-Host "Removing any existing Imprivata installations..." -ForegroundColor Yellow
            try {
                Start-Process -FilePath "MsiExec.exe" -ArgumentList "/X{E8A87655-4D4C-4ADF-A097-D4FBBB95CA42} /q REBOOT=REALLYSUPPRESS" -Wait -NoNewWindow -ErrorAction SilentlyContinue
                Start-Process -FilePath "MsiExec.exe" -ArgumentList "/X{26B7B974-0DE7-4CD2-93D7-B3D9CBFC91B2} /q REBOOT=REALLYSUPPRESS" -Wait -NoNewWindow -ErrorAction SilentlyContinue
                Write-Host "Old Imprivata installations removed" -ForegroundColor Green
            }
            catch {
                Write-Host "Note: No existing Imprivata installations found to remove" -ForegroundColor Gray
            }

            # Check if Imprivata MSI exists
            $imprivataAgent = "C:\Files\ImprivataAgent_x64.msi"

            if (Test-Path $imprivataAgent) {
                Write-Host "Installing Imprivata Agent Type 2..." -ForegroundColor Yellow
                try {
                    # Use the exact command provided for Type 2 installation
                    $arguments = "/i `"$imprivataAgent`" /q /norestart AGENTTYPE=2 IPTXPRIMSERVER=https://mhc-lxasimp01.mclaren.org/sso/servlet/messagerouter"
                    Start-Process -FilePath "msiexec.exe" -ArgumentList $arguments -Wait -NoNewWindow
                    Write-Host "Imprivata Agent Type 2 installed successfully" -ForegroundColor Green
                }
                catch {
                    Write-Host "Error: Failed to install Imprivata Agent Type 2 - $_" -ForegroundColor Red
                }
            } else {
                Write-Host "Error: ImprivataAgent_x64.msi not found at $imprivataAgent" -ForegroundColor Red

                # List available files for troubleshooting
                Write-Host "Available files in C:\Files\:" -ForegroundColor Yellow
                if (Test-Path "C:\Files\") {
                    Get-ChildItem -Path "C:\Files\" -Filter "*.msi" | ForEach-Object {
                        Write-Host "  $($_.Name)" -ForegroundColor Gray
                    }
                }
            }

            # Configure Winlogon settings
            $winlogonPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon"
            Set-ItemProperty -Path $winlogonPath -Name "DefaultUserName" -Value $Username
            Set-ItemProperty -Path $winlogonPath -Name "DefaultDomainName" -Value "MCLAREN"
            Set-ItemProperty -Path $winlogonPath -Name "DefaultPassword" -Value $Password
            Set-ItemProperty -Path $winlogonPath -Name "AutoAdminLogon" -Value "1"
            Set-ItemProperty -Path $winlogonPath -Name "ForceAutoLogon" -Value "1"

            # Configure System Policies
            Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "DontDisplayLastUsername" -Value 1 -Type DWord

            # Configure SSO Provider
            $ssoPath = "HKLM:\SOFTWARE\SSOProvider\ISXAgent"
            if (-not (Test-Path $ssoPath)) {
                New-Item -Path $ssoPath -Force | Out-Null
            }
            Set-ItemProperty -Path $ssoPath -Name "Type" -Value 2 -Type DWord

            # Cleanup
            Remove-Item -Path "C:\Files\Imprivata" -Recurse -Force -ErrorAction SilentlyContinue
        } -ArgumentList $AutoUsername, $PlainPassword

        # Clear sensitive data
        Remove-Variable -Name AutoUsername, PlainPassword -ErrorAction SilentlyContinue
        [GC]::Collect()

        Write-Color "Type 2 configuration completed successfully" "Green"
    }
    catch {
        Write-Color "Error executing Type 2 configuration: $_" "Red"
    }
}

function Invoke-NoOfficeConfiguration {
    param(
        [Parameter(Mandatory=$true)][string]$copyToDeviceId,
        [Parameter(Mandatory=$true)][System.Management.Automation.PSCredential]$Credentials
    )

    # Check and enable PSRemoting if needed
    Write-Color "Checking PSRemoting status..." "Cyan"
    $winrm = Get-WmiObject -Class Win32_Service -ComputerName $copyToDeviceId | Where-Object { $_.Name -eq 'WinRM' }
    if ($winrm.Status -ne "Running") {
        Write-Color "Enabling PSRemoting..." "Yellow"
        $SessionArgs = @{
            ComputerName = $copyToDeviceId
            SessionOption = New-CimSessionOption -Protocol Dcom
        }
        $MethodArgsEnablePS = @{
            ClassName = 'Win32_Process'
            MethodName = 'Create'
            CimSession = New-CimSession @SessionArgs
            Arguments = @{ CommandLine = 'powershell -Command "Enable-PSRemoting -Force"' }
        }
        Invoke-CimMethod @MethodArgsEnablePS
        Start-Sleep -Seconds 5
    }

    Write-Color "Creating PSSession..." "Cyan"
    $maxAttempts = 3
    $attempt = 0
    $session = $null

    while ($attempt -lt $maxAttempts -and $null -eq $session) {
        $attempt++
        try {
            $session = New-PSSession -ComputerName $copyToDeviceId -Credential $Credentials -ErrorAction Stop
            break
        }
        catch {
            if ($attempt -lt $maxAttempts) {
                Write-Color "PSSession creation attempt $attempt failed. Retrying in 5 seconds..." "Yellow"
                Start-Sleep -Seconds 5
            }
            else {
                Write-Color "Failed to create PSSession after $maxAttempts attempts: $_" "Red"
                return
            }
        }
    }

    if ($null -eq $session) {
        Write-Color "Could not establish PSSession with $copyToDeviceId" "Red"
        return
    }

    try {
        # Copy required files using dynamic paths
        $installerPaths = Get-InstallerPaths -TargetComputer $copyToDeviceId
        $copyTasks = @(
            @{ Name = "Splashtop"; Source = $installerPaths.Splashtop; Dest = "\\$copyToDeviceId\c$\Files\Splashtop"; IsFolder = $true },
            @{ Name = ".NET 3.5 SXS"; Source = $installerPaths.DotNetSxs; Dest = "\\$copyToDeviceId\c$\Files\sxs"; IsFolder = $true },
            @{ Name = "Lexmark Driver"; Source = $installerPaths.LexmarkDriver; Dest = "\\$copyToDeviceId\c$\Files\Lexmark_Universal_v2_UD1_PostScript_3_Emulation"; IsFolder = $true },
            @{ Name = "FSTools"; Source = $installerPaths.FSTools; Dest = "\\$copyToDeviceId\c$\Files\FSTools"; IsFolder = $true },
            @{ Name = "Nuance"; Source = $installerPaths.Nuance; Dest = "\\$copyToDeviceId\c$\Files\Nuance121"; IsFolder = $true },
            @{ Name = "Dell Command Update"; Source = $installerPaths.DellCommandUpdate; Dest = "\\$copyToDeviceId\c$\Files\dcu\Commandupdate.EXE" },
            @{ Name = "Windows Photo Viewer"; Source = $installerPaths.PhotoViewer; Dest = "\\$copyToDeviceId\c$\Files\Win_Photo_Viewer.reg" },
            @{ Name = "Volume Script"; Source = $installerPaths.VolumeScript; Dest = "\\$copyToDeviceId\c$\Files\Volume.ps1" },
            @{ Name = "BitLocker Script"; Source = $installerPaths.BitLockerScript; Dest = "\\$copyToDeviceId\c$\Files\BitLockerAD.ps1" }
        )

        foreach ($task in $copyTasks) {
            Write-Color "Copying $($task.Name)" "Cyan"
            try {
                if ($script:useLocalInstallers) {
                    # Use remote command to copy from local D:\ drive on target computer
                    Invoke-Command -ComputerName $copyToDeviceId -Credential $script:storedCredentials -ScriptBlock {
                        param($SourcePath, $DestPath, $IsFolder)

                        if (Test-Path $SourcePath) {
                            # Create destination directory
                            $destFolder = if ($IsFolder) { $DestPath } else { Split-Path $DestPath -Parent }
                            if (-not (Test-Path $destFolder)) {
                                New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
                            }

                            if ($IsFolder) {
                                # Copy folder contents using robocopy
                                $result = Start-Process -FilePath "robocopy" -ArgumentList "`"$SourcePath`"", "`"$DestPath`"", "/E", "/R:3", "/W:10" -Wait -PassThru -NoNewWindow
                                if ($result.ExitCode -le 7) {
                                    Write-Host "Folder copied successfully" -ForegroundColor Green
                                } else {
                                    Write-Host "Warning: Folder copy completed with exit code: $($result.ExitCode)" -ForegroundColor Yellow
                                }
                            } else {
                                # Copy single file
                                Copy-Item -Path $SourcePath -Destination $DestPath -Force
                                Write-Host "File copied successfully" -ForegroundColor Green
                            }
                        } else {
                            Write-Host "Warning: Source not found at $SourcePath" -ForegroundColor Yellow
                        }
                    } -ArgumentList $task.Source, ($task.Dest -replace "\\\\[^\\]+\\c\$\\", "C:\"), $task.IsFolder
                } else {
                    # Network mode - use original Copy-Item method
                    if ($task.IsFolder) {
                        # Create destination folder with correct name
                        $destFolder = Split-Path $task.Dest -Parent
                        if (-not (Test-Path $task.Dest)) {
                            New-Item -Path $task.Dest -ItemType Directory -Force | Out-Null
                        }
                        # Copy contents to the correctly named folder
                        Copy-Item -Path "$($task.Source)\*" -Destination $task.Dest -Recurse -Force
                    } else {
                        # Handle single file copy
                        $destFolder = Split-Path $task.Dest -Parent
                        if (-not (Test-Path $destFolder)) {
                            New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
                        }
                        Copy-Item -Path $task.Source -Destination $task.Dest -Force
                    }
                }
                Write-Color "Success: $($task.Name)" "Green"
            }
            catch {
                Write-Color "Failed: $($task.Name) - $_" "Red"
            }
        }

        # Execute remote configuration
        Invoke-Command -Session $session -ScriptBlock {
            # System Information
            Write-Host "*** Running new computer setup Without Office 365"
            Write-Host ""
            $ComputerName = $env:computername
            $ComputerBIOS = Get-WmiObject -Class Win32_Bios
            $ComputerSN = $ComputerBIOS.SerialNumber
            $ComputerModel = (Get-WmiObject -Class Win32_ComputerSystem).Model
            $ComputerMemory = Get-CimInstance Win32_PhysicalMemory | Measure-Object -Property capacity -Sum | Select-Object -ExpandProperty Sum
            $ComputerMemoryGB = [math]::Round($ComputerMemory / 1GB, 2)

            Write-Host "*** Setting Pagefile info"
            Write-Host "*** Computer Name: $ComputerName"
            Write-Host "*** Computer Model: $ComputerModel"
            Write-Host "*** Computer Serial: $ComputerSN"
            Write-Host "*** Computer RAM: $ComputerMemoryGB GB"

            # Install Splashtop
            if ((Test-Path "C:\Program Files (x86)\Splashtop\Splashtop Remote\Server\SRServer.exe") -or
                (Test-Path "C:\Program Files\Splashtop\Splashtop Remote\Server\SRServer.exe")) {
                Write-Host "***" -ForegroundColor Green
                Write-Host "Splashtop Remote is installed"
                Write-Host "Stopping SplashtopRemoteService"
                Stop-Service -Name "SplashtopRemoteService"
                Start-Sleep -Seconds 5
                Write-Host "Starting SplashtopRemoteService"
                Start-Service -Name "SplashtopRemoteService"
                Write-Host "Restarting SplashtopRemoteService Just in case"
                Restart-Service -Name "SplashtopRemoteService"
            } else {
                Write-Host "***" -ForegroundColor Green
                Write-Host "Installing Splashtop Remote"
                $siteCode = "TIIHJDTLCNDN"
                Write-Host "Selected Site Code: $siteCode"
                Start-Process -FilePath "msiexec.exe" -ArgumentList "/i C:\Files\Splashtop\Splashtop_Streamer_v3.5.8.3.msi /q USERINFO='sc=splashtop.mclaren.org:443,dcode=$siteCode,hidewindow=1,confirm_d=0'" -Wait
                Write-Host "Installation complete"
                Remove-Item "C:\Files\Splashtop" -Recurse -Force
            }

            # Install .NET Framework 3.5
            Write-Host "*****"
            Write-Host "Installing .NET Framework 3.5"
            $destinationPath2 = "C:\files\sxs"
            if (-Not (Test-Path -Path $destinationPath2)) {
                New-Item -ItemType Directory -Path $destinationPath2 -Force
            }
            Start-Process -FilePath "DISM.exe" -ArgumentList "/Online /enable-feature /featurename:netfx3 /all /Source:$destinationPath2 /LimitAccess" -Wait
            Remove-Item -Path "$destinationPath2\*" -Recurse -Force
            Write-Host ".NET Framework 3.5 installed"

            # Configure Local Admin Account
            $username = "bayfs-local"
            $password = ConvertTo-SecureString "FSMIS302!" -AsPlainText -Force
            $description = "bay fs Local Admin"
            Write-Host "***" -ForegroundColor Green
            Write-Host "*** Adding Local Admin Account $username"
            New-LocalUser -Name $username -Password $password -Description $description -FullName $username
            Add-LocalGroupMember -Group "Administrators" -Member $username
            Get-LocalUser $username
            Get-LocalGroupMember -Group "Administrators"

            # Install Lexmark Driver (handled by printer migration section)
            Write-Host "***" -ForegroundColor Green
            Write-Host "*** Lexmark driver will be installed during printer migration"

            # Configure Printers
            Write-Host "***" -ForegroundColor Green
            Write-Host "*** Removing generic printers"
            Remove-Printer -Name "Microsoft XPS Document Writer" -ErrorAction Ignore
            Remove-Printer -Name "Microsoft Print to PDF" -ErrorAction Ignore
            Remove-Printer -Name "Fax" -ErrorAction Ignore
            Set-ItemProperty -path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -name HideFastUserSwitching -type DWord -value 0

            # Configure Volume and BitLocker
            Write-Host "***" -ForegroundColor Green
            Write-Host "*** Setting Volume"
            Start-Job -ScriptBlock {
                powershell.exe -ExecutionPolicy Bypass -File "C:\Files\Volume.ps1"
            } | Out-Null

            Write-Host "***" -ForegroundColor Green
            Write-Host "*** Adding BitLocker Recovery in AD"
            Start-Job -ScriptBlock {
                powershell.exe -ExecutionPolicy Bypass -File "C:\Files\BitLockerAD.ps1"
            } | Out-Null

            # Configure NumLock
            Write-Host "***" -ForegroundColor Green
            Write-Host "*** Turning NumLock On at Start-up for Windows 10"
            Set-ItemProperty -path "HKCU:\Control Panel\Keyboard" -name "InitialKeyboardIndicators" -value "2"
            Set-ItemProperty -path "Registry::HKEY_USERS\.DEFAULT\Control Panel\Keyboard" -name "InitialKeyboardIndicators" -value "2"
            Set-ItemProperty -path "Registry::HKEY_USERS\S-1-5-19\Control Panel\Keyboard" -name "InitialKeyboardIndicators" -value "2"
            Set-ItemProperty -path "Registry::HKEY_USERS\S-1-5-20\Control Panel\Keyboard" -name "InitialKeyboardIndicators" -value "2"

            # Configure Windows Photo Viewer
            Write-Host "***" -ForegroundColor Green
            Write-Host "*** Adding Windows Photo Viewer"
            reg import "C:\Files\Win_Photo_Viewer.reg" *>&1 | Out-Null

            # Configure Edge
            Write-Host "***" -ForegroundColor Green
            Write-Host "*** Disabling Edge desktop shortcut creation"
            New-ItemProperty -Path "HKLM:SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" -Name "DisableEdgeDesktopShortcutCreation" -Value "1" -PropertyType DWORD -Force | Out-Null

            # Install Java
            Write-Host "***" -ForegroundColor Green
            if (-not (Test-Path "C:\Program Files (x86)\Java\jre7\bin\client\jvm.dll" -PathType Leaf)) {
                Write-Host "*** Installing Java RE 7 Update 45"
                Start-Process -FilePath "C:\Files\jre1.7.0_45.msi" -ArgumentList "/quiet" -Wait
            } else {
                Write-Host "*** Java RE 7 Update 45 already installed"
            }

        }

        # Imprivata Type 2 installation will be handled by external script
    }
    finally {
        if ($null -ne $session) {
            Remove-PSSession $session
        }
    }
}
# Main script execution function
function Start-MainScript {
    # Initialize credentials
    if (-not (Initialize-Credentials)) {
        Write-Color "Cannot proceed without valid credentials" "Red"
        return
    }

    # Display welcome banner
    Show-Banner "*** SYSTEM MIGRATION UTILITY v2.0 (SUPER AUTO) ***" "Magenta"
    Write-Host "  Welcome to the super automated system migration utility!" -ForegroundColor "Green"
    Write-Host "  This tool will automatically perform all operations except computer renaming." -ForegroundColor "Green"
    Write-Host "  Developed by: The greatest technician that ever lived" -ForegroundColor "DarkGray"
    Write-Host ""

    # Configuration Options
    Show-Banner "*** CONFIGURATION OPTIONS ***" "Yellow"

    # Local installer option (ask first)
    Write-Color "Do you want to use local installer files (D:\) instead of network paths? (Y/N): " "Yellow" -NoNewline
    $localInstallerInput = Read-Host
    $script:useLocalInstallers = $localInstallerInput -match '^(Y|y)$'

    if ($script:useLocalInstallers) {
        Write-Color "Local installer mode enabled - using D:\ paths on target computer" "Green"
        Write-Color "Note: This will check for files on D:\ of the target computer (copytopc)" "Gray"
    } else {
        Write-Color "Network installer mode enabled - using network paths" "Green"
    }
    Write-Host ""

    # Auto-yes option (ask second)
    Write-Color "Do you want to automatically answer 'Yes' to all prompts? (Y/N): " "Yellow" -NoNewline
    $autoYesInput = Read-Host
    $script:autoYes = $autoYesInput -match '^(Y|y)$'
    if ($script:autoYes) {
        Write-Color "Auto-yes mode enabled - all prompts will be automatically approved" "Green"
    } else {
        Write-Color "Manual mode enabled - you will be prompted for each option" "Yellow"
    }
    Write-Host ""

    # Credential management option
    Write-Color "Do you want to clear saved credentials? (Y/N): " "Yellow" -NoNewline
    $clearCredsInput = Read-Host
    if ($clearCredsInput -match '^(Y|y)$') {
        Clear-SavedCredentials
        Write-Color "You will need to re-enter credentials on next run." "Gray"
        Write-Host ""
    }

    # Profile backup variables
    $daysInput = Read-Host -Prompt "Enter maximum profile age in days (default 30)"
    if (-not [int]::TryParse($daysInput, [ref]$null)) { $daysInput = 30 }
    $cutoffDays = [math]::Max(1, [int]$daysInput)
    $cutoffDate = (Get-Date).AddDays(-$cutoffDays)
    $copyFromDeviceId = Read-Host -Prompt "`nEnter (copy from) Hostname or IP"
    $copyToDeviceId = Read-Host "Enter the (copy to) device ID"

    # Store current date for logging purposes if needed
    $BackupPath = "\\$copyToDeviceId\c$\Files\user_profile_backups\"

    # Start profile backup process
    Show-Banner ">>> PROFILE BACKUP STARTING <<<" "Yellow"
    Show-Progress "Checking system connectivity and profiles" 1 6 "Yellow"

    $result = Test-PCOnlineAndDNS -computerName $copyFromDeviceId
    $result2 = Test-PCOnlineAndDNS -computerName $copyToDeviceId
    if (-not ($result.Online -and $result2.Online)) {
        if (-not $result.Online) {
            Write-Color "$copyFromDeviceId is offline. Error: $($result.Error)" "Red"
        }
        if (-not $result2.Online) {
            Write-Color "$copyToDeviceId is offline. Error: $($result2.Error)" "Red"
        }
        return
    }

    Write-Color ""
    Write-Color "$copyFromDeviceId is online. DNS Status: $($result.DNSStatus)" "Green"
    Write-Color ""
    Write-Color "$copyToDeviceId is online. DNS Status: $($result2.DNSStatus)" "Green"
    Write-Color ""

    # AD Configuration with banner
    Show-Banner "<<< ACTIVE DIRECTORY CONFIGURATION >>>" "Blue"
    Show-Progress "Configuring AD group memberships and OU placement" 2 6 "Yellow"
    Write-Color "Would you like to copy AD group memberships and place the new PC in the same OU as the old one? (Y/N)  " "Yellow" -NoNewline
    if ($script:autoYes) {
        Write-Host "Y"
        $adOption = "Y"
    } else {
        $adOption = Read-Host
    }

    if ($adOption -match '^(Y|y)$') {
        try {
            Import-Module ActiveDirectory -ErrorAction Stop

            # Region: Account Validation
            $source = $copyFromDeviceId.Trim()
            $destination = $copyToDeviceId.Trim()
            $sourceAD = if ($source[-1] -ne '$') { "$source$" } else { $source }
            $destinationAD = if ($destination[-1] -ne '$') { "$destination$" } else { $destination }

            if (-not (Get-ADComputer -Identity $sourceAD -ErrorAction SilentlyContinue)) {
                Write-Color "[!] Source computer '$sourceAD' not found in Active Directory" "Red"
                exit
            }

            if (-not (Get-ADComputer -Identity $destinationAD -ErrorAction SilentlyContinue)) {
                Write-Color "[!] Destination computer '$destinationAD' not found in Active Directory" "Red"
                exit
            }

            # Region: Group Membership Processing
            Write-Color "`n[1/3] Preparing group membership changes..." "Cyan"

            $oldGroups = Get-ADComputer -Identity $destinationAD -Properties MemberOf |
                Select-Object -ExpandProperty MemberOf |
                ForEach-Object { (Get-ADGroup $_).Name } |
                Where-Object { $_ -ne "Domain Computers" }

            Write-Color "  Current groups on $destinationAD :" "Yellow"
            $oldGroups | Sort-Object | ForEach-Object { Write-Color "    $_" "Gray" }

            # Remove existing non-default groups
            $oldGroups | ForEach-Object {
                try {
                    Remove-ADGroupMember -Identity $_ -Members $destinationAD -Confirm:$false -ErrorAction Stop
                    Write-Color "    Removed: $_" "DarkRed"
                }
                catch {
                    Write-Color "    [!] Failed removing $_ : $_" "Red"
                }
            }

            # Region: Group Copy Operations
            Write-Color "`n[2/3] Copying group memberships..." "Cyan"

            $sourceGroups = Get-ADComputer -Identity $sourceAD -Properties MemberOf |
                Select-Object -ExpandProperty MemberOf |
                ForEach-Object { (Get-ADGroup $_).Name }

            $sourceGroups | ForEach-Object {
                try {
                    Add-ADGroupMember -Identity $_ -Members $destinationAD -ErrorAction Stop
                    Write-Color "    Added: $_" "DarkGreen"
                }
                catch {
                    Write-Color "    [!] Failed adding $_ : $_" "Red"
                }
            }

            # Region: OU Migration
            Write-Color "`n[3/3] Processing OU relocation..." "Cyan"
            try {
                $sourceOU = (Get-ADComputer $sourceAD).DistinguishedName -replace '^CN=[^,]+,', ''
                $targetComputerDN = (Get-ADComputer $destinationAD).DistinguishedName
                $currentOU = (Get-ADComputer $destinationAD).DistinguishedName -replace '^CN=[^,]+,', ''

                if ($sourceOU -ne $currentOU) {
                    Move-ADObject -Identity $targetComputerDN -TargetPath $sourceOU
                    Write-Color "  Successfully moved to OU: $sourceOU" "Green"
                }
                else {
                    Write-Color "  Computer already in correct OU: $sourceOU" "Yellow"
                }
            }
            catch {
                Write-Color "  [!] OU move failed: $_" "Red"
            }

            # Final Verification
            Write-Color "`n[Complete] AD configuration summary:" "Cyan"
            Write-Color "  Source Computer: $sourceAD" "Gray"
            Write-Color "  Target Computer: $destinationAD" "Gray"
            Write-Color "  Groups Transferred: $($sourceGroups.Count)" "Gray"
            Write-Color "  Final OU: $sourceOU" "Gray"
        }
        catch {
            Write-Color "[!] Critical error in AD operations: $_" "Red"
        }
    }

    # Printer Backup with banner
    Show-Banner "*** PRINTER MIGRATION ***" "Green"
    Show-Progress "Configuring printer settings" 3 6 "Yellow"
    Write-Color "Do you want to perform printer backup and restore? (Y/N)  " "Yellow" -NoNewline
    if ($script:autoYes) {
        Write-Host "Y"
        $printerBackupChoice = "Y"
    } else {
        $printerBackupChoice = Read-Host
    }

    if ($printerBackupChoice -match '^(Y|y)$') {
        try {
            Write-Color "`n[1/5] Verifying network connectivity..." "Cyan"
            Test-Connection $copyFromDeviceId -Count 1 -ErrorAction Stop | Out-Null
            Test-Connection $copyToDeviceId -Count 1 -ErrorAction Stop | Out-Null

            # Enable PSRemoting if needed
            Write-Color "`nChecking PSRemoting status..." "Cyan"
            foreach ($computer in @($copyFromDeviceId, $copyToDeviceId)) {
                $winrm = Get-WmiObject -Class Win32_Service -ComputerName $computer | Where-Object { $_.Name -eq 'WinRM' }
                if ($winrm.Status -ne "Running") {
                    Write-Color "Enabling PSRemoting on $computer..." "Yellow"
                    $SessionArgs = @{
                        ComputerName = $computer
                        SessionOption = New-CimSessionOption -Protocol Dcom
                    }
                    $MethodArgsEnablePS = @{
                        ClassName = 'Win32_Process'
                        MethodName = 'Create'
                        CimSession = New-CimSession @SessionArgs
                        Arguments = @{ CommandLine = 'powershell -Command "Enable-PSRemoting -Force"' }
                    }
                    Invoke-CimMethod @MethodArgsEnablePS
                    Start-Sleep -Seconds 5
                }
            }

            # Install Lexmark Driver first
            Write-Color "`n[2/6] Installing Lexmark Universal Driver..." "Cyan"

            try {
                # Copy driver files from local D:\ drive on target computer to C:\Files\
                Write-Color "Copying Lexmark driver files from D:\ to C:\Files\ on $copyToDeviceId..." "Gray"

                Invoke-Command -ComputerName $copyToDeviceId -Credential $script:storedCredentials -ScriptBlock {
                    param($UseLocalInstallers)

                    if ($UseLocalInstallers) {
                        # Copy from local D:\ drive to C:\Files\
                        $sourcePath = "D:\LexmarkGDI"
                        $destPath = "C:\Files\Drivers\Print\GDI"

                        if (Test-Path $sourcePath) {
                            # Create destination directory
                            if (-not (Test-Path $destPath)) {
                                New-Item -Path $destPath -ItemType Directory -Force | Out-Null
                            }

                            # Copy files using robocopy on the target computer
                            $result = Start-Process -FilePath "robocopy" -ArgumentList "`"$sourcePath`"", "`"$destPath`"", "/E", "/R:3", "/W:10" -Wait -PassThru -NoNewWindow

                            if ($result.ExitCode -le 7) {
                                Write-Host "Lexmark driver files copied successfully from local D:\ drive" -ForegroundColor Green
                            } else {
                                Write-Host "Warning: Lexmark driver copy completed with exit code: $($result.ExitCode)" -ForegroundColor Yellow
                            }
                        } else {
                            Write-Host "Warning: Lexmark driver source not found at $sourcePath" -ForegroundColor Yellow
                        }
                    } else {
                        Write-Host "Network mode - Lexmark driver will be copied from network location" -ForegroundColor Gray
                    }
                } -ArgumentList $script:useLocalInstallers

                # Install the print driver
                Invoke-Command -ComputerName $copyToDeviceId -Credential $script:storedCredentials -ScriptBlock {
                    $driverPath = "C:\Files\Drivers\Print\GDI\LMUD1n40.inf"
                    if (Test-Path $driverPath) {
                        Pnputil /add-driver $driverPath /install
                        Add-PrinterDriver -Name "Lexmark Universal v2 PostScript 3 Emulation" -ErrorAction SilentlyContinue
                        Remove-Item -Path "C:\Files\Drivers" -Recurse -Force -ErrorAction SilentlyContinue
                        Write-Host "Lexmark driver installed and files cleaned up." -ForegroundColor Green
                    } else {
                        Write-Host "Warning: Lexmark driver file not found at $driverPath" -ForegroundColor Yellow
                    }
                }

                # If network mode, copy from network location as fallback
                if (-not $script:useLocalInstallers) {
                    $installerPaths = Get-InstallerPaths -TargetComputer $copyToDeviceId
                    $sourceFolderPath = $installerPaths.LexmarkGDI
                    $destinationFolderPath = "\\$copyToDeviceId\C$\Files\Drivers\Print\GDI"

                    Write-Color "Network mode: Copying Lexmark driver from network location..." "Gray"
                    robocopy $sourceFolderPath $destinationFolderPath /E /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /IS /IT
                }
            }
            catch {
                Write-Color "Warning: Failed to install Lexmark driver - $_" "Yellow"
            }



            $DriverTempPath = "\\$copyToDeviceId\c`$\Temp\Drivers\"
            $exportPath = "\\$copyFromDeviceId\c`$\Temp\Drivers\"
            $ExcludeList = "XPS|Fax|PDF|OneNote|MSFT|CutePDF|Send to Microsoft OneNote|Microsoft Print To PDF|Microsoft XPS Document Writer|Snagit"

            # Create temporary directories
            Write-Color "`n[3/6] Preparing temporary directories..." "Cyan"

            # Function to create PSSession with retry
            function New-RetryPSSession {
                param($Computer)
                $maxAttempts = 3
                $attempt = 0
                $session = $null

                while ($attempt -lt $maxAttempts -and $null -eq $session) {
                    $attempt++
                    try {
                        $session = New-PSSession -ComputerName $Computer -Credential $script:storedCredentials -ErrorAction Stop
                        break
                    }
                    catch {
                        if ($attempt -lt $maxAttempts) {
                            Write-Color "PSSession creation attempt $attempt failed. Retrying in 5 seconds..." "Yellow"
                            Start-Sleep -Seconds 5
                        }
                        else {
                            Write-Color "Failed to create PSSession after $maxAttempts attempts: $_" "Red"
                            throw
                        }
                    }
                }
                return $session
            }

            # Create source session
            $sourceSession = New-RetryPSSession -Computer $copyFromDeviceId
            Invoke-Command -Session $sourceSession -ScriptBlock {
                New-Item -Path $using:exportPath -ItemType Directory -Force -ErrorAction Stop | Out-Null
            }
            Remove-PSSession $sourceSession

            # Create target session
            $targetSession = New-RetryPSSession -Computer $copyToDeviceId
            Invoke-Command -Session $targetSession -ScriptBlock {
                New-Item -Path $using:DriverTempPath -ItemType Directory -Force -ErrorAction Stop | Out-Null
            }
            Remove-PSSession $targetSession

            # Get source printers
            Write-Color "`n[4/6] Retrieving source printers..." "Cyan"
            $sourceSession = New-RetryPSSession -Computer $copyFromDeviceId
            $printers = Invoke-Command -Session $sourceSession -ScriptBlock {
                Get-CimInstance -ClassName Win32_Printer | Where-Object {
                    $_.Name -notmatch $using:ExcludeList -and
                    $_.DriverName -notmatch '^Microsoft'
                } | ForEach-Object {
                    [PSCustomObject]@{
                        Name = $_.Name
                        DriverName = $_.DriverName
                        PortName = $_.PortName
                        Shared = $_.Shared
                        Location = $_.Location
                    }
                }
            } -ErrorAction Stop

            Write-Color "  Found $($printers.Count) printers on source computer" "Gray"
            $printers | ForEach-Object {
                Write-Color "  [Source] $($_.Name) | Driver: $($_.DriverName)" "DarkGray"
            }

            # Close source session
            Remove-PSSession $sourceSession

            # Process printers
            Write-Color "`n[5/6] Migrating printers..." "Cyan"
            $successCount = 0
            $failureCount = 0

            foreach ($printer in $printers) {
                Write-Color "  Processing: $($printer.Name)" "Gray"
                try {
                    Invoke-Command -ComputerName $copyToDeviceId -ScriptBlock {
                        param($printerName, $driverName, $portName)

                        # Create port if missing
                        if (-not (Get-PrinterPort -Name $portName -ErrorAction SilentlyContinue)) {
                            Add-PrinterPort -Name $portName -ErrorAction Stop | Out-Null
                        }

                        # Create printer if missing
                        if (-not (Get-Printer -Name $printerName -ErrorAction SilentlyContinue)) {
                            # Use Lexmark Universal driver for Lexmark printers
                            $actualDriverName = if ($printerName -like "*Lexmark*" -or $driverName -like "*Lexmark*") {
                                "Lexmark Universal v2 PostScript 3 Emulation"
                            } else {
                                $driverName
                            }
                            Add-Printer -Name $printerName -DriverName $actualDriverName -PortName $portName -ErrorAction Stop | Out-Null
                        }
                    } -ArgumentList $printer.Name, $printer.DriverName, $printer.PortName -ErrorAction Stop

                    Write-Color "    Success: $($printer.Name)" "Green"
                    $successCount++
                }
                catch {
                    Write-Color "    [!] Failed: $($printer.Name) - $($_.Exception.Message)" "Red"
                    $failureCount++
                }
            }

            # Cleanup
            Write-Color "`n[6/6] Removing temporary files..." "Cyan"
            Invoke-Command -ComputerName $copyFromDeviceId -ScriptBlock {
                Remove-Item -Path $using:exportPath -Recurse -Force -ErrorAction SilentlyContinue | Out-Null
            }
            Invoke-Command -ComputerName $copyToDeviceId -ScriptBlock {
                Remove-Item -Path $using:DriverTempPath -Recurse -Force -ErrorAction SilentlyContinue | Out-Null
            }

            # Results summary
            Write-Color "`n[Migration Complete]" "Cyan"
            Write-Color "  Attempted printers: $($printers.Count)" "Gray"
            Write-Color "  Successful: $successCount" "Green"
            Write-Color "  Failed: $failureCount" "Red"
        }
        catch {
            Write-Color "`n[!] Critical printer migration error: $($_.Exception.Message)" "Red"
        }
    }

    # Create necessary directories
    if (-not (Test-Path $BackupPath)) {
        Show-Spinner "Creating backup directory..." 1
        New-Item -Path $BackupPath -ItemType Directory -Force | Out-Null
    }

    if (-not (Test-Path "C:\files\ProfileBackup")) {
        Show-Spinner "Creating profile backup directory..." 1
        New-Item -Path "C:\files\ProfileBackup" -ItemType Directory -Force | Out-Null
    }

    # Profile backup with banner
    Show-Banner "=== PROFILE BACKUP IN PROGRESS ===" "Green"
    Show-Progress "Retrieving user profiles" 2 7 "Yellow"

    $UserFile = "C:\files\ProfileBackup\Users.txt"
    Show-Spinner "Getting profiles from source computer..." 2
    (Get-CimInstance -ClassName Win32_UserProfile -ComputerName $copyFromDeviceId).LocalPath | Out-File $UserFile
    $Users = Get-Content -Path $UserFile

    # Process profiles
    $ProfileInfoList = @()
    ForEach ($User in $Users) {
        if ($User -ne "C:\WINDOWS\system32\config\systemprofile" -and
            $User -ne "C:\WINDOWS\ServiceProfiles\NetworkService" -and
            $User -ne "C:\WINDOWS\ServiceProfiles\LocalService" -and
            $User -notlike "*ark*" -and
            $User -notlike "*Imprivata*") {

            $ModifiedPath = $User.Replace("C:\", "\\$copyFromDeviceId\c$\")
            $paths = @(
                "$ModifiedPath\Desktop\Shortcuts",
                "$ModifiedPath\Desktop\McLaren Safety First.url",
                "$ModifiedPath\AppData\Local\Microsoft\Edge\User Data\Default",
                "$ModifiedPath\Desktop\Microsoft Edge.lnk"
            )
            $UserModifiedDate = $null
            foreach ($path in $paths) {
                if (Test-Path -Path $path) {
                    $UserModifiedDate = (Get-Item -Path $path).LastWriteTime
                    break
                }
            }
            if (-not $UserModifiedDate) { $UserModifiedDate = Get-Date "2000-01-01" }
            $ProfileInfoList += [PSCustomObject]@{ User = $User; ModifiedDate = $UserModifiedDate }
        }
    }

    # Copy profiles
    $ProfilesToCopy = $ProfileInfoList | Where-Object { $_.ModifiedDate -ge $cutoffDate }
    if ($ProfilesToCopy.Count -eq 0) {
        $MostRecentProfile = $ProfileInfoList | Sort-Object -Property ModifiedDate -Descending | Select-Object -First 1
        if ($MostRecentProfile) {
            Write-Color "No profiles found within the last $cutoffDays days. Copying the most recently used profile: $($MostRecentProfile.User) (Last modified: $($MostRecentProfile.ModifiedDate))" "Yellow"
            $ProfilesToCopy = @($MostRecentProfile)
        } else {
            Write-Color "No eligible user profiles found to copy." "Red"
        }
    }

    foreach ($Profile in $ProfilesToCopy) {
        $UserOnly = $Profile.User.Replace('C:\Users\<USER>\',"\\$copyFromDeviceId\c$\")
        $UserDestinationPath = Join-Path $BackupPath $UserOnly
        Write-Color " " "Gray"
        Write-Color "Backing up $UserOnly (Last modified: $($Profile.ModifiedDate.ToString('yyyy-MM-dd')))" "Cyan"

        $paths = @{
            Chrome = @{
                Source = "$UserSourcePath\AppData\Local\Google\Chrome\User Data\Default"
                Dest = "$UserDestinationPath\AppData\Local\Google\Chrome\User Data\Default"
                Files = "Bookmarks.*"
            }
            Edge = @{
                Source = "$UserSourcePath\AppData\Local\Microsoft\Edge\User Data\Default"
                Dest = "$UserDestinationPath\AppData\Local\Microsoft\Edge\User Data\Default"
                Files = "Bookmarks.*"
            }
            Desktop = @{
                Source = "$UserSourcePath\Desktop"
                Dest = "$UserDestinationPath\Desktop"
            }
            Documents = @{
                Source = "$UserSourcePath\Documents"
                Dest = "$UserDestinationPath\Documents"
            }
            Downloads = @{
                Source = "$UserSourcePath\Downloads"
                Dest = "$UserDestinationPath\Downloads"
            }
            Favorites = @{
                Source = "$UserSourcePath\Favorites"
                Dest = "$UserDestinationPath\Favorites"
            }
        }

        foreach ($path in $paths.GetEnumerator()) {
            try {
                if ($path.Value.Files) {
                    robocopy $path.Value.Source $path.Value.Dest $path.Value.Files /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /IS /IT | Out-Null
                } else {
                    robocopy $path.Value.Source $path.Value.Dest /E /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /IS /IT | Out-Null
                }
            } catch {
                Write-Color "Error copying $($path.Key): $_" "Red"
            }
        }
    }
Show-Progress "Moving to printer scripts and public desktop backup" 4 7 "Yellow"

# --- BACKUP .VBS PRINTER SCRIPTS AND PUBLIC DESKTOP ---

Write-Color "Backing up .vbs printer scripts in startup folder" "Cyan"
$sourceStartup = "\\$copyFromDeviceId\c$\ProgramData\Microsoft\Windows\Start Menu\Programs\Startup"
$destStartup   = "\\$copyToDeviceId\c$\ProgramData\Microsoft\Windows\Start Menu\Programs\Startup"
$vbsFiles = Get-ChildItem -Path $sourceStartup -Filter *.vbs -File -ErrorAction SilentlyContinue
foreach ($file in $vbsFiles) {
    $destFile = Join-Path $destStartup $file.Name
    try {
        Copy-Item -Path $file.FullName -Destination $destFile -Force
        Write-Color "Copied $($file.Name) from $copyFromDeviceId to $copyToDeviceId startup folder." "Green"
    } catch {
        Write-Color "Failed to copy $($file.Name): $_" "Red"
    }
}

Write-Color "Backing up Public desktop" "Cyan"
$USP1 = "\\$copyFromDeviceId\c$\Users\public\desktop"
$UDP1 = $BackupPath+"public\desktop"
try { robocopy $USP1 $UDP1 /E /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /XC /XN | Out-Null } catch { Write-Color "Error copying $USP1 : $_" "Red" }
Remove-Item -Path $UserFile -ErrorAction Ignore
if (-not (Get-ChildItem -Path "C:\files\ProfileBackup")) {
    Remove-Item -Path "C:\files\ProfileBackup" -Recurse -Force
}
    # Transition to next phase
Show-Banner "--- CREATING RESTORE SCRIPT ---" "Cyan"
Show-Progress "Setting up automatic profile restoration" 3 7 "Yellow"

# Create the restore script locally
$startupFolder = "C:\ProgramData\Microsoft\Windows\Start Menu\Programs\Startup"
$restoreScriptPath = Join-Path -Path $startupFolder -ChildPath "profile_restore.bat"
$restoreScriptContent = '@echo off
setlocal enabledelayedexpansion
set profile_dir=c:\users\<USER>\files\user_profile_backups\
IF EXIST "c:\files\user_profile_backups\" (
    echo Backup Folder located!
    set bckp_folder_empty=true
    echo ____________Backed Up Profiles________
    for /F %%i in (''dir /b /a "c:\files\user_profile_backups\*"'') do (
        echo %%i
        set bckp_folder_empty=false
        echo bckp_folder_empty: !bckp_folder_empty!
    )
    echo ______________________________________
    if !bckp_folder_empty!==false (
        echo Backup Folder contains profiles...
        echo Checking for existence of: c:\files\user_profile_backups\%USERNAME%\
        IF EXIST "c:\files\user_profile_backups\%USERNAME%\" (
            echo c:\files\user_profile_backups\%USERNAME%\ Exists!
            echo Checking for bookmarks file - c:\files\user_profile_backups\%USERNAME%\Bookmarks :
            IF EXIST c:\files\user_profile_backups\%USERNAME%\Bookmarks (
                Echo c:\files\user_profile_backups\%USERNAME%\Bookmark Found!
                Echo Copying to "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                IF NOT EXIST "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default" ECHO Chrome bookmark folder not built yet. Building..
                IF EXIST %profile_dir%\appdata\ (
                    IF EXIST %profile_dir%\appdata\Local\ (
                        IF EXIST %profile_dir%\appdata\Local\Google\ (
                            IF EXIST "%profile_dir%\appdata\Local\Google\Chrome\" (
                                IF EXIST "%profile_dir%\appdata\Local\Google\Chrome\User Data\" (
                                    IF EXIST "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default" (
                                        ECHO Chrome bookmark folder Found!
                                    ) ELSE (
                                        mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                                    )
                                ) ELSE (
                                    mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\"
                                    mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                                )
                            ) ELSE (
                                mkdir "%profile_dir%\appdata\Local\Google\Chrome\"
                                mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\"
                                mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                            )
                        ) ELSE (
                            mkdir %profile_dir%\appdata\Local\Google\
                            mkdir "%profile_dir%\appdata\Local\Google\Chrome\"
                            mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\"
                            mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                        )
                    ) ELSE (
                        mkdir %profile_dir%\appdata\Local\
                        mkdir %profile_dir%\appdata\Local\Google\
                        mkdir "%profile_dir%\appdata\Local\Google\Chrome\"
                        mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\"
                        mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                    )
                ) ELSE (
                    mkdir %profile_dir%\appdata\
                    mkdir %profile_dir%\appdata\Local\
                    mkdir %profile_dir%\appdata\Local\Google\
                    mkdir "%profile_dir%\appdata\Local\Google\Chrome\"
                    mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\"
                    mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                )
                Echo Copying bookmarks file
                ECHO F
                xcopy /Y /H /F /G /R /E c:\files\user_profile_backups\%USERNAME%\Bookmarks "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default\Bookmarks"
                del c:\files\user_profile_backups\%USERNAME%\Bookmarks
            )
            echo %profile_dir%
            xcopy /Y /H /F /G /R /E c:\files\user_profile_backups\%USERNAME% %profile_dir%
            RD /S /Q "c:\files\user_profile_backups\%USERNAME%"
            set bckp_folder_empty=true
            for /F %%i in (''dir /b /a "c:\files\user_profile_backups\*"'') do (
                set bckp_folder_empty=false
            )
            if !bckp_folder_empty!==true (
                echo No more backup profiles detected. Removing profile restore files.
                RD /S /Q "c:\files\user_profile_backups"
                IF EXIST "c:\programdata\microsoft\windows\start Menu\programs\startup\profile_restore.bat" (
                    del /q "c:\programdata\microsoft\windows\start Menu\programs\startup\profile_restore.bat"
                )
            ) else (
                echo More backup profiles detected. Leaving profile restore files.
            )
        ) else (
            echo Backup for %USERNAME% doesn''t Exist!
        )
    ) else (
        echo Backup folder exists, but is empty!
        RD /S /Q "c:\files\user_profile_backups"
        IF EXIST "c:\programdata\microsoft\windows\start Menu\programs\startup\profile_restore.bat" (
            del /q "c:\programdata\microsoft\windows\start Menu\programs\startup\profile_restore.bat"
        )
    )
) ELSE (
    echo No Backup Folder on this PC
    IF EXIST "c:\programdata\microsoft\windows\start Menu\programs\startup\profile_restore.bat" (
        del /q "c:\programdata\microsoft\windows\start Menu\programs\startup\profile_restore.bat"
    )
)'

Try {
    Set-Content -Path $restoreScriptPath -Value $restoreScriptContent
    $remoteStartupFolder = "\\$copyToDeviceId\c$\ProgramData\Microsoft\Windows\Start Menu\Programs\Startup"
    Copy-Item -Path $restoreScriptPath -Destination $remoteStartupFolder -Force
    Write-Host "Script copied to $remoteStartupFolder"
} Catch {
    Write-Host "Failed to create or copy script: $_"
}

    # Imprivata Configuration with banner
    Show-Banner "+++ IMPRIVATA CONFIGURATION +++" "Magenta"
    Show-Progress "Setting up Imprivata authentication" 5 7 "Yellow"
    Write-Color "`n[Imprivata] Determining script to run based on membership..." "Cyan"

    # Get group memberships
    $sourceAD = if ($copyFromDeviceId[-1] -ne '$') { "$copyFromDeviceId$" } else { $copyFromDeviceId }
    $sourceGroups = try {
        (Get-ADComputer $sourceAD -Properties MemberOf -ErrorAction Stop).MemberOf |
        ForEach-Object { (Get-ADGroup $_ -ErrorAction Stop).Name }
    } catch {
        Write-Color "  [!] AD group check failed: $_" "Yellow"
        @()
    }

    $installType1 = $sourceGroups | Where-Object { $_ -like "*type1*"}
    $installType2 = $sourceGroups | Where-Object { $_ -like "*type2*"}

    # Execute appropriate configuration based on type membership
    if ($installType2) {
        Write-Color "Type 2 membership detected - Running no office configuration with Type 2 Imprivata" "Yellow"
        Invoke-NoOfficeConfiguration -copyToDeviceId $copyToDeviceId -Credentials $script:storedCredentials

        # Create PSSession for Type 2 configuration
        Write-Color "Creating PSSession for Type 2 configuration..." "Cyan"
        $session = $null
        try {
            $session = New-PSSession -ComputerName $copyToDeviceId -Credential $script:storedCredentials -ErrorAction Stop
            Invoke-Type2Configuration -copyToDeviceId $copyToDeviceId -copyFromDeviceId $copyFromDeviceId -Credentials $script:storedCredentials -session $session
        }
        catch {
            Write-Color "Error creating PSSession for Type 2 configuration: $_" "Red"
        }
        finally {
            if ($null -ne $session) {
                Remove-PSSession $session
            }
        }
    } elseif ($installType1) {
        Write-Color "Type 1 membership detected - Running office configuration with Type 1 Imprivata" "Yellow"
        Invoke-OfficeConfiguration -copyToDeviceId $copyToDeviceId -Credentials $script:storedCredentials

        # Create PSSession for Type 1 configuration
        Write-Color "Creating PSSession for Type 1 configuration..." "Cyan"
        $session = $null
        try {
            $session = New-PSSession -ComputerName $copyToDeviceId -Credential $script:storedCredentials -ErrorAction Stop
            Invoke-Type1Configuration -copyToDeviceId $copyToDeviceId -copyFromDeviceId $copyFromDeviceId -Credentials $script:storedCredentials -session $session
        }
        catch {
            Write-Color "Error creating PSSession for Type 1 configuration: $_" "Red"
        }
        finally {
            if ($null -ne $session) {
                Remove-PSSession $session
            }
        }
    } else {
        Write-Color "No Type 1 or Type 2 membership detected - Running office configuration WITHOUT Imprivata" "Yellow"
        Write-Color "Imprivata will NOT be installed on this computer" "Magenta"
        Invoke-OfficeConfiguration -copyToDeviceId $copyToDeviceId -Credentials $script:storedCredentials
    }

    # Dell Command Update - Run before renaming
    Show-Banner "*** DELL COMMAND UPDATE ***" "Green"
    Show-Progress "Installing and configuring Dell Command Update" 4 6 "Yellow"

    try {
        Invoke-Command -ComputerName $copyToDeviceId -Credential $script:storedCredentials -ScriptBlock {
            param($UseLocalInstallers)

            Write-Host "*** Installing Dell Command Update"
            $DellCommandUpdatePath = "C:\Program Files (x86)\Dell\CommandUpdate\dcu-cli.exe"
            $DellCommandUpdatePath2 = "C:\Program Files\Dell\CommandUpdate\dcu-cli.exe"
            $BiosPassword = "B1TuSer"
            $targetDirectory = "c:\files\dcu"

            # Create target directory
            if (-not (Test-Path -Path $targetDirectory)) {
                New-Item -Path $targetDirectory -ItemType Directory -Force
            }

            # Copy installer file if using local installers
            if ($UseLocalInstallers) {
                if (Test-Path "D:\Commandupdate.EXE") {
                    Copy-Item -Path "D:\Commandupdate.EXE" -Destination "$targetDirectory\Commandupdate.EXE" -Force
                    Write-Host "Dell Command Update installer copied from D:\ drive" -ForegroundColor Green
                } elseif (Test-Path "D:\dcu\Commandupdate.EXE") {
                    Copy-Item -Path "D:\dcu\Commandupdate.EXE" -Destination "$targetDirectory\Commandupdate.EXE" -Force
                    Write-Host "Dell Command Update installer copied from D:\dcu\ drive" -ForegroundColor Green
                } else {
                    Write-Host "Warning: Dell Command Update installer not found at D:\Commandupdate.EXE or D:\dcu\Commandupdate.EXE" -ForegroundColor Yellow
                }
            }

            # Install Dell Command Update
            $InstallerFile = Join-Path $targetDirectory "Commandupdate.EXE"
            if (Test-Path $InstallerFile) {
                Write-Host "Installing Dell Command Update..."
                Start-Process -FilePath $InstallerFile -ArgumentList "/S" -Wait -NoNewWindow -ErrorAction Stop
                Write-Host "Dell Command Update installed successfully" -ForegroundColor Green
            } else {
                Write-Host "Warning: Dell Command Update installer not found at $InstallerFile" -ForegroundColor Yellow
            }

            # Configure Dell Command Update
            Start-Sleep -Seconds 5  # Wait for installation to complete
            if (Test-Path $DellCommandUpdatePath) {
                Write-Host "Configuring Dell Command Update (x86 path)..."
                Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -biosPassword=$BiosPassword" -NoNewWindow -Wait
                Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -autoSuspendBitLocker=enable" -NoNewWindow -Wait
                Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -scheduleManual" -NoNewWindow -Wait
                Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -updatesNotification=Disable" -NoNewWindow -Wait
                Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -scheduleAction=DownloadInstallAndNotify" -NoNewWindow -Wait
                Write-Host "Running initial scan and update..."
                Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/scan" -NoNewWindow -Wait
                Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/applyupdates -reboot=disable" -NoNewWindow -Wait
                Write-Host "Dell Command Update configured successfully" -ForegroundColor Green
            }
            elseif (Test-Path $DellCommandUpdatePath2) {
                Write-Host "Configuring Dell Command Update (x64 path)..."
                Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -biosPassword=$BiosPassword" -NoNewWindow -Wait
                Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -autoSuspendBitLocker=enable" -NoNewWindow -Wait
                Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -scheduleManual" -NoNewWindow -Wait
                Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -updatesNotification=Disable" -NoNewWindow -Wait
                Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -scheduleAction=DownloadInstallAndNotify" -NoNewWindow -Wait
                Write-Host "Running initial scan and update..."
                Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/scan" -NoNewWindow -Wait
                Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/applyupdates -reboot=disable" -NoNewWindow -Wait
                Write-Host "Dell Command Update configured successfully" -ForegroundColor Green
            } else {
                Write-Host "Warning: Dell Command Update CLI not found after installation" -ForegroundColor Yellow
            }

            # Clean up installer files
            Remove-Item -Path $targetDirectory -Recurse -Force -ErrorAction SilentlyContinue

        } -ArgumentList $script:useLocalInstallers

        Write-Color "Dell Command Update installation and configuration completed" "Green"
    }
    catch {
        Write-Color "Warning: Dell Command Update installation failed - $_" "Yellow"
    }

    # Computer Naming with banner
    Show-Banner "*** COMPUTER NAMING ***" "Green"
    Show-Progress "Configuring new computer name" 5 6 "Yellow"
    $runRename = Read-Host "Do you want to run the rename logic? (Y/N)"
    if ($runRename -eq 'Y') {
        do {
            $deviceType = (Read-Host "Is the target device a Desktop or Laptop? (D/L)").ToUpper()
        } until ($deviceType -in @('D','L'))
        $suffix = $deviceType

        # Generate base name from source PC (all but last 3 chars)
        $sourcePC = $copyFromDeviceId.Trim().ToUpper()
        $targetPC = $copyToDeviceId.Trim().ToUpper()
        $baseName = $sourcePC.Substring(0, $sourcePC.Length - 3)

        # Check naming convention
        if ($targetPC -notmatch "^$($baseName)\d{2}$suffix$") {
            Write-Color "`n[!] Target name doesn't match source naming convention" "Yellow"

            # AD query for existing names
            $existingNames = Get-ADComputer -Filter "Name -like '$baseName*'" |
                Select-Object -ExpandProperty Name |
                Where-Object { $_ -imatch "^$baseName\d{2}$suffix$" } |
                ForEach-Object { $_.ToUpper() }

            # Generate available numbers
            $usedNumbers = $existingNames | ForEach-Object {
                if ($_ -imatch "$baseName(\d{2})$suffix$") { $matches[1] }
            }
            $allPossible = 00..99 | ForEach-Object { "{0:D2}" -f $_ }
            $availableNumbers = $allPossible | Where-Object { $_ -notin $usedNumbers } | Select-Object -First 20

            # Display available names
            Write-Color "`nAvailable PC numbers for $baseName ($suffix)" "Cyan"
            $i = 0
            $availableNumbers | ForEach-Object {
                Write-Color "  [$i] ${baseName}$_$suffix" "Gray"
                $i++
            }

            # Get user selection
            do {
                $selected = Read-Host "`nEnter number choice (0-19) or manual number (00-99)"
                if ($selected -match '^\d{2}$') {
                    $number = $selected
                    break
                } elseif ($selected -match '^\d+$' -and [int]$selected -lt 20) {
                    $number = $availableNumbers[[int]$selected]
                    break
                }
                Write-Color "Invalid selection. Please try again." "Red"
            } while ($true)

            $newName = "${baseName}${number}${suffix}"

            # Final confirmation and rename
            Write-Color "`nProposed new name: $newName" "Cyan"
            $confirm = Read-Host "Confirm rename? (Y/N)"
            if ($confirm -eq 'Y') {
                try {
                    Rename-Computer -ComputerName $targetPC -NewName $newName -DomainCredential $script:storedCredentials -Force -Restart -ErrorAction Stop
                    $copyToDeviceId = $newName
                    Write-Color "Successfully renamed to $newName" "Green"
                    Start-Sleep -Seconds 10
                } catch {
                    Write-Color "Rename failed: $_" "Red"
                }
            }
        }
    }

    # Completion with banner
    Show-Banner "!!! MIGRATION COMPLETE !!!" "Green"
    Show-Progress "Finalizing configuration" 6 6 "Yellow"
    Show-CompletionAnimation

    # Keep restart prompt
    $restart = Read-Host "Press Enter to restart the script, or type 'exit' to quit"
    if ($restart -ne 'exit') {
        Remove-Variable * -ErrorAction SilentlyContinue
        & $PSCommandPath
    }
}

# Initial script execution
Start-MainScript