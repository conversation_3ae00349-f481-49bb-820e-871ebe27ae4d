# Example of improved PSRemoting usage with persistent sessions
# This demonstrates better practices for maintaining remote connections

param(
    [string]$TargetComputer,
    [switch]$SkipPSRemotingSetup,
    [switch]$QuietMode
)

# Import the enhanced session manager (if using the modular approach)
# Import-Module .\Enhanced-PSSessionManager.ps1

# Global variables for session management
$script:storedCredentials = $null
$script:targetSession = $null
$script:quietMode = $QuietMode

# Initialize credentials (reuse from your existing script)
function Initialize-Credentials {
    # ... (your existing credential function)
    $script:storedCredentials = Get-Credential -Message "Enter domain credentials"
    return ($null -ne $script:storedCredentials)
}

# Enhanced connection function with better persistence
function Connect-ToTargetComputer {
    param(
        [string]$TargetComputer,
        [switch]$SkipSetup,
        [switch]$Quiet
    )
    
    Write-Host "=== Enhanced PSRemoting Connection ===" -ForegroundColor Cyan
    
    # Step 1: Test if PSRemoting is already configured
    if (Test-PSRemotingConfiguration -TargetComputer $TargetComputer -Quiet:$Quiet) {
        Write-Host "✓ PSRemoting is already configured - skipping setup" -ForegroundColor Green
        $SkipSetup = $true
    }
    
    # Step 2: Initialize connection with appropriate settings
    $connectionResult = Initialize-PSRemoting -TargetComputer $TargetComputer -Quiet:$Quiet -SkipPSRemotingSetup:$SkipSetup
    
    if ($connectionResult) {
        Write-Host "✓ Persistent session established successfully" -ForegroundColor Green
        return $true
    } else {
        Write-Host "✗ Failed to establish persistent session" -ForegroundColor Red
        return $false
    }
}

# Example usage of persistent commands
function Example-PersistentOperations {
    param([string]$TargetComputer)
    
    Write-Host "`n=== Running Multiple Operations with Persistent Session ===" -ForegroundColor Cyan
    
    # Operation 1: Get system info
    Write-Host "Getting system information..." -ForegroundColor Yellow
    $systemInfo = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -Quiet:$script:quietMode -ScriptBlock {
        [PSCustomObject]@{
            ComputerName = $env:COMPUTERNAME
            OSVersion = (Get-WmiObject Win32_OperatingSystem).Caption
            TotalRAM = [math]::Round((Get-WmiObject Win32_ComputerSystem).TotalPhysicalMemory / 1GB, 2)
            FreeSpace = [math]::Round((Get-WmiObject Win32_LogicalDisk -Filter "DeviceID='C:'").FreeSpace / 1GB, 2)
        }
    }
    Write-Host "System: $($systemInfo.ComputerName) - $($systemInfo.OSVersion)" -ForegroundColor Green
    
    # Operation 2: Check services (reuses same session)
    Write-Host "Checking critical services..." -ForegroundColor Yellow
    $services = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -Quiet:$script:quietMode -ScriptBlock {
        Get-Service -Name "WinRM", "Spooler", "BITS" | Select-Object Name, Status
    }
    foreach ($service in $services) {
        $color = if ($service.Status -eq 'Running') { 'Green' } else { 'Red' }
        Write-Host "  $($service.Name): $($service.Status)" -ForegroundColor $color
    }
    
    # Operation 3: Install something (reuses same session)
    Write-Host "Testing file operations..." -ForegroundColor Yellow
    $fileTest = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -Quiet:$script:quietMode -ScriptBlock {
        $testPath = "C:\temp\psremoting-test.txt"
        try {
            if (-not (Test-Path "C:\temp")) {
                New-Item -Path "C:\temp" -ItemType Directory -Force | Out-Null
            }
            "PSRemoting test - $(Get-Date)" | Out-File -FilePath $testPath -Force
            if (Test-Path $testPath) {
                Remove-Item $testPath -Force
                return "File operations successful"
            }
        }
        catch {
            return "File operations failed: $_"
        }
    }
    Write-Host "File test result: $fileTest" -ForegroundColor Green
    
    Write-Host "✓ All operations completed using the same persistent session" -ForegroundColor Green
}

# Demonstrate session reuse across multiple script runs
function Test-SessionPersistence {
    param([string]$TargetComputer)
    
    Write-Host "`n=== Testing Session Persistence ===" -ForegroundColor Cyan
    
    # Check if we already have a session
    if (Test-PSSession -Quiet) {
        Write-Host "✓ Existing session is still active - reusing it" -ForegroundColor Green
        
        # Test the existing session
        $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -Quiet:$script:quietMode -ScriptBlock {
            "Session reuse test - $(Get-Date) - Uptime: $((Get-Date) - (Get-WmiObject Win32_OperatingSystem).ConvertToDateTime((Get-WmiObject Win32_OperatingSystem).LastBootUpTime))"
        }
        Write-Host "Session test: $result" -ForegroundColor Green
    } else {
        Write-Host "No active session found - this would create a new one" -ForegroundColor Yellow
    }
}

# Main execution
function Main {
    Write-Host "PSRemoting Best Practices Demo" -ForegroundColor Cyan
    Write-Host "=============================" -ForegroundColor Cyan
    
    # Get target computer if not provided
    if (-not $TargetComputer) {
        $TargetComputer = Read-Host "Enter target computer name"
    }
    
    # Initialize credentials
    if (-not (Initialize-Credentials)) {
        Write-Host "Failed to get credentials" -ForegroundColor Red
        return
    }
    
    # Connect with enhanced persistence
    if (-not (Connect-ToTargetComputer -TargetComputer $TargetComputer -SkipSetup:$SkipPSRemotingSetup -Quiet:$script:quietMode)) {
        Write-Host "Failed to connect to $TargetComputer" -ForegroundColor Red
        return
    }
    
    # Run example operations
    Example-PersistentOperations -TargetComputer $TargetComputer
    
    # Test session persistence
    Test-SessionPersistence -TargetComputer $TargetComputer
    
    # Show session info
    if ($script:targetSession) {
        Write-Host "`nSession Information:" -ForegroundColor Cyan
        Write-Host "  Session ID: $($script:targetSession.Id)" -ForegroundColor Gray
        Write-Host "  State: $($script:targetSession.State)" -ForegroundColor Gray
        Write-Host "  Computer: $($script:targetSession.ComputerName)" -ForegroundColor Gray
        Write-Host "  Idle Timeout: $($script:targetSession.IdleTimeout)" -ForegroundColor Gray
    }
    
    # Ask if user wants to keep session open
    $keepOpen = Read-Host "`nKeep session open for future use? (Y/N)"
    if ($keepOpen -notmatch '^(Y|y)$') {
        if ($script:targetSession) {
            Remove-PSSession $script:targetSession
            Write-Host "✓ Session closed" -ForegroundColor Green
        }
    } else {
        Write-Host "✓ Session kept open - you can run this script again to reuse it" -ForegroundColor Green
        Write-Host "  Note: Session will remain active until computer restart or manual cleanup" -ForegroundColor Gray
    }
}

# Run the demo
Main
