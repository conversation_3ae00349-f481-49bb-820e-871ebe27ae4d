# Simple USB Installer Copy Script
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

function Write-Color {
    param([string]$Text, [string]$Color = "White")
    Write-Host $Text -ForegroundColor $Color
}

function Get-AvailableDrives {
    Write-Color "Detecting available drives..." "Cyan"

    $drives = @()
    $driveCount = 0

    $allDrives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -ne "C:" }

    Write-Color "REMOVABLE DRIVES (USB/External):" "Yellow"
    $removableDrives = $allDrives | Where-Object { $_.DriveType -eq 2 }

    foreach ($drive in $removableDrives) {
        $driveCount++
        $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
        $volumeLabel = if ($drive.VolumeName) { "`"$($drive.VolumeName)`"" } else { "(No Label)" }

        Write-Color "[$driveCount] $($drive.DeviceID) $volumeLabel (USB/Removable) - $freeSpaceGB GB free / $totalSpaceGB GB total" "White"

        $drives += @{
            Number = $driveCount
            DeviceID = $drive.DeviceID
            VolumeName = $drive.VolumeName
            FreeSpace = $drive.FreeSpace
            Size = $drive.Size
            Type = "USB/Removable"
        }
    }

    Write-Host ""
    Write-Color "FIXED DRIVES (Local Hard Drives):" "Yellow"
    $fixedDrives = $allDrives | Where-Object { $_.DriveType -eq 3 }

    foreach ($drive in $fixedDrives) {
        $driveCount++
        $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
        $volumeLabel = if ($drive.VolumeName) { "`"$($drive.VolumeName)`"" } else { "(No Label)" }

        Write-Color "[$driveCount] $($drive.DeviceID) $volumeLabel (Fixed Drive) - $freeSpaceGB GB free / $totalSpaceGB GB total" "White"

        $drives += @{
            Number = $driveCount
            DeviceID = $drive.DeviceID
            VolumeName = $drive.VolumeName
            FreeSpace = $drive.FreeSpace
            Size = $drive.Size
            Type = "Fixed Drive"
        }
    }

    if ($driveCount -eq 0) {
        Write-Color "ERROR: No suitable drives found!" "Red"
        Read-Host "Press Enter to exit"
        exit 1
    }

    Write-Host ""
    Write-Color "[0] Exit" "Gray"
    Write-Host ""

    return $drives
}

function Select-Drive {
    param([array]$Drives)

    do {
        $choice = Read-Host "Please select a drive (1-$($Drives.Count) or 0 to exit)"

        if ($choice -eq "0") {
            Write-Color "Operation cancelled by user." "Yellow"
            exit 0
        }

        $selectedDrive = $Drives | Where-Object { $_.Number -eq [int]$choice }

        if (-not $selectedDrive) {
            Write-Color "Invalid selection. Please try again." "Red"
        }
    } while (-not $selectedDrive)

    return $selectedDrive
}

function Copy-SingleFile {
    param([string]$Source, [string]$Destination, [string]$Name)

    Write-Color "Copying $Name..." "Yellow"

    # Simple copy like the working batch file
    Copy-Item -Path $Source -Destination $Destination -Force -ErrorAction SilentlyContinue

    if (Test-Path $Destination) {
        Write-Color "$Name copied successfully" "Green"
    } else {
        Write-Color "ERROR: Failed to copy $Name" "Red"
    }
}

function Copy-SingleFolder {
    param([string]$Source, [string]$Destination, [string]$Name)

    Write-Color "Copying $Name..." "Yellow"

    # Use cmd.exe to run robocopy exactly like the batch file
    $cmd = "robocopy `"$Source`" `"$Destination`" /E"
    cmd.exe /c $cmd | Out-Null
    $exitCode = $LASTEXITCODE

    if ($exitCode -le 7) {
        Write-Color "$Name copied successfully" "Green"
    } elseif ($exitCode -eq 16) {
        Write-Color "ERROR: $Name copy failed - Serious error, no files copied. Trying alternative method..." "Red"

        # Try with xcopy as fallback
        $xcopyCmd = "xcopy `"$Source`" `"$Destination`" /E /I /H /Y"
        cmd.exe /c $xcopyCmd | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Color "$Name copied successfully (using xcopy)" "Green"
        } else {
            Write-Color "ERROR: $Name copy failed completely" "Red"
        }
    } else {
        Write-Color "WARNING: $Name copy completed with warnings (Exit code: $exitCode)" "Yellow"
    }
}

# Main execution
Clear-Host
Write-Color "********************************************************************************" "Magenta"
Write-Color "*                    INSTALLER FILES COPY UTILITY                             *" "Magenta"
Write-Color "********************************************************************************" "Magenta"
Write-Host ""

$availableDrives = Get-AvailableDrives
$selectedDrive = Select-Drive -Drives $availableDrives

Write-Host ""
Write-Color "Selected drive: $($selectedDrive.DeviceID)" "Green"

$confirm = Read-Host "Do you want to proceed with copying installer files to $($selectedDrive.DeviceID)? (Y/N)"
if ($confirm -notmatch '^(Y|y)$') {
    Write-Color "Operation cancelled by user." "Yellow"
    Read-Host "Press Enter to exit"
    exit 0
}

Write-Host ""
Write-Color "Starting copy operations..." "Cyan"

# Create necessary directories first
Write-Color "Creating directory structure..." "Cyan"
New-Item -Path "$($selectedDrive.DeviceID)\dcu" -ItemType Directory -Force -ErrorAction SilentlyContinue | Out-Null

# Copy all installer files (exactly like your working batch file)
Write-Color "[1/15] Copying folders..." "Cyan"
Copy-SingleFolder "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\Splashtop_Push" "$($selectedDrive.DeviceID)\Splashtop" "Splashtop"
Copy-SingleFolder "\\bay-msfsnas01\data\FS\PUBLIC\installdotnet3_5\Win11\sxs" "$($selectedDrive.DeviceID)\sxs" ".NET 3.5 SXS"
Copy-SingleFolder "\\bay-msfsnas01\data\FS\SFILES\__install__\source\Lexmark\Lexmark_Universal_v2_UD1_PostScript_3_Emulation" "$($selectedDrive.DeviceID)\Lexmark_Universal_v2_UD1_PostScript_3_Emulation" "Lexmark Driver"
Copy-SingleFolder "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\o365" "$($selectedDrive.DeviceID)\o365" "Office 365"
Copy-SingleFolder "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\4.9_LTSR2" "$($selectedDrive.DeviceID)\4.9_LTSR2" "Citrix 4.9 LTSR2"
Copy-SingleFolder "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\CustomCitrix\4.9_LTSR2\Nuance" "$($selectedDrive.DeviceID)\Nuance121" "Nuance"
Copy-SingleFolder "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\FSTools" "$($selectedDrive.DeviceID)\FSTools" "FSTools"
Copy-SingleFolder "\\storagehd\Desktopshare\Scripts\Andrew\Needed\LexmarkGDI" "$($selectedDrive.DeviceID)\LexmarkGDI" "Lexmark GDI"
Copy-SingleFile "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Imprivata_push\ImprivataAgent_x64.msi" "$($selectedDrive.DeviceID)\ImprivataAgent_x64.msi" "Imprivata Agent"

Write-Host ""
Write-Color "[2/15] Copying individual files..." "Cyan"
Copy-SingleFile "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\WSP\7\jre1.7.0_45.msi" "$($selectedDrive.DeviceID)\jre1.7.0_45.msi" "Java RE 7"
Copy-SingleFile "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\Google\GoogleChromeStandaloneEnterprise64.msi" "$($selectedDrive.DeviceID)\GoogleChromeStandaloneEnterprise64.msi" "Google Chrome"
Copy-SingleFile "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\4.9_LTSR2\CitrixReceiver.cmd" "$($selectedDrive.DeviceID)\4.9_LTSR2\CitrixReceiver.cmd" "Citrix Receiver"
Copy-SingleFile "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Win_Photo_Viewer.reg" "$($selectedDrive.DeviceID)\Win_Photo_Viewer.reg" "Photo Viewer Registry"
Copy-SingleFile "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Volume.ps1" "$($selectedDrive.DeviceID)\Volume.ps1" "Volume Script"
Copy-SingleFile "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\BitLockerAD.ps1" "$($selectedDrive.DeviceID)\BitLockerAD.ps1" "BitLocker Script"
Copy-SingleFile "\\bay-msfsnas01\data\FS\Source\Commandupdate.EXE" "$($selectedDrive.DeviceID)\dcu\Commandupdate.EXE" "Dell Command Update"

Write-Host ""
Write-Color "********************************************************************************" "Green"
Write-Color "*                           COPY OPERATION COMPLETE                           *" "Green"
Write-Color "********************************************************************************" "Green"
Write-Host ""
Write-Color "All installer files have been copied to $($selectedDrive.DeviceID)." "Green"
Write-Host ""
Write-Color "Files copied:" "Yellow"
Write-Color "  Folders: Splashtop, .NET SXS, Lexmark Driver, Office 365, Citrix, Nuance, FSTools, Lexmark GDI, Imprivata" "White"
Write-Color "  Files: Java, Chrome, Citrix Receiver, Photo Viewer, Volume Script, BitLocker Script, Dell Command Update" "White"
Write-Host ""
Write-Color "NEXT STEPS:" "Yellow"
Write-Color "1. If using USB drive, copy files from $($selectedDrive.DeviceID) to D:\ on target computer" "White"
Write-Color "2. Run the migration script and select 'Yes' for local installer files" "White"
Write-Color "3. Migration script will automatically use D:\ drive" "White"
Write-Host ""
Read-Host "Press Enter to exit"
