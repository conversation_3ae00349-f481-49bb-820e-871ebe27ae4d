# Get and store credentials at script start
$script:storedCredentials = $null
$script:targetSession = $null

function Initialize-Credentials {
    $credentialPath = "$env:USERPROFILE\.migration_creds.xml"

    # Try to load saved credentials first
    if (Test-Path $credentialPath) {
        try {
            Write-Color "Found saved credentials. Loading..." "Green"
            $script:storedCredentials = Import-Clixml -Path $credentialPath

            # Test the credentials by trying to access a domain resource
            Write-Color "Testing saved credentials..." "Yellow"
            try {
                # Try a simple domain operation to validate credentials
                Invoke-Command -ComputerName "localhost" -Credential $script:storedCredentials -ScriptBlock { $env:COMPUTERNAME } -ErrorAction Stop | Out-Null
                Write-Color "Saved credentials are valid!" "Green"

                # Ask if user wants to use saved credentials
                $useSaved = Read-Host "Use saved credentials? (Y/N)"
                if ($useSaved -match '^(Y|y)$') {
                    Write-Color "Using saved credentials" "Green"
                    return $true
                } else {
                    Write-Color "Getting new credentials..." "Yellow"
                }
            }
            catch {
                Write-Color "Saved credentials are invalid or expired. Getting new credentials..." "Yellow"
                Remove-Item -Path $credentialPath -Force -ErrorAction SilentlyContinue
            }
        }
        catch {
            Write-Color "Error loading saved credentials. Getting new credentials..." "Yellow"
            Remove-Item -Path $credentialPath -Force -ErrorAction SilentlyContinue
        }
    }

    # Get new credentials
    Write-Color "Initializing credentials for script execution..." "Cyan"
    try {
        $script:storedCredentials = Get-Credential -Message "Enter domain credentials for script operations" -UserName "mclaren\"
        if ($script:storedCredentials) {
            Write-Color "Credentials stored successfully" "Green"

            # Ask if user wants to save credentials
            $saveChoice = Read-Host "Save credentials for future use? (Y/N)"
            if ($saveChoice -match '^(Y|y)$') {
                try {
                    $script:storedCredentials | Export-Clixml -Path $credentialPath -Force
                    Write-Color "Credentials saved securely to: $credentialPath" "Green"
                    Write-Color "Note: Credentials are encrypted and can only be used by your user account on this computer." "Gray"
                }
                catch {
                    Write-Color "Warning: Could not save credentials - $_" "Yellow"
                }
            }
            return $true
        }
        Write-Color "No credentials provided" "Red"
        return $false
    }
    catch {
        Write-Color "Error storing credentials: $_" "Red"
        return $false
    }
}

function Clear-SavedCredentials {
    $credentialPath = "$env:USERPROFILE\.migration_creds.xml"
    if (Test-Path $credentialPath) {
        Remove-Item -Path $credentialPath -Force
        Write-Color "Saved credentials cleared successfully" "Green"
    } else {
        Write-Color "No saved credentials found to clear" "Yellow"
    }
}

function Write-Color {
    param([string]$Message, [string]$Color = "Gray", [switch]$NoNewline)
    if ($NoNewline) { Write-Host $Message -ForegroundColor $Color -NoNewline }
    else { Write-Host $Message -ForegroundColor $Color }
}

function Show-Banner {
    param([string]$Title, [string]$Color = "Cyan")
    $width = 80
    $padding = [math]::Max(0, ($width - $Title.Length - 4) / 2)
    $leftPad = [math]::Floor($padding)
    $rightPad = [math]::Ceiling($padding)
    Write-Host ""
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ("*" + " " * ($width - 2) + "*") -ForegroundColor $Color
    Write-Host ("*" + " " * $leftPad + $Title + " " * $rightPad + "*") -ForegroundColor $Color
    Write-Host ("*" + " " * ($width - 2) + "*") -ForegroundColor $Color
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ""
}

function Show-Progress {
    param([string]$Message, [int]$Step, [int]$Total, [string]$Color = "Cyan")
    $percentage = [math]::Round(($Step / $Total) * 100)
    Write-Color "[$Step/$Total] ($percentage%) $Message" $Color
}

function Show-Spinner {
    param([string]$Message, [int]$Seconds = 3)
    $spinner = @('|', '/', '-', '\')
    $startTime = Get-Date
    $endTime = $startTime.AddSeconds($Seconds)
    $i = 0
    Write-Host "  " -NoNewline
    while ((Get-Date) -lt $endTime) {
        Write-Host "`r  $($spinner[$i % $spinner.Length]) $Message" -NoNewline -ForegroundColor Cyan
        Start-Sleep -Milliseconds 100
        $i++
    }
    Write-Host "`r  + $Message" -ForegroundColor Green
}

function Show-CompletionAnimation {
    $colors = @("Red", "Yellow", "Green", "Cyan", "Blue", "Magenta")
    $message = "MIGRATION COMPLETE!"
    1..3 | ForEach-Object { Write-Host "" }
    1..3 | ForEach-Object {
        $dots = "." * $_
        Write-Host "  Finalizing$dots"
        Start-Sleep -Milliseconds 300
    }
    foreach ($color in $colors) {
        Write-Host $message -ForegroundColor $color
        Start-Sleep -Milliseconds 100
    }
    Write-Host ""
}

function Initialize-PSRemoting {
    param([string]$TargetComputer)

    Write-Color "Initializing PSRemoting connection to $TargetComputer..." "Cyan"

    # Check if WinRM service is running
    try {
        $winrm = Get-WmiObject -Class Win32_Service -ComputerName $TargetComputer | Where-Object { $_.Name -eq 'WinRM' }
        if ($winrm.Status -ne "Running") {
            Write-Color "Enabling PSRemoting on $TargetComputer..." "Yellow"
            $SessionArgs = @{
                ComputerName = $TargetComputer
                SessionOption = New-CimSessionOption -Protocol Dcom
            }
            $MethodArgsEnablePS = @{
                ClassName = 'Win32_Process'
                MethodName = 'Create'
                CimSession = New-CimSession @SessionArgs
                Arguments = @{ CommandLine = 'powershell -Command "Enable-PSRemoting -Force"' }
            }
            Invoke-CimMethod @MethodArgsEnablePS
            Start-Sleep -Seconds 10
        }
    }
    catch {
        Write-Color "Warning: Could not check WinRM service status - $_" "Yellow"
    }

    # Create persistent PSSession with enhanced settings
    Write-Color "Creating persistent PSSession..." "Yellow"
    $maxAttempts = 5
    $attempt = 0

    while ($attempt -lt $maxAttempts -and $null -eq $script:targetSession) {
        $attempt++
        try {
            $sessionOption = New-PSSessionOption -IdleTimeout 3600000 -OpenTimeout 60000 -OperationTimeout 300000
            $script:targetSession = New-PSSession -ComputerName $TargetComputer -Credential $script:storedCredentials -SessionOption $sessionOption -ErrorAction Stop
            Write-Color "PSSession established successfully!" "Green"
            break
        }
        catch {
            Write-Color "PSSession creation attempt $attempt failed: $_" "Yellow"
            if ($attempt -lt $maxAttempts) {
                Write-Color "Retrying in 10 seconds..." "Yellow"
                Start-Sleep -Seconds 10
            }
        }
    }

    if ($null -eq $script:targetSession) {
        Write-Color "Failed to create PSSession after $maxAttempts attempts" "Red"
        return $false
    }

    return $true
}

function Test-PSSession {
    if ($null -eq $script:targetSession -or $script:targetSession.State -ne 'Opened') {
        Write-Color "PSSession is not available or has been closed" "Red"
        return $false
    }

    try {
        Invoke-Command -Session $script:targetSession -ScriptBlock { $env:COMPUTERNAME } -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        Write-Color "PSSession test failed: $_" "Yellow"
        return $false
    }
}

function Repair-PSSession {
    param([string]$TargetComputer)

    Write-Color "Attempting to repair PSSession..." "Yellow"

    # Clean up existing session
    if ($null -ne $script:targetSession) {
        try {
            Remove-PSSession $script:targetSession -ErrorAction SilentlyContinue
        }
        catch { }
        $script:targetSession = $null
    }

    # Re-establish connection
    return Initialize-PSRemoting -TargetComputer $TargetComputer
}

function Invoke-RemoteCommand {
    param(
        [scriptblock]$ScriptBlock,
        [object[]]$ArgumentList = @(),
        [string]$TargetComputer,
        [int]$MaxRetries = 2,
        [switch]$UseFallback
    )

    $attempt = 0
    while ($attempt -le $MaxRetries) {
        $attempt++

        # Test session before use
        if (-not (Test-PSSession)) {
            Write-Color "PSSession not available, attempting repair..." "Yellow"
            if (-not [string]::IsNullOrEmpty($TargetComputer) -and -not (Repair-PSSession -TargetComputer $TargetComputer)) {
                Write-Color "Failed to repair PSSession, trying fallback method..." "Yellow"
                break
            } elseif ([string]::IsNullOrEmpty($TargetComputer)) {
                Write-Color "TargetComputer parameter is empty, cannot repair PSSession" "Red"
                break
            }
        }

        try {
            if ($ArgumentList.Count -gt 0) {
                return Invoke-Command -Session $script:targetSession -ScriptBlock $ScriptBlock -ArgumentList $ArgumentList -ErrorAction Stop
            } else {
                return Invoke-Command -Session $script:targetSession -ScriptBlock $ScriptBlock -ErrorAction Stop
            }
        }
        catch {
            Write-Color "Remote command failed (attempt $attempt): $_" "Yellow"
            if ($attempt -le $MaxRetries) {
                Write-Color "Retrying..." "Yellow"
                Start-Sleep -Seconds 5
                # Force session repair on retry
                $script:targetSession = $null
            }
        }
    }

    # Fallback to direct Invoke-Command if PSSession fails
    if ($UseFallback) {
        Write-Color "Using fallback method (direct Invoke-Command)..." "Cyan"
        try {
            if ($ArgumentList.Count -gt 0) {
                return Invoke-Command -ComputerName $TargetComputer -Credential $script:storedCredentials -ScriptBlock $ScriptBlock -ArgumentList $ArgumentList -ErrorAction Stop
            } else {
                return Invoke-Command -ComputerName $TargetComputer -Credential $script:storedCredentials -ScriptBlock $ScriptBlock -ErrorAction Stop
            }
        }
        catch {
            Write-Color "Fallback method also failed: $_" "Red"
            return "Error: Both PSSession and fallback methods failed"
        }
    }

    Write-Color "Remote command failed after $($MaxRetries + 1) attempts" "Red"
    return "Error: Remote command failed after all attempts"
}

function Get-InstallerPaths {
    param([string]$TargetComputer = "")

    # Define all installer paths based on local vs network mode
    if ($script:useLocalInstallers) {
        # Use local D:\ drive paths (these will be used in remote commands on target computer)
        return @{
            Splashtop = "D:\Splashtop_Push"
            DotNetSxs = "D:\sxs"
            LexmarkDriver = "D:\Lexmark_Universal_v2_UD1_PostScript_3_Emulation"
            Office365 = "D:\o365"
            Citrix = "D:\4.9_LTSR2"
            Nuance = "D:\Nuance121"
            Java = "D:\jre1.7.0_45.msi"
            Chrome = "D:\GoogleChromeStandaloneEnterprise64.msi"
            CitrixReceiver = "D:\4.9_LTSR2\CitrixReceiver.cmd"
            PhotoViewer = "D:\Win_Photo_Viewer.reg"
            VolumeScript = "D:\Volume.ps1"
            BitLockerScript = "D:\BitLockerAD.ps1"
            DellCommandUpdate = "D:\Commandupdate.EXE"
            FSTools = "D:\FSTools"
            LexmarkGDI = "D:\LexmarkGDI"
            ImprivataAgent = "D:\ImprivataAgent_x64.msi"
        }
    } else {
        return @{
            Splashtop = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\Splashtop_Push"
            DotNetSxs = "\\bay-msfsnas01\data\FS\PUBLIC\installdotnet3_5\Win11\sxs"
            LexmarkDriver = "\\bay-msfsnas01\data\FS\SFILES\__install__\source\Lexmark\Lexmark_Universal_v2_UD1_PostScript_3_Emulation"
            Office365 = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\o365"
            Citrix = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\4.9_LTSR2"
            Nuance = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\CustomCitrix\4.9_LTSR2\Nuance"
            Java = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\WSP\7\jre1.7.0_45.msi"
            Chrome = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\Google\GoogleChromeStandaloneEnterprise64.msi"
            CitrixReceiver = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\4.9_LTSR2\CitrixReceiver.cmd"
            PhotoViewer = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Win_Photo_Viewer.reg"
            VolumeScript = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Volume.ps1"
            BitLockerScript = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\BitLockerAD.ps1"
            DellCommandUpdate = "\\bay-msfsnas01\data\FS\Source\Commandupdate.EXE"
            FSTools = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\FSTools"
            LexmarkGDI = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\LexmarkGDI"
            ImprivataAgent = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Imprivata_push\ImprivataAgent_x64.msi"
        }
    }
}

function Copy-FilesToTarget {
    param([string]$TargetComputer)

    Write-Color "Copying installer files to target computer..." "Cyan"
    $installerPaths = Get-InstallerPaths -TargetComputer $TargetComputer

    $copyItems = @(
        @{ Name = "Splashtop"; Source = $installerPaths.Splashtop; Dest = "Splashtop"; IsFolder = $true },
        @{ Name = ".NET 3.5 SXS"; Source = $installerPaths.DotNetSxs; Dest = "sxs"; IsFolder = $true },
        @{ Name = "Lexmark Driver"; Source = $installerPaths.LexmarkDriver; Dest = "Lexmark_Universal_v2_UD1_PostScript_3_Emulation"; IsFolder = $true },

        @{ Name = "Citrix"; Source = $installerPaths.Citrix; Dest = "4.9_LTSR2"; IsFolder = $true },
        @{ Name = "Nuance"; Source = $installerPaths.Nuance; Dest = "Nuance121"; IsFolder = $true },
        @{ Name = "Java"; Source = $installerPaths.Java; Dest = "jre1.7.0_45.msi" },
        @{ Name = "Chrome"; Source = $installerPaths.Chrome; Dest = "GoogleChromeStandaloneEnterprise64.msi" },
        @{ Name = "Citrix Receiver"; Source = $installerPaths.CitrixReceiver; Dest = "4.9_LTSR2\CitrixReceiver.cmd" },
        @{ Name = "Photo Viewer"; Source = $installerPaths.PhotoViewer; Dest = "Win_Photo_Viewer.reg" },
        @{ Name = "Volume Script"; Source = $installerPaths.VolumeScript; Dest = "Volume.ps1" },
        @{ Name = "BitLocker Script"; Source = $installerPaths.BitLockerScript; Dest = "BitLockerAD.ps1" },
        @{ Name = "Dell Command Update"; Source = $installerPaths.DellCommandUpdate; Dest = "dcu\Commandupdate.EXE" }
    )

    foreach ($item in $copyItems) {
        Write-Color "Copying $($item.Name)" "Cyan"
        try {
            if ($script:useLocalInstallers) {
                # Use remote command to copy from local D:\ drive on target computer
                $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
                    param($SourcePath, $DestPath, $IsFolder)

                    if (Test-Path $SourcePath) {
                        # Create destination directory
                        $destFolder = if ($IsFolder) { $DestPath } else { Split-Path $DestPath -Parent }
                        if (-not (Test-Path $destFolder)) {
                            New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
                        }

                        if ($IsFolder) {
                            # Copy folder contents using robocopy
                            $result = Start-Process -FilePath "robocopy" -ArgumentList "`"$SourcePath`"", "`"$DestPath`"", "/E", "/R:3", "/W:10" -Wait -PassThru -NoNewWindow
                            if ($result.ExitCode -le 7) {
                                return "Success"
                            } else {
                                return "Warning: Exit code $($result.ExitCode)"
                            }
                        } else {
                            # Copy single file
                            Copy-Item -Path $SourcePath -Destination $DestPath -Force
                            return "Success"
                        }
                    } else {
                        return "Error: Source not found at $SourcePath"
                    }
                } -ArgumentList $item.Source, "C:\Files\$($item.Dest)", $item.IsFolder

                if ($result -like "Success*") {
                    Write-Color "✅ Success: $($item.Name)" "Green"
                } else {
                    Write-Color "❌ Failed: $($item.Name) - $result" "Red"
                }
            } else {
                # Network mode - use original Copy-Item method
                if ($item.IsFolder) {
                    # Create destination path with correct folder name
                    $destPath = "\\$TargetComputer\c$\Files\$($item.Dest)"
                    if (-not (Test-Path $destPath)) {
                        New-Item -Path $destPath -ItemType Directory -Force | Out-Null
                    }
                    # Copy contents to the correctly named folder
                    Copy-Item -Path "$($item.Source)\*" -Destination $destPath -Recurse -Force
                } else {
                    # Handle single file copy
                    $destPath = "\\$TargetComputer\c$\Files\$($item.Dest)"
                    $destFolder = Split-Path $destPath -Parent
                    if (-not (Test-Path $destFolder)) {
                        New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
                    }
                    Copy-Item -Path $item.Source -Destination $destPath -Force
                }
                Write-Color "✅ Success: $($item.Name)" "Green"
            }
        }
        catch {
            Write-Color "❌ Failed: $($item.Name) - $_" "Red"
        }
    }
}

function Install-ImprivataAgent {
    param([string]$TargetComputer, [int]$AgentType, [string]$SourceComputer)

    Write-Color "Installing Imprivata Agent Type $AgentType..." "Cyan"

    # For Type 2, get stored credentials from source computer
    $AutoUsername = $null
    $AutoPassword = $null

    if ($AgentType -eq 2 -and $SourceComputer) {
        Write-Color "Retrieving autologin credentials from source computer..." "Cyan"
        try {
            # Get stored credentials from source
            $sourceCreds = Invoke-Command -ComputerName $SourceComputer -Credential $script:storedCredentials -ScriptBlock {
                $regPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon"
                if (Test-Path $regPath) {
                    $props = Get-ItemProperty -Path $regPath
                    [PSCustomObject]@{
                        UserName = $props.DefaultUserName
                        Password = $props.DefaultPassword
                    }
                }
            }

            if ($sourceCreds.UserName -and $sourceCreds.Password) {
                Write-Color "  ✅ Found stored credentials on source computer" "Green"
                $AutoUsername = $sourceCreds.UserName
                $AutoPassword = ConvertTo-SecureString $sourceCreds.Password -AsPlainText -Force
            } else {
                Write-Color "  ⚠️ No stored credentials found" "Yellow"
                $AutoUsername = Read-Host "Enter Imprivata autologin username"
                $AutoPassword = Read-Host "Enter Imprivata autologin password" -AsSecureString
            }
        } catch {
            Write-Color "  ❌ Error retrieving credentials from source: $_" "Yellow"
            $AutoUsername = Read-Host "Enter Imprivata autologin username"
            $AutoPassword = Read-Host "Enter Imprivata autologin password" -AsSecureString
        }
    }

    # Copy Imprivata MSI file first
    if ($script:useLocalInstallers) {
        $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
            $sourcePath = "D:\ImprivataAgent_x64.msi"
            $destPath = "C:\Files\ImprivataAgent_x64.msi"

            if (Test-Path $sourcePath) {
                # Create destination directory
                $destFolder = Split-Path $destPath -Parent
                if (-not (Test-Path $destFolder)) {
                    New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
                }

                # Copy MSI file
                Copy-Item -Path $sourcePath -Destination $destPath -Force
                return "✅ Success: Imprivata Agent MSI copied from local D:\ drive"
            } else {
                return "❌ Error: Imprivata Agent MSI not found at $sourcePath"
            }
        }
        Write-Color $result "Gray"
    } else {
        # Network mode - copy MSI from network location
        try {
            $sourceMSI = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Imprivata_push\ImprivataAgent_x64.msi"
            $destMSI = "\\$TargetComputer\C$\Files\ImprivataAgent_x64.msi"

            # Create destination directory
            $destFolder = Split-Path $destMSI -Parent
            if (-not (Test-Path $destFolder)) {
                New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
            }

            Copy-Item -Path $sourceMSI -Destination $destMSI -Force
            Write-Color "✅ Success: Imprivata Agent MSI copied from network" "Gray"
        }
        catch {
            Write-Color "❌ Error: Failed to copy Imprivata MSI - $_" "Red"
            return
        }
    }

    # Install Imprivata with verification
    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($AgentType)

        Write-Host "*** Installing Imprivata Agent Type $AgentType..." -ForegroundColor Green

        # Remove any existing Imprivata installations first
        Write-Host "🔄 Removing any existing Imprivata installations..." -ForegroundColor Yellow
        try {
            Start-Process -FilePath "MsiExec.exe" -ArgumentList "/X{E8A87655-4D4C-4ADF-A097-D4FBBB95CA42} /q REBOOT=REALLYSUPPRESS" -Wait -NoNewWindow -ErrorAction SilentlyContinue
            Start-Process -FilePath "MsiExec.exe" -ArgumentList "/X{26B7B974-0DE7-4CD2-93D7-B3D9CBFC91B2} /q REBOOT=REALLYSUPPRESS" -Wait -NoNewWindow -ErrorAction SilentlyContinue
            Write-Host "✅ Old Imprivata installations removed" -ForegroundColor Green
        }
        catch {
            Write-Host "ℹ️ Note: No existing Imprivata installations found to remove" -ForegroundColor Gray
        }

        # Check if Imprivata MSI exists
        $imprivataAgent = "C:\Files\ImprivataAgent_x64.msi"

        if (Test-Path $imprivataAgent) {
            Write-Host "🚀 Installing Imprivata Agent Type $AgentType..." -ForegroundColor Yellow
            try {
                # Use the exact command provided for installation
                $arguments = "/i `"$imprivataAgent`" /q /norestart AGENTTYPE=$AgentType IPTXPRIMSERVER=https://mhc-lxasimp01.mclaren.org/sso/servlet/messagerouter"
                $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $arguments -Wait -NoNewWindow -PassThru

                Write-Host "📊 Installation process completed with exit code: $($process.ExitCode)" -ForegroundColor Gray

                # Wait for installation to complete
                Write-Host "⏳ Waiting for Imprivata installation to complete..." -ForegroundColor Yellow
                Start-Sleep -Seconds 15

                # Verify installation
                $installed = $false
                $maxAttempts = 6
                $attempt = 0

                while (-not $installed -and $attempt -lt $maxAttempts) {
                    $attempt++
                    Write-Host "🔍 Verification attempt $attempt of $maxAttempts..." -ForegroundColor Gray

                    # Check for Imprivata service
                    $imprivataService = Get-Service -Name "*Imprivata*" -ErrorAction SilentlyContinue

                    # Check for Imprivata processes
                    $imprivataProcess = Get-Process -Name "*Imprivata*" -ErrorAction SilentlyContinue

                    # Check for Imprivata registry entries
                    $imprivataRegistry = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue | Where-Object { $_.DisplayName -like "*Imprivata*" }

                    if ($imprivataService -or $imprivataProcess -or $imprivataRegistry) {
                        $installed = $true
                        Write-Host "✅ Imprivata Agent Type $AgentType installation verified successfully!" -ForegroundColor Green

                        if ($imprivataService) {
                            Write-Host "  🔧 Found Imprivata service: $($imprivataService.Name)" -ForegroundColor Gray
                        }
                        if ($imprivataProcess) {
                            Write-Host "  🔧 Found Imprivata process: $($imprivataProcess.Name -join ', ')" -ForegroundColor Gray
                        }
                        if ($imprivataRegistry) {
                            Write-Host "  🔧 Found Imprivata in registry: $($imprivataRegistry.DisplayName)" -ForegroundColor Gray
                        }
                        return "✅ Success: Imprivata Agent Type $AgentType installed and verified"
                    } else {
                        Write-Host "  ⏳ Imprivata not detected yet, waiting 10 seconds..." -ForegroundColor Yellow
                        Start-Sleep -Seconds 10
                    }
                }

                if (-not $installed) {
                    Write-Host "⚠️ Warning: Could not verify Imprivata installation after $($maxAttempts * 10) seconds" -ForegroundColor Red
                    Write-Host "Installation may still be in progress or may have failed" -ForegroundColor Yellow
                    return "⚠️ Warning: Imprivata installation could not be verified"
                }
            }
            catch {
                Write-Host "❌ Error: Failed to install Imprivata Agent Type $AgentType - $_" -ForegroundColor Red
                return "❌ Error: Installation failed - $_"
            }
        } else {
            Write-Host "❌ Error: ImprivataAgent_x64.msi not found at $imprivataAgent" -ForegroundColor Red

            # List available files for troubleshooting
            Write-Host "📁 Available files in C:\Files\:" -ForegroundColor Yellow
            if (Test-Path "C:\Files\") {
                Get-ChildItem -Path "C:\Files\" -Filter "*.msi" | ForEach-Object {
                    Write-Host "  📄 $($_.Name)" -ForegroundColor Gray
                }
            }
            return "❌ Error: Imprivata MSI file not found"
        }
    } -ArgumentList $AgentType

    Write-Color $result "Gray"

    # Configure Type 2 autologin settings if needed
    if ($AgentType -eq 2 -and $AutoUsername -and $AutoPassword) {
        Write-Color "🔧 Configuring Type 2 autologin settings..." "Cyan"

        # Convert secure string to plain text for registry
        $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($AutoPassword)
        $PlainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
        [Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)

        $configResult = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
            param($Username, $Password)

            try {
                # Configure Winlogon settings
                $winlogonPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon"
                Set-ItemProperty -Path $winlogonPath -Name "DefaultUserName" -Value $Username
                Set-ItemProperty -Path $winlogonPath -Name "DefaultDomainName" -Value "MCLAREN"
                Set-ItemProperty -Path $winlogonPath -Name "DefaultPassword" -Value $Password
                Set-ItemProperty -Path $winlogonPath -Name "AutoAdminLogon" -Value "1"
                Set-ItemProperty -Path $winlogonPath -Name "ForceAutoLogon" -Value "1"

                # Configure System Policies
                Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "DontDisplayLastUsername" -Value 1 -Type DWord

                # Configure SSO Provider
                $ssoPath = "HKLM:\SOFTWARE\SSOProvider\ISXAgent"
                if (-not (Test-Path $ssoPath)) {
                    New-Item -Path $ssoPath -Force | Out-Null
                }
                Set-ItemProperty -Path $ssoPath -Name "Type" -Value 2 -Type DWord

                Write-Host "✅ Type 2 autologin configuration completed successfully" -ForegroundColor Green
                return "✅ Success: Type 2 autologin configured"
            } catch {
                Write-Host "❌ Error configuring Type 2 autologin: $_" -ForegroundColor Red
                return "❌ Error: Type 2 autologin configuration failed - $_"
            }
        } -ArgumentList $AutoUsername, $PlainPassword

        Write-Color $configResult "Gray"

        # Clear sensitive data
        Remove-Variable -Name AutoUsername, PlainPassword -ErrorAction SilentlyContinue
        [GC]::Collect()
    }
}

function Install-SystemConfiguration {
    param([string]$TargetComputer)

    Write-Color "Installing system configuration and applications..." "Cyan"

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($UseLocalInstallers)

        Write-Host "***" -ForegroundColor Green
        Write-Host "*** System Configuration and Application Installation"

        # Install .NET 3.5
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Checking .NET 3.5 installation"
        $dotNetInstalled = Get-WindowsOptionalFeature -Online -FeatureName "NetFx3" | Where-Object { $_.State -eq "Enabled" }

        if ($dotNetInstalled) {
            Write-Host "✅ .NET 3.5 is already installed - skipping installation" -ForegroundColor Yellow
        } else {
            Write-Host "🔄 Installing .NET 3.5" -ForegroundColor Yellow
            if (Test-Path "C:\Files\sxs") {
                Enable-WindowsOptionalFeature -Online -FeatureName "NetFx3" -Source "C:\Files\sxs" -All
                Write-Host "✅ .NET 3.5 installation complete" -ForegroundColor Green
            } else {
                Write-Host "⚠️ Warning: .NET 3.5 source files not found at C:\Files\sxs" -ForegroundColor Yellow
            }
        }

        # Install Java
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Checking Java installation"
        $javaInstalled = $false
        $javaPaths = @(
            "C:\Program Files\Java\jre*",
            "C:\Program Files (x86)\Java\jre*"
        )

        foreach ($path in $javaPaths) {
            if (Test-Path $path) {
                $javaInstalled = $true
                Write-Host "✅ Java found at: $path" -ForegroundColor Green
                break
            }
        }

        if ($javaInstalled) {
            Write-Host "✅ Java is already installed - skipping installation" -ForegroundColor Yellow
        } else {
            Write-Host "🔄 Installing Java" -ForegroundColor Yellow
            if (Test-Path "C:\Files\jre1.7.0_45.msi") {
                Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"C:\Files\jre1.7.0_45.msi`" /quiet" -Wait -NoNewWindow
                Write-Host "✅ Java installation complete" -ForegroundColor Green
            } else {
                Write-Host "⚠️ Warning: Java installer not found at C:\Files\jre1.7.0_45.msi" -ForegroundColor Yellow
            }
        }

        # Install Chrome
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Checking Chrome installation"
        $chromeInstalled = $false
        $chromePaths = @(
            "C:\Program Files\Google\Chrome\Application\chrome.exe",
            "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        )

        foreach ($path in $chromePaths) {
            if (Test-Path $path) {
                $chromeInstalled = $true
                Write-Host "✅ Chrome found at: $path" -ForegroundColor Green
                break
            }
        }

        if ($chromeInstalled) {
            Write-Host "✅ Chrome is already installed - skipping installation" -ForegroundColor Yellow
        } else {
            Write-Host "🔄 Installing Chrome" -ForegroundColor Yellow
            if (Test-Path "C:\Files\GoogleChromeStandaloneEnterprise64.msi") {
                Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"C:\Files\GoogleChromeStandaloneEnterprise64.msi`" /quiet" -Wait -NoNewWindow
                Write-Host "✅ Chrome installation complete" -ForegroundColor Green
            } else {
                Write-Host "⚠️ Warning: Chrome installer not found at C:\Files\GoogleChromeStandaloneEnterprise64.msi" -ForegroundColor Yellow
            }
        }

        # Install Citrix Receiver
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Checking Citrix Receiver installation"
        $citrixInstalled = $false
        $citrixPaths = @(
            "C:\Program Files (x86)\Citrix\ICA Client\wfcrun32.exe",
            "C:\Program Files\Citrix\ICA Client\wfcrun32.exe",
            "C:\Program Files (x86)\Citrix\ICA Client\Receiver\Receiver.exe",
            "C:\Program Files\Citrix\ICA Client\Receiver\Receiver.exe"
        )

        foreach ($path in $citrixPaths) {
            if (Test-Path $path) {
                $citrixInstalled = $true
                Write-Host "✅ Citrix Receiver found at: $path" -ForegroundColor Green
                break
            }
        }

        if ($citrixInstalled) {
            Write-Host "✅ Citrix Receiver is already installed - skipping installation" -ForegroundColor Yellow
        } else {
            Write-Host "🔄 Installing Citrix Receiver 4.9" -ForegroundColor Yellow
            if (Test-Path "C:\Files\4.9_LTSR2\CitrixReceiver.cmd") {
                $p = Start-Process -PassThru "C:\Files\4.9_LTSR2\CitrixReceiver.cmd"
                $p.WaitForExit()
                Write-Host "✅ Citrix Receiver installation complete" -ForegroundColor Green
            } else {
                Write-Host "⚠️ Warning: Citrix installer script not found at C:\Files\4.9_LTSR2\CitrixReceiver.cmd" -ForegroundColor Yellow
            }
        }

        # Install Nuance Components (Enhanced with both components clearly displayed)
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Installing Nuance Cerner Dragon Mic Components"

        # Component 1: Nuance Citrix Client Audio Extension
        Write-Host "🎤 Installing Nuance Citrix Client Audio Extension" -ForegroundColor Yellow
        if (Test-Path "C:\Files\Nuance121\Nuance Citrix Client Audio Extension.msi") {
            try {
                Start-Process -FilePath "C:\Files\Nuance121\Nuance Citrix Client Audio Extension.msi" -ArgumentList ('/qb REBOOT=REALLYSUPPRESS') -Wait
                Write-Host "✅ Nuance Citrix Client Audio Extension installation complete" -ForegroundColor Green
            } catch {
                Write-Host "❌ Error installing Nuance Audio Extension: $_" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ Warning: Nuance Audio Extension installer not found at C:\Files\Nuance121\" -ForegroundColor Yellow
        }

        # Component 2: Nuance PowerMic Citrix Client Extension
        Write-Host "🎙️ Installing Nuance PowerMic Citrix Client Extension" -ForegroundColor Yellow
        if (Test-Path "C:\Files\Nuance121\Nuance PowerMic Citrix Client Extension.msi") {
            try {
                Start-Process -FilePath "C:\Files\Nuance121\Nuance PowerMic Citrix Client Extension.msi" -ArgumentList ('/qb REBOOT=REALLYSUPPRESS') -Wait
                Write-Host "✅ Nuance PowerMic Citrix Client Extension installation complete" -ForegroundColor Green
            } catch {
                Write-Host "❌ Error installing Nuance PowerMic Extension: $_" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ Warning: Nuance PowerMic Extension installer not found at C:\Files\Nuance121\" -ForegroundColor Yellow
        }

        Write-Host "🎯 Nuance installation process completed" -ForegroundColor Green

        return "✅ Success: System configuration completed"

    } -ArgumentList $script:useLocalInstallers

    Write-Color $result "Gray"
}

function Install-LexmarkDriver {
    param([string]$TargetComputer)

    Write-Color "Installing Lexmark Universal Driver..." "Cyan"

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($UseLocalInstallers)

        if ($UseLocalInstallers) {
            # Copy from local D:\ drive to C:\Files\
            $sourcePath = "D:\LexmarkGDI"
            $destPath = "C:\Files\Drivers\Print\GDI"

            if (Test-Path $sourcePath) {
                # Create destination directory
                if (-not (Test-Path $destPath)) {
                    New-Item -Path $destPath -ItemType Directory -Force | Out-Null
                }

                # Copy files using robocopy on the target computer
                $result = Start-Process -FilePath "robocopy" -ArgumentList "`"$sourcePath`"", "`"$destPath`"", "/E", "/R:3", "/W:10" -Wait -PassThru -NoNewWindow

                if ($result.ExitCode -le 7) {
                    Write-Host "✅ Lexmark driver files copied successfully from local D:\ drive" -ForegroundColor Green
                } else {
                    Write-Host "⚠️ Warning: Lexmark driver copy completed with exit code: $($result.ExitCode)" -ForegroundColor Yellow
                }
            } else {
                Write-Host "⚠️ Warning: Lexmark driver source not found at $sourcePath" -ForegroundColor Yellow
            }
        }

        # Install the print driver
        $driverPath = "C:\Files\Drivers\Print\GDI\LMUD1n40.inf"
        if (Test-Path $driverPath) {
            try {
                Pnputil /add-driver $driverPath /install
                Add-PrinterDriver -Name "Lexmark Universal v2 PostScript 3 Emulation" -ErrorAction SilentlyContinue
                Remove-Item -Path "C:\Files\Drivers" -Recurse -Force -ErrorAction SilentlyContinue
                Write-Host "✅ Lexmark driver installed and files cleaned up." -ForegroundColor Green
                return "✅ Success: Lexmark driver installed"
            }
            catch {
                Write-Host "⚠️ Warning: Failed to install Lexmark driver - $_" -ForegroundColor Yellow
                return "⚠️ Warning: Lexmark driver installation failed"
            }
        } else {
            Write-Host "⚠️ Warning: Lexmark driver file not found at $driverPath" -ForegroundColor Yellow
            return "⚠️ Warning: Lexmark driver file not found"
        }
    } -ArgumentList $script:useLocalInstallers

    Write-Color $result "Gray"
}

function Install-VPNClient {
    param([string]$TargetComputer)

    Write-Color "Installing Cisco Secure Client VPN..." "Cyan"

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($UseLocalInstallers)

        Write-Host "🌐 Installing Cisco Secure Client VPN"
        $netInstallerPath = "\\mhc-msassccm1\sources\apps\msi\Cisco\Secure_Client\5_1_0_136"
        $remoteFolderPath = "C:\files\5_1_0_136"
        $remoteScriptPath = "$remoteFolderPath\Deploy-Application.ps1"

        # Create the destination directory
        if (-Not (Test-Path -Path $remoteFolderPath)) {
            New-Item -ItemType Directory -Path $remoteFolderPath -Force | Out-Null
            Write-Host "📁 Created directory: $remoteFolderPath" -ForegroundColor Green
        }

        # Copy installer files
        if ($UseLocalInstallers) {
            # Copy from local D:\ drive
            $localSourcePath = "D:\5_1_0_136"
            if (Test-Path $localSourcePath) {
                Write-Host "📥 Copying VPN installer from local D:\ drive..." -ForegroundColor Yellow
                $result = Start-Process -FilePath "robocopy" -ArgumentList "`"$localSourcePath`"", "`"$remoteFolderPath`"", "/E", "/R:3", "/W:10" -Wait -PassThru -NoNewWindow
                if ($result.ExitCode -le 7) {
                    Write-Host "✅ VPN installer files copied successfully from local D:\ drive" -ForegroundColor Green
                } else {
                    Write-Host "⚠️ Warning: VPN installer copy completed with exit code: $($result.ExitCode)" -ForegroundColor Yellow
                }
            } else {
                Write-Host "⚠️ Warning: VPN installer source not found at $localSourcePath" -ForegroundColor Yellow
                return "⚠️ Warning: VPN installer files not found on local D:\ drive"
            }
        } else {
            # Copy from network path
            Write-Host "📥 Copying VPN installer from network path..." -ForegroundColor Yellow
            try {
                $result = Start-Process -FilePath "robocopy" -ArgumentList "`"$netInstallerPath`"", "`"$remoteFolderPath`"", "/E", "/R:3", "/W:10" -Wait -PassThru -NoNewWindow
                if ($result.ExitCode -le 7) {
                    Write-Host "✅ VPN installer files copied successfully from network" -ForegroundColor Green
                } else {
                    Write-Host "⚠️ Warning: VPN installer copy completed with exit code: $($result.ExitCode)" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "❌ Error copying from network: $_" -ForegroundColor Red
                return "❌ Error: Failed to copy VPN installer from network"
            }
        }

        # Execute the installation script
        if (Test-Path $remoteScriptPath) {
            Write-Host "🚀 Executing VPN installation script..." -ForegroundColor Yellow
            try {
                Start-Process -FilePath "powershell.exe" -ArgumentList "-ExecutionPolicy Bypass -File `"$remoteScriptPath`" -DeployMode Silent" -Wait -NoNewWindow
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✅ Cisco Secure Client VPN installed successfully" -ForegroundColor Green
                } else {
                    Write-Host "⚠️ VPN installation completed with exit code: $LASTEXITCODE" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "❌ Error during VPN installation: $_" -ForegroundColor Red
                return "❌ Error: VPN installation failed"
            }
        } else {
            Write-Host "⚠️ Warning: VPN installation script not found at $remoteScriptPath" -ForegroundColor Yellow
            return "⚠️ Warning: VPN installation script not found"
        }

        # Clean up installer files
        try {
            Remove-Item -Path $remoteFolderPath -Recurse -Force -ErrorAction SilentlyContinue
            Write-Host "🧹 VPN installer files cleaned up" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ Warning: Could not clean up VPN installer files" -ForegroundColor Yellow
        }

        return "✅ Success: Cisco Secure Client VPN installed"

    } -ArgumentList $script:useLocalInstallers

    Write-Color $result "Gray"
}

function Install-OfficeApplication {
    param([string]$TargetComputer, [array]$SourceGroups)

    Write-Color "Checking Office 365 installation requirements..." "Cyan"

    # Determine Imprivata configuration from source groups
    $installType1 = $SourceGroups | Where-Object { $_ -like "*type1*"}
    $installType2 = $SourceGroups | Where-Object { $_ -like "*type2*"}

    # Check if Type 2 (Type 2 should NOT get Office)
    if ($installType2) {
        Write-Color "⚠️ Type 2 membership detected - Office 365 will NOT be installed" "Yellow"
        return
    }

    # For Type 1 or no Imprivata, ask about Office installation
    $installOffice = $false
    if (-not $script:autoYes) {
        $officeChoice = Read-Host "Do you want to install Office 365? (Y/N)"
        $installOffice = $officeChoice -match '^(Y|y)$'
    } else {
        $installOffice = $true  # Auto-yes mode installs Office for non-Type 2
    }

    if (-not $installOffice) {
        Write-Color "⏭️ Office 365 installation skipped by user choice" "Yellow"
        return
    }

    Write-Color "✅ Office 365 will be installed" "Green"

    # Copy Office 365 files first
    Write-Color "📥 Copying Office 365 installer files..." "Cyan"
    $installerPaths = Get-InstallerPaths -TargetComputer $TargetComputer

    try {
        if ($script:useLocalInstallers) {
            # Use remote command to copy from local D:\ drive on target computer
            $copyResult = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
                $sourcePath = "D:\o365"
                $destPath = "C:\Files\o365"

                if (Test-Path $sourcePath) {
                    # Create destination directory
                    if (-not (Test-Path $destPath)) {
                        New-Item -Path $destPath -ItemType Directory -Force | Out-Null
                    }

                    # Copy folder contents using robocopy
                    $result = Start-Process -FilePath "robocopy" -ArgumentList "`"$sourcePath`"", "`"$destPath`"", "/E", "/R:3", "/W:10" -Wait -PassThru -NoNewWindow
                    if ($result.ExitCode -le 7) {
                        return "✅ Success: Office 365 files copied from local D:\ drive"
                    } else {
                        return "⚠️ Warning: Office 365 copy completed with exit code $($result.ExitCode)"
                    }
                } else {
                    return "❌ Error: Office 365 source not found at $sourcePath"
                }
            }
            Write-Color $copyResult "Gray"
        } else {
            # Network mode - copy from network location
            $destPath = "\\$TargetComputer\c$\Files\o365"
            if (-not (Test-Path $destPath)) {
                New-Item -Path $destPath -ItemType Directory -Force | Out-Null
            }
            Copy-Item -Path "$($installerPaths.Office365)\*" -Destination $destPath -Recurse -Force
            Write-Color "✅ Success: Office 365 files copied from network" "Gray"
        }
    } catch {
        Write-Color "❌ Error copying Office 365 files: $_" "Red"
        Write-Color "⏭️ Office 365 installation will be skipped due to file copy failure" "Yellow"
        return
    }

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        Write-Host "🚀 Install Office 365"
        Write-Host "*** Running Office 365 installation"
        if (Test-Path "C:\Files\o365\batch_365.bat") {
            $p = Start-Process -FilePath "C:\Files\o365\batch_365.bat" -PassThru
            $p.WaitForExit()
            Write-Host "🧹 Deleting C:\Files\o365 folder"
            Remove-Item -Path "C:\Files\o365" -Recurse -Force

            # Office Ribbon Fix
            Write-Host "🔧" -ForegroundColor Green
            Write-Output "Office ribbon key fix being applied"
            $RegistryPath = "HKCU:\Software\Microsoft\Office\16.0\Common\ExperimentConfigs\ExternalFeatureOverrides\word"
            $RegistryName = "Microsoft.Office.UXPlatform.FluentSVRefresh"
            $RegistryValue = "false"
            if (-not (Test-Path $RegistryPath)) {
                New-Item -Path $RegistryPath -Force | Out-Null
            }
            Set-ItemProperty -Path $RegistryPath -Name $RegistryName -Value $RegistryValue
            Write-Output "Registry key '$RegistryName' set to '$RegistryValue' in path '$RegistryPath'."
            Write-Host "✅ Office ribbon key and value have been set successfully."

            return "✅ Success: Office 365 installed"
        } else {
            Write-Host "⚠️ Warning: Office batch file not found at C:\Files\o365\batch_365.bat" -ForegroundColor Yellow
            return "⚠️ Warning: Office 365 installer not found"
        }
    }

    Write-Color $result "Gray"
}

function Install-DellCommandUpdate {
    param([string]$TargetComputer)

    Write-Color "Installing and configuring Dell Command Update..." "Cyan"

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($UseLocalInstallers)

        Write-Host "🖥️ Installing Dell Command Update"
        $DellCommandUpdatePath = "C:\Program Files (x86)\Dell\CommandUpdate\dcu-cli.exe"
        $DellCommandUpdatePath2 = "C:\Program Files\Dell\CommandUpdate\dcu-cli.exe"
        $BiosPassword = "B1TuSer"
        $targetDirectory = "c:\files\dcu"

        # Create target directory
        if (-not (Test-Path -Path $targetDirectory)) {
            New-Item -Path $targetDirectory -ItemType Directory -Force
        }

        # Copy installer file if using local installers
        if ($UseLocalInstallers) {
            if (Test-Path "D:\Commandupdate.EXE") {
                Copy-Item -Path "D:\Commandupdate.EXE" -Destination "$targetDirectory\Commandupdate.EXE" -Force
                Write-Host "✅ Dell Command Update installer copied from D:\ drive" -ForegroundColor Green
            } elseif (Test-Path "D:\dcu\Commandupdate.EXE") {
                Copy-Item -Path "D:\dcu\Commandupdate.EXE" -Destination "$targetDirectory\Commandupdate.EXE" -Force
                Write-Host "✅ Dell Command Update installer copied from D:\dcu\ drive" -ForegroundColor Green
            } else {
                Write-Host "⚠️ Warning: Dell Command Update installer not found at D:\Commandupdate.EXE or D:\dcu\Commandupdate.EXE" -ForegroundColor Yellow
            }
        }

        # Install Dell Command Update
        $InstallerFile = Join-Path $targetDirectory "Commandupdate.EXE"
        if (Test-Path $InstallerFile) {
            Write-Host "🔄 Installing Dell Command Update..."
            Start-Process -FilePath $InstallerFile -ArgumentList "/S" -Wait -NoNewWindow -ErrorAction Stop
            Write-Host "✅ Dell Command Update installed successfully" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Warning: Dell Command Update installer not found at $InstallerFile" -ForegroundColor Yellow
        }

        # Configure Dell Command Update
        Start-Sleep -Seconds 5  # Wait for installation to complete
        if (Test-Path $DellCommandUpdatePath) {
            Write-Host "⚙️ Configuring Dell Command Update (x86 path)..."
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -biosPassword=$BiosPassword" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -autoSuspendBitLocker=enable" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -scheduleManual" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -updatesNotification=Disable" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -scheduleAction=DownloadInstallAndNotify" -NoNewWindow -Wait
            Write-Host "🔍 Running initial scan and update..."
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/scan" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/applyupdates -reboot=disable" -NoNewWindow -Wait
            Write-Host "✅ Dell Command Update configured successfully" -ForegroundColor Green
        }
        elseif (Test-Path $DellCommandUpdatePath2) {
            Write-Host "⚙️ Configuring Dell Command Update (x64 path)..."
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -biosPassword=$BiosPassword" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -autoSuspendBitLocker=enable" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -scheduleManual" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -updatesNotification=Disable" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -scheduleAction=DownloadInstallAndNotify" -NoNewWindow -Wait
            Write-Host "🔍 Running initial scan and update..."
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/scan" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/applyupdates -reboot=disable" -NoNewWindow -Wait
            Write-Host "✅ Dell Command Update configured successfully" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Warning: Dell Command Update CLI not found after installation" -ForegroundColor Yellow
        }

        # Clean up installer files
        Remove-Item -Path $targetDirectory -Recurse -Force -ErrorAction SilentlyContinue

        return "✅ Success: Dell Command Update installed and configured"

    } -ArgumentList $script:useLocalInstallers

    Write-Color $result "Gray"
}

function Backup-PublicDesktopAndVBS {
    param([string]$SourceComputer, [string]$TargetComputer)

    Write-Color "Starting limited backup for Type 2 computer (public desktop and VBS files only)..." "Cyan"

    # Create backup directory
    $BackupPath = "\\$TargetComputer\c$\Files\user_profile_backups\"
    if (-not (Test-Path $BackupPath)) {
        New-Item -Path $BackupPath -ItemType Directory -Force | Out-Null
    }

    # Backup .VBS printer scripts from startup folder
    Write-Color "📄 Backing up .vbs printer scripts in startup folder" "Cyan"
    $sourceStartup = "\\$SourceComputer\c$\ProgramData\Microsoft\Windows\Start Menu\Programs\Startup"
    $destStartup   = "\\$TargetComputer\c$\ProgramData\Microsoft\Windows\Start Menu\Programs\Startup"

    try {
        $vbsFiles = Get-ChildItem -Path $sourceStartup -Filter *.vbs -File -ErrorAction SilentlyContinue
        foreach ($file in $vbsFiles) {
            $destFile = Join-Path $destStartup $file.Name
            try {
                Copy-Item -Path $file.FullName -Destination $destFile -Force
                Write-Color "✅ Copied $($file.Name) from $SourceComputer to $TargetComputer startup folder." "Green"
            } catch {
                Write-Color "❌ Failed to copy $($file.Name): $_" "Red"
            }
        }
        if ($vbsFiles.Count -eq 0) {
            Write-Color "ℹ️ No VBS files found in startup folder" "Gray"
        }
    } catch {
        Write-Color "❌ Error accessing startup folder: $_" "Red"
    }

    # Backup Public desktop
    Write-Color "🖥️ Backing up Public desktop" "Cyan"
    $USP1 = "\\$SourceComputer\c$\Users\public\desktop"
    $UDP1 = $BackupPath+"public\desktop"

    try {
        robocopy $USP1 $UDP1 /E /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /XC /XN | Out-Null
        Write-Color "✅ Public desktop backup completed" "Green"
    } catch {
        Write-Color "❌ Error copying public desktop: $_" "Red"
    }

    Write-Color "✅ Limited backup completed for Type 2 computer" "Green"
}

function Backup-UserProfiles {
    param([string]$SourceComputer, [string]$TargetComputer)

    Write-Color "Starting comprehensive user profile backup..." "Cyan"

    # Get profile age cutoff
    $daysInput = Read-Host -Prompt "Enter maximum profile age in days (default 30)"
    if (-not [int]::TryParse($daysInput, [ref]$null)) { $daysInput = 30 }
    $cutoffDays = [math]::Max(1, [int]$daysInput)
    $cutoffDate = (Get-Date).AddDays(-$cutoffDays)

    # Store current date for logging purposes if needed
    $BackupPath = "\\$TargetComputer\c$\Files\user_profile_backups\"

    # Create necessary directories
    if (-not (Test-Path $BackupPath)) {
        Show-Spinner "Creating backup directory..." 1
        New-Item -Path $BackupPath -ItemType Directory -Force | Out-Null
    }

    if (-not (Test-Path "C:\files\ProfileBackup")) {
        Show-Spinner "Creating profile backup directory..." 1
        New-Item -Path "C:\files\ProfileBackup" -ItemType Directory -Force | Out-Null
    }

    $UserFile = "C:\files\ProfileBackup\Users.txt"
    Show-Spinner "Getting profiles from source computer..." 2
    (Get-CimInstance -ClassName Win32_UserProfile -ComputerName $SourceComputer).LocalPath | Out-File $UserFile
    $Users = Get-Content -Path $UserFile

    # Process profiles
    $ProfileInfoList = @()
    ForEach ($User in $Users) {
        if ($User -ne "C:\WINDOWS\system32\config\systemprofile" -and
            $User -ne "C:\WINDOWS\ServiceProfiles\NetworkService" -and
            $User -ne "C:\WINDOWS\ServiceProfiles\LocalService" -and
            $User -notlike "*ark*" -and
            $User -notlike "*Imprivata*") {

            $ModifiedPath = $User.Replace("C:\", "\\$SourceComputer\c$\")
            $paths = @(
                "$ModifiedPath\Desktop\Shortcuts",
                "$ModifiedPath\Desktop\McLaren Safety First.url",
                "$ModifiedPath\AppData\Local\Microsoft\Edge\User Data\Default",
                "$ModifiedPath\Desktop\Microsoft Edge.lnk"
            )
            $UserModifiedDate = $null
            foreach ($path in $paths) {
                if (Test-Path -Path $path) {
                    $UserModifiedDate = (Get-Item -Path $path).LastWriteTime
                    break
                }
            }
            if (-not $UserModifiedDate) { $UserModifiedDate = Get-Date "2000-01-01" }
            $ProfileInfoList += [PSCustomObject]@{ User = $User; ModifiedDate = $UserModifiedDate }
        }
    }

    # Copy profiles
    $ProfilesToCopy = $ProfileInfoList | Where-Object { $_.ModifiedDate -ge $cutoffDate }
    if ($ProfilesToCopy.Count -eq 0) {
        $MostRecentProfile = $ProfileInfoList | Sort-Object -Property ModifiedDate -Descending | Select-Object -First 1
        if ($MostRecentProfile) {
            Write-Color "⚠️ No profiles found within the last $cutoffDays days. Copying the most recently used profile: $($MostRecentProfile.User) (Last modified: $($MostRecentProfile.ModifiedDate))" "Yellow"
            $ProfilesToCopy = @($MostRecentProfile)
        } else {
            Write-Color "❌ No eligible user profiles found to copy." "Red"
        }
    }

    foreach ($Profile in $ProfilesToCopy) {
        $UserOnly = $Profile.User.Replace('C:\Users\<USER>\',"\\$SourceComputer\c$\")
        $UserDestinationPath = Join-Path $BackupPath $UserOnly
        Write-Color " " "Gray"
        Write-Color "📁 Backing up $UserOnly (Last modified: $($Profile.ModifiedDate.ToString('yyyy-MM-dd')))" "Cyan"

        $paths = @{
            Chrome = @{
                Source = "$UserSourcePath\AppData\Local\Google\Chrome\User Data\Default"
                Dest = "$UserDestinationPath\Chrome"
                Description = "Chrome profile"
            }
            Edge = @{
                Source = "$UserSourcePath\AppData\Local\Microsoft\Edge\User Data\Default"
                Dest = "$UserDestinationPath\Edge"
                Description = "Edge profile"
            }
            Desktop = @{
                Source = "$UserSourcePath\Desktop"
                Dest = "$UserDestinationPath\Desktop"
                Description = "Desktop"
            }
            Documents = @{
                Source = "$UserSourcePath\Documents"
                Dest = "$UserDestinationPath\Documents"
                Description = "Documents"
            }
            Downloads = @{
                Source = "$UserSourcePath\Downloads"
                Dest = "$UserDestinationPath\Downloads"
                Description = "Downloads"
            }
            Favorites = @{
                Source = "$UserSourcePath\Favorites"
                Dest = "$UserDestinationPath\Favorites"
                Description = "Favorites"
            }
            Pictures = @{
                Source = "$UserSourcePath\Pictures"
                Dest = "$UserDestinationPath\Pictures"
                Description = "Pictures"
            }
        }

        foreach ($pathInfo in $paths.GetEnumerator()) {
            $source = $pathInfo.Value.Source
            $dest = $pathInfo.Value.Dest
            $desc = $pathInfo.Value.Description

            if (Test-Path $source) {
                try {
                    Write-Color "  📂 Copying $desc..." "Gray"
                    robocopy $source $dest /E /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /XC /XN | Out-Null
                    Write-Color "  ✅ $desc copied successfully" "Green"
                } catch {
                    Write-Color "  ❌ Failed to copy $desc`: $_" "Red"
                }
            } else {
                Write-Color "  ⚠️ $desc not found at source" "Yellow"
            }
        }
    }

    Write-Color "✅ User profile backup completed" "Green"
}

function Create-ProfileBackupScript {
    param([string]$TargetComputer)

    Write-Color "Creating profile backup script on target computer..." "Cyan"

    # Create the profile backup script content as a here-string
    $profileBackupScript = @'
# Profile Backup Script - Auto-generated by Migration Wizard
# This script backs up user profiles from the current computer

param(
    [string]$BackupDestination = "C:\ProfileBackups",
    [int]$MaxProfileAgeDays = 30
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path "$BackupDestination\backup_log.txt" -Value $logMessage
}

function Backup-UserProfile {
    param([string]$ProfilePath, [string]$UserName, [string]$BackupPath)

    Write-Log "Starting backup for user: $UserName"

    # Create user backup directory
    $userBackupPath = Join-Path $BackupPath $UserName
    if (-not (Test-Path $userBackupPath)) {
        New-Item -Path $userBackupPath -ItemType Directory -Force | Out-Null
    }

    # Define folders to backup
    $foldersToBackup = @{
        "Desktop" = "$ProfilePath\Desktop"
        "Documents" = "$ProfilePath\Documents"
        "Downloads" = "$ProfilePath\Downloads"
        "Favorites" = "$ProfilePath\Favorites"
        "Pictures" = "$ProfilePath\Pictures"
        "Chrome" = "$ProfilePath\AppData\Local\Google\Chrome\User Data\Default"
        "Edge" = "$ProfilePath\AppData\Local\Microsoft\Edge\User Data\Default"
    }

    foreach ($folder in $foldersToBackup.GetEnumerator()) {
        $sourcePath = $folder.Value
        $destPath = Join-Path $userBackupPath $folder.Key

        if (Test-Path $sourcePath) {
            try {
                Write-Log "Backing up $($folder.Key) for $UserName"
                robocopy "$sourcePath" "$destPath" /E /R:3 /W:10 /MT:8 /NFL /NDL /NJH /NJS /NP /XC /XN | Out-Null
                if ($LASTEXITCODE -le 7) {
                    Write-Log "Successfully backed up $($folder.Key) for $UserName"
                } else {
                    Write-Log "Warning: Backup of $($folder.Key) completed with exit code $LASTEXITCODE" "WARNING"
                }
            } catch {
                Write-Log "Error backing up $($folder.Key) for $UserName: $_" "ERROR"
            }
        } else {
            Write-Log "$($folder.Key) not found for $UserName - skipping" "WARNING"
        }
    }

    Write-Log "Completed backup for user: $UserName"
}

# Main backup process
Write-Log "=== Profile Backup Script Started ==="
Write-Log "Backup Destination: $BackupDestination"
Write-Log "Max Profile Age: $MaxProfileAgeDays days"

# Create backup destination
if (-not (Test-Path $BackupDestination)) {
    New-Item -Path $BackupDestination -ItemType Directory -Force | Out-Null
    Write-Log "Created backup destination: $BackupDestination"
}

# Get user profiles
$cutoffDate = (Get-Date).AddDays(-$MaxProfileAgeDays)
$profiles = Get-WmiObject -Class Win32_UserProfile | Where-Object {
    $_.LocalPath -notlike "*system*" -and
    $_.LocalPath -notlike "*NetworkService*" -and
    $_.LocalPath -notlike "*LocalService*" -and
    $_.LocalPath -notlike "*ark*" -and
    $_.LocalPath -notlike "*Imprivata*" -and
    $_.LocalPath -like "C:\Users\<USER>\Desktop",
        "$profilePath\Documents",
        "$profilePath\AppData\Local\Microsoft\Edge\User Data\Default"
    )

    foreach ($checkPath in $checkPaths) {
        if (Test-Path $checkPath) {
            $profileAge = (Get-Item $checkPath).LastWriteTime
            break
        }
    }

    if ($profileAge -and $profileAge -ge $cutoffDate) {
        Write-Log "Profile $userName is recent (last activity: $profileAge) - backing up"
        Backup-UserProfile -ProfilePath $profilePath -UserName $userName -BackupPath $BackupDestination
        $backedUpCount++
    } else {
        $ageText = if ($profileAge) { $profileAge.ToString() } else { "unknown" }
        Write-Log "Profile $userName is too old or inactive (last activity: $ageText) - skipping"
    }
}

Write-Log "=== Profile Backup Script Completed ==="
Write-Log "Total profiles backed up: $backedUpCount"
Write-Log "Backup location: $BackupDestination"

# Create summary file
$summary = @"
Profile Backup Summary
======================
Date: $(Get-Date)
Computer: $env:COMPUTERNAME
Backup Location: $BackupDestination
Max Profile Age: $MaxProfileAgeDays days
Total Profiles Backed Up: $backedUpCount

For detailed logs, see: $BackupDestination\backup_log.txt
"@

$summary | Out-File -FilePath "$BackupDestination\backup_summary.txt" -Encoding UTF8

Write-Host ""
Write-Host "Profile backup completed!" -ForegroundColor Green
Write-Host "Backed up $backedUpCount user profiles" -ForegroundColor Green
Write-Host "Backup location: $BackupDestination" -ForegroundColor Gray
Write-Host "Summary file: $BackupDestination\backup_summary.txt" -ForegroundColor Gray
Write-Host ""
'@

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param([string]$ScriptContent)

        Write-Host "📝 Creating profile backup script..." -ForegroundColor Yellow

        # Save the script to the target computer
        $scriptPath = "C:\Files\ProfileBackup.ps1"

        try {
            # Create the Files directory if it doesn't exist
            if (-not (Test-Path "C:\Files")) {
                New-Item -Path "C:\Files" -ItemType Directory -Force | Out-Null
            }

            # Write the script content to file
            $ScriptContent | Out-File -FilePath $scriptPath -Encoding UTF8 -Force

            Write-Host "✅ Profile backup script created successfully at: $scriptPath" -ForegroundColor Green

            # Create a desktop shortcut for easy access
            $desktopPath = [Environment]::GetFolderPath("CommonDesktopDirectory")
            $shortcutPath = Join-Path $desktopPath "Profile Backup Tool.lnk"

            try {
                $WshShell = New-Object -ComObject WScript.Shell
                $Shortcut = $WshShell.CreateShortcut($shortcutPath)
                $Shortcut.TargetPath = "powershell.exe"
                $Shortcut.Arguments = "-ExecutionPolicy Bypass -File `"$scriptPath`""
                $Shortcut.WorkingDirectory = "C:\Files"
                $Shortcut.Description = "Profile Backup Tool - Created by Migration Wizard"
                $Shortcut.IconLocation = "shell32.dll,4"
                $Shortcut.Save()

                Write-Host "✅ Desktop shortcut created: $shortcutPath" -ForegroundColor Green
            } catch {
                Write-Host "⚠️ Warning: Could not create desktop shortcut - $_" -ForegroundColor Yellow
            }

            # Create a batch file for easy execution
            $batchPath = "C:\Files\RunProfileBackup.bat"
            $batchContent = "@echo off" + "`n"
            $batchContent += "echo Profile Backup Tool" + "`n"
            $batchContent += "echo ===================" + "`n"
            $batchContent += "echo." + "`n"
            $batchContent += "echo This tool will backup user profiles from this computer." + "`n"
            $batchContent += "echo." + "`n"
            $batchContent += "pause" + "`n"
            $batchContent += "echo." + "`n"
            $batchContent += "echo Starting profile backup..." + "`n"
            $batchContent += "powershell.exe -ExecutionPolicy Bypass -File `"C:\Files\ProfileBackup.ps1`"" + "`n"
            $batchContent += "echo." + "`n"
            $batchContent += "echo Profile backup completed!" + "`n"
            $batchContent += "pause" + "`n"
            $batchContent | Out-File -FilePath $batchPath -Encoding ASCII -Force
            Write-Host "✅ Batch file created: $batchPath" -ForegroundColor Green

            return "✅ Success: Profile backup script and tools created successfully"

        } catch {
            Write-Host "❌ Error creating profile backup script: $_" -ForegroundColor Red
            return "❌ Error: Failed to create profile backup script - $_"
        }
    } -ArgumentList $profileBackupScript

    Write-Color $result "Gray"
    Write-Color "📋 Profile backup script has been created on the target computer" "Green"
    Write-Color "   • Script location: C:\Files\ProfileBackup.ps1" "Gray"
    Write-Color "   • Desktop shortcut: Profile Backup Tool.lnk" "Gray"
    Write-Color "   • Batch file: C:\Files\RunProfileBackup.bat" "Gray"
    Write-Color "   • Users can run this anytime to backup profiles" "Gray"
}

function Test-PCOnlineAndDNS {
    param([string]$computerName)

    try {
        Test-Connection -ComputerName $computerName -Count 1 -ErrorAction Stop | Out-Null
        $dnsStatus = if ($computerName -match '^\d+\.\d+\.\d+\.\d+$') { "IP Address" } else { "DNS Resolved" }
        return @{ Online = $true; DNSStatus = $dnsStatus; Error = $null }
    }
    catch {
        return @{ Online = $false; DNSStatus = "Failed"; Error = $_.Exception.Message }
    }
}

function Set-ADConfiguration {
    param([string]$SourceComputer, [string]$TargetComputer)

    Write-Color "Configuring Active Directory settings..." "Cyan"

    try {
        Import-Module ActiveDirectory -ErrorAction Stop

        # Get source computer groups
        $source = $SourceComputer.Trim()
        $sourceAD = if ($source[-1] -ne '$') { "$source$" } else { $source }

        $sourceGroups = @()
        if (Get-ADComputer -Identity $sourceAD -ErrorAction SilentlyContinue) {
            $sourceGroups = Get-ADComputer -Identity $sourceAD -Properties MemberOf |
                Select-Object -ExpandProperty MemberOf |
                ForEach-Object { (Get-ADGroup $_).Name }

            Write-Color "📋 Source computer AD groups:" "Green"
            $sourceGroups | ForEach-Object { Write-Color "  • $_" "Gray" }
        }

        # Configure target computer
        $destination = $TargetComputer.Trim()
        $destinationAD = if ($destination[-1] -ne '$') { "$destination$" } else { $destination }

        if (Get-ADComputer -Identity $destinationAD -ErrorAction SilentlyContinue) {
            Write-Color "🔧 Configuring target computer AD groups..." "Cyan"

            # Copy groups from source to target
            foreach ($group in $sourceGroups) {
                try {
                    Add-ADGroupMember -Identity $group -Members $destinationAD -ErrorAction SilentlyContinue
                    Write-Color "  ✅ Added to group: $group" "Green"
                } catch {
                    Write-Color "  ⚠️ Could not add to group $group`: $_" "Yellow"
                }
            }
        }

        return $sourceGroups
    }
    catch {
        Write-Color "❌ Error configuring AD: $_" "Red"
        return @()
    }
}

function Install-PrinterMigration {
    param([string]$SourceComputer, [string]$TargetComputer)

    Write-Color "Starting printer migration..." "Cyan"

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($SourceComputer)

        Write-Host "🖨️ Starting Printer Migration Process"

        # Create backup directory
        $BackupPath = "C:\Files\user_profile_backups\"
        if (-not (Test-Path $BackupPath)) {
            New-Item -Path $BackupPath -ItemType Directory -Force | Out-Null
        }

        # Export printers from source computer
        Write-Host "📤 Exporting printers from source computer..." -ForegroundColor Yellow
        try {
            $printers = Get-WmiObject -Class Win32_Printer -ComputerName $SourceComputer | Where-Object { $_.Network -eq $true }

            if ($printers.Count -gt 0) {
                Write-Host "📋 Found $($printers.Count) network printers on source computer:" -ForegroundColor Green
                foreach ($printer in $printers) {
                    Write-Host "  • $($printer.Name)" -ForegroundColor Gray
                }

                # Import printers to target computer
                Write-Host "📥 Installing printers on target computer..." -ForegroundColor Yellow
                foreach ($printer in $printers) {
                    try {
                        # Add network printer
                        $null = (New-Object -ComObject WScript.Network).AddWindowsPrinterConnection($printer.Name)
                        Write-Host "  ✅ Added printer: $($printer.Name)" -ForegroundColor Green
                    } catch {
                        Write-Host "  ❌ Failed to add printer $($printer.Name): $_" -ForegroundColor Red
                    }
                }

                # Set default printer if one was set on source
                $defaultPrinter = Get-WmiObject -Class Win32_Printer -ComputerName $SourceComputer | Where-Object { $_.Default -eq $true }
                if ($defaultPrinter) {
                    try {
                        $null = (New-Object -ComObject WScript.Network).SetDefaultPrinter($defaultPrinter.Name)
                        Write-Host "  🎯 Set default printer: $($defaultPrinter.Name)" -ForegroundColor Green
                    } catch {
                        Write-Host "  ⚠️ Could not set default printer: $_" -ForegroundColor Yellow
                    }
                }

                return "✅ Success: Printer migration completed - $($printers.Count) printers migrated"
            } else {
                Write-Host "ℹ️ No network printers found on source computer" -ForegroundColor Gray
                return "ℹ️ Info: No network printers found to migrate"
            }
        } catch {
            Write-Host "❌ Error during printer migration: $_" -ForegroundColor Red
            return "❌ Error: Printer migration failed - $_"
        }
    } -ArgumentList $SourceComputer

    Write-Color $result "Gray"
}

# Main script execution function
function Start-MainScript {
    # Initialize credentials
    if (-not (Initialize-Credentials)) {
        Write-Color "Cannot proceed without valid credentials" "Red"
        return
    }

    # Display enhanced welcome banner from version 2
    Show-Banner "🚀 COMPUTER MIGRATION WIZARD v4.0 🚀" "Cyan"
    Write-Host ""
    Write-Host "  ╔══════════════════════════════════════════════════════════════╗" -ForegroundColor "Blue"
    Write-Host "  ║                                                              ║" -ForegroundColor "Blue"
    Write-Host "  ║    🖥️  Welcome to the Computer Migration Wizard! 🖥️         ║" -ForegroundColor "Green"
    Write-Host "  ║                                                              ║" -ForegroundColor "Blue"
    Write-Host "  ║    ✨ Seamlessly transfer your computer setup with ease     ║" -ForegroundColor "Yellow"
    Write-Host "  ║    🔧 Automated installation and configuration              ║" -ForegroundColor "Yellow"
    Write-Host "  ║    📁 Smart profile and data migration                      ║" -ForegroundColor "Yellow"
    Write-Host "  ║    🛡️  Enhanced security and reliability                    ║" -ForegroundColor "Yellow"
    Write-Host "  ║                                                              ║" -ForegroundColor "Blue"
    Write-Host "  ║    Developed by: The greatest technician that ever lived     ║" -ForegroundColor "Magenta"
    Write-Host "  ║                                                              ║" -ForegroundColor "Blue"
    Write-Host "  ╚══════════════════════════════════════════════════════════════╝" -ForegroundColor "Blue"
    Write-Host ""

    # Configuration Options
    Show-Banner "*** CONFIGURATION OPTIONS ***" "Yellow"

    # Local installer option (ask first)
    Write-Color "Do you want to use local installer files (D:\) instead of network paths? (Y/N): " "Yellow" -NoNewline
    $localInstallerInput = Read-Host
    $script:useLocalInstallers = $localInstallerInput -match '^(Y|y)$'

    if ($script:useLocalInstallers) {
        Write-Color "✅ Local installer mode enabled - using D:\ paths on target computer" "Green"
        Write-Color "ℹ️ Note: This will check for files on D:\ of the target computer (copytopc)" "Gray"
    } else {
        Write-Color "🌐 Network installer mode enabled - using network paths" "Green"
    }
    Write-Host ""

    # Auto-yes option (ask second)
    Write-Color "Do you want to automatically answer 'Yes' to all prompts? (Y/N): " "Yellow" -NoNewline
    $autoYesInput = Read-Host
    $script:autoYes = $autoYesInput -match '^(Y|y)$'
    if ($script:autoYes) {
        Write-Color "⚡ Auto-yes mode enabled - all prompts will be automatically approved" "Green"
    } else {
        Write-Color "👤 Manual mode enabled - you will be prompted for each option" "Yellow"
    }
    Write-Host ""

    # Get device IDs with connectivity validation loop
    do {
        $copyFromDeviceId = Read-Host -Prompt "`nEnter (copy from) Hostname or IP"
        $copyToDeviceId = Read-Host "Enter the (copy to) device ID"

        # Test connectivity
        Show-Banner "=== CONNECTIVITY TEST ===" "Yellow"
        Show-Progress "Testing connectivity to target computers" 1 6 "Yellow"

        Write-Color "🔍 Testing connectivity to source computer: $copyFromDeviceId" "Cyan"
        $result = Test-PCOnlineAndDNS -computerName $copyFromDeviceId

        Write-Color "🔍 Testing connectivity to target computer: $copyToDeviceId" "Cyan"
        $result2 = Test-PCOnlineAndDNS -computerName $copyToDeviceId

        $allOnline = $result.Online -and $result2.Online

        if ($allOnline) {
            Write-Color "✅ $copyFromDeviceId is online. DNS Status: $($result.DNSStatus)" "Green"
            Write-Color "✅ $copyToDeviceId is online. DNS Status: $($result2.DNSStatus)" "Green"
            Write-Host ""
            break
        } else {
            Write-Host ""
            Write-Color "=== CONNECTIVITY ISSUES DETECTED ===" "Red"

            if (-not $result.Online) {
                Write-Color "❌ SOURCE COMPUTER OFFLINE: $copyFromDeviceId" "Red"
                Write-Color "   Error: $($result.Error)" "Yellow"
            } else {
                Write-Color "✅ Source computer online: $copyFromDeviceId" "Green"
            }

            if (-not $result2.Online) {
                Write-Color "❌ TARGET COMPUTER OFFLINE: $copyToDeviceId" "Red"
                Write-Color "   Error: $($result2.Error)" "Yellow"
            } else {
                Write-Color "✅ Target computer online: $copyToDeviceId" "Green"
            }

            Write-Host ""
            Write-Color "Please check the following:" "Yellow"
            Write-Color "• Verify computer names are spelled correctly" "Gray"
            Write-Color "• Ensure computers are powered on and connected to network" "Gray"
            Write-Color "• Check if computers are accessible from this location" "Gray"
            Write-Color "• Try using IP addresses instead of hostnames if DNS issues" "Gray"
            Write-Host ""

            $retry = Read-Host "Do you want to re-enter computer names? (Y/N)"
            if ($retry -notmatch '^(Y|y)$') {
                Write-Color "Exiting script due to connectivity issues." "Red"
                return
            }
            Write-Host ""
        }
    } while (-not $allOnline)

    # Initialize PSRemoting to target computer
    Show-Banner "=== PSREMOTING INITIALIZATION ===" "Blue"
    Show-Progress "Establishing persistent connection to target computer" 2 6 "Yellow"

    if (-not (Initialize-PSRemoting -TargetComputer $copyToDeviceId)) {
        Write-Color "Failed to establish PSRemoting connection. Cannot continue." "Red"
        return
    }

    Write-Color "🎯 Migration setup complete! Starting migration process..." "Green"
    Write-Host ""

    # Get AD configuration and source groups
    Show-Banner "=== ACTIVE DIRECTORY CONFIGURATION ===" "Blue"
    Show-Progress "Configuring Active Directory settings" 3 6 "Yellow"
    $sourceGroups = Set-ADConfiguration -SourceComputer $copyFromDeviceId -TargetComputer $copyToDeviceId

    # File copying phase
    Show-Banner "=== FILE COPYING PHASE ===" "Blue"
    Show-Progress "Copying installer files to target computer" 4 6 "Yellow"
    Copy-FilesToTarget -TargetComputer $copyToDeviceId

    # Installation phase
    Show-Banner "=== INSTALLATION PHASE ===" "Blue"
    Show-Progress "Installing applications and configuring system" 5 6 "Yellow"

    # Install Lexmark driver first (before printer migration)
    Install-LexmarkDriver -TargetComputer $copyToDeviceId

    # Install system configuration (Java, Chrome, Citrix, Nuance)
    Install-SystemConfiguration -TargetComputer $copyToDeviceId

    # Install Office 365 (separate section, only for Type 1)
    Install-OfficeApplication -TargetComputer $copyToDeviceId -SourceGroups $sourceGroups

    # Install Imprivata (separate section)
    $installType1 = $sourceGroups | Where-Object { $_ -like "*type1*"}
    $installType2 = $sourceGroups | Where-Object { $_ -like "*type2*"}

    if ($installType1 -or $installType2) {
        $agentType = if ($installType1) { 1 } else { 2 }
        Install-ImprivataAgent -TargetComputer $copyToDeviceId -AgentType $agentType -SourceComputer $copyFromDeviceId
    }

    # Install VPN (separate section) - only if source computer name ends with 'L'
    if ($copyFromDeviceId -like "*L") {
        Install-VPNClient -TargetComputer $copyToDeviceId
    } else {
        Write-Color "⏭️ VPN installation skipped - source computer name does not end with 'L'" "Yellow"
    }

    # Install Dell Command Update (before computer renaming)
    Install-DellCommandUpdate -TargetComputer $copyToDeviceId

    # Profile backup phase
    Show-Banner "=== PROFILE BACKUP PHASE ===" "Blue"
    Show-Progress "Backing up user profiles and data" 6 6 "Yellow"

    if ($installType2) {
        # Type 2 - limited backup only
        Backup-PublicDesktopAndVBS -SourceComputer $copyFromDeviceId -TargetComputer $copyToDeviceId
    } else {
        # Full profile backup for Type 1 or non-Imprivata computers
        if (-not $script:autoYes) {
            $backupChoice = Read-Host "Do you want to backup user profiles? (Y/N)"
            $doBackup = $backupChoice -match '^(Y|y)$'
        } else {
            $doBackup = $true
        }

        if ($doBackup) {
            Backup-UserProfiles -SourceComputer $copyFromDeviceId -TargetComputer $copyToDeviceId
        } else {
            Write-Color "⏭️ User profile backup skipped by user choice" "Yellow"
        }
    }

    # Printer migration
    Install-PrinterMigration -SourceComputer $copyFromDeviceId -TargetComputer $copyToDeviceId

    # Create profile backup script (one of the most important chunks of code)
    Write-Color "📝 Creating profile backup script for future use..." "Cyan"
    Create-ProfileBackupScript -TargetComputer $copyToDeviceId

    # System configuration scripts
    Write-Color "🔧 Running system configuration scripts..." "Cyan"
    $configResult = Invoke-RemoteCommand -TargetComputer $copyToDeviceId -UseFallback -ScriptBlock {
        # Photo Viewer Registry Fix
        Write-Host "�️ Applying Photo Viewer registry fix..." -ForegroundColor Yellow
        if (Test-Path "C:\Files\Win_Photo_Viewer.reg") {
            Start-Process -FilePath "regedit.exe" -ArgumentList "/s", "C:\Files\Win_Photo_Viewer.reg" -Wait -NoNewWindow
            Write-Host "✅ Photo Viewer registry fix applied" -ForegroundColor Green
        }

        # Volume Script
        Write-Host "🔊 Running Volume configuration script..." -ForegroundColor Yellow
        if (Test-Path "C:\Files\Volume.ps1") {
            PowerShell.exe -ExecutionPolicy Bypass -File "C:\Files\Volume.ps1"
            Write-Host "✅ Volume script executed" -ForegroundColor Green
        }

        # BitLocker Script
        Write-Host "🔐 Running BitLocker AD configuration script..." -ForegroundColor Yellow
        if (Test-Path "C:\Files\BitLockerAD.ps1") {
            PowerShell.exe -ExecutionPolicy Bypass -File "C:\Files\BitLockerAD.ps1"
            Write-Host "✅ BitLocker AD script executed" -ForegroundColor Green
        }

        return "✅ Success: System configuration scripts completed"
    }
    Write-Color $configResult "Gray"

    # Computer renaming (final step)
    Write-Color "🏷️ Computer renaming..." "Cyan"
    $renameResult = Invoke-RemoteCommand -TargetComputer $copyToDeviceId -UseFallback -ScriptBlock {
        param($NewName)

        try {
            # Get current computer name
            $currentName = $env:COMPUTERNAME
            Write-Host "Current computer name: $currentName" -ForegroundColor Gray
            Write-Host "New computer name: $NewName" -ForegroundColor Green

            # Rename computer without requiring domain admin credentials
            Rename-Computer -NewName $NewName -Force -PassThru
            Write-Host "✅ Computer renamed successfully to: $NewName" -ForegroundColor Green
            Write-Host "⚠️ A restart will be required to complete the name change" -ForegroundColor Yellow

            return "✅ Success: Computer renamed to $NewName"
        } catch {
            Write-Host "❌ Error renaming computer: $_" -ForegroundColor Red
            return "❌ Error: Computer rename failed - $_"
        }
    } -ArgumentList $copyFromDeviceId
    Write-Color $renameResult "Gray"

    # Migration completion
    Show-Banner "=== MIGRATION COMPLETED ===" "Green"
    Show-CompletionAnimation

    Write-Color "🎉 Computer migration has been completed successfully!" "Green"
    Write-Color "📋 Migration Summary:" "Cyan"
    Write-Color "  • Source Computer: $copyFromDeviceId" "Gray"
    Write-Color "  • Target Computer: $copyToDeviceId" "Gray"
    Write-Color "  • Installer Mode: $(if ($script:useLocalInstallers) { 'Local (D:\)' } else { 'Network' })" "Gray"
    Write-Color "  • Auto-Yes Mode: $(if ($script:autoYes) { 'Enabled' } else { 'Disabled' })" "Gray"
    Write-Color "  • Imprivata Type: $(if ($installType1) { 'Type 1' } elseif ($installType2) { 'Type 2' } else { 'None' })" "Gray"
    Write-Color "  • VPN Installed: $(if ($copyFromDeviceId -like '*L') { 'Yes' } else { 'No' })" "Gray"
    Write-Host ""

    Write-Color "⚠️ IMPORTANT NEXT STEPS:" "Yellow"
    Write-Color "1. The target computer needs to be restarted to complete the computer name change" "Yellow"
    Write-Color "2. Verify all applications are working correctly after restart" "Yellow"
    Write-Color "3. Test printer functionality and network connectivity" "Yellow"
    Write-Color "4. Confirm user profiles and data have been migrated successfully" "Yellow"
    Write-Host ""

    # Clean up PSSession
    if ($null -ne $script:targetSession) {
        try {
            Remove-PSSession $script:targetSession
            Write-Color "✅ PSSession cleaned up successfully" "Gray"
        }
        catch {
            Write-Color "⚠️ Warning: Could not clean up PSSession - $_" "Yellow"
        }
        $script:targetSession = $null
    }

    # Final completion message
    Write-Color "🏁 Migration process completed! Thank you for using the Computer Migration Wizard." "Green"
    Write-Host ""

    # Keep restart prompt
    $restart = Read-Host "Press Enter to restart the script, or type 'exit' to quit"
    if ($restart -ne 'exit') {
        Clear-Host
        Remove-Variable * -ErrorAction SilentlyContinue
        & $PSCommandPath
    } else {
        Write-Color "👋 Goodbye! Have a great day!" "Cyan"
    }
}

# Global script variables
$script:useLocalInstallers = $false
$script:autoYes = $false

# Initial script execution
Start-MainScript
