@echo off
setlocal enabledelayedexpansion

echo ********************************************************************************
echo *                    COPY INSTALLER FILES FROM USB TO D:\                    *
echo ********************************************************************************
echo.
echo This script copies installer files from a USB drive to D:\ for local installation
echo.

:: Detect USB drives
echo Detecting USB drives...
echo.

set count=0
for /f "tokens=1,2,3" %%a in ('wmic logicaldisk where "drivetype=2" get deviceid^,volumename^,size /format:csv ^| findstr /v "Node"') do (
    if not "%%b"=="" (
        set /a count+=1
        set drive!count!=%%b
        set label!count!=%%c
        set size!count!=%%d
        echo [!count!] %%b - "%%c"
    )
)

if %count%==0 (
    echo ERROR: No USB drives found!
    echo Please connect a USB drive with installer files.
    pause
    exit /b 1
)

echo.
echo [0] Exit
echo.

:: Get user selection
:select_drive
set /p choice="Please select a USB drive (1-%count% or 0 to exit): "

if "%choice%"=="0" (
    echo Operation cancelled.
    pause
    exit /b 0
)

if %choice% LSS 1 goto invalid_choice
if %choice% GTR %count% goto invalid_choice

set selected_drive=!drive%choice%!
echo.
echo Selected USB drive: %selected_drive%

:: Check if D: drive exists
if not exist D:\ (
    echo ERROR: D:\ drive not found!
    echo Please ensure D:\ drive is available on this computer.
    pause
    exit /b 1
)

:: Confirm operation
echo.
set /p confirm="Copy all installer files from %selected_drive% to D:\? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Starting copy operation from %selected_drive% to D:\...
echo.

:: Copy all files and folders from USB to D:\
echo Copying all installer files...
robocopy "%selected_drive%" "D:\" /E /MT:8 /R:3 /W:10

echo.
echo ********************************************************************************
echo *                           COPY OPERATION COMPLETE                           *
echo ********************************************************************************
echo.
echo All installer files have been copied from %selected_drive% to D:\
echo.
echo NEXT STEPS:
echo 1. Run the migration script on this computer
echo 2. Select "Yes" when asked about using local installer files
echo 3. The migration script will now find files on D:\ drive
echo.
pause
goto :eof

:invalid_choice
echo Invalid selection. Please try again.
goto select_drive
