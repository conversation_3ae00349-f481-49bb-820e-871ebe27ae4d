# ===============================================================================
# INSTALLER FILES COPY UTILITY - PowerShell Version
# This script copies all required installer files from network shares to
# a selected USB or local drive for use with the migration script
# ===============================================================================

# Set execution policy for current session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Function to display colored text
function Write-Color {
    param(
        [string]$Text,
        [string]$Color = "White",
        [switch]$NoNewline
    )
    if ($NoNewline) {
        Write-Host $Text -ForegroundColor $Color -NoNewline
    } else {
        Write-Host $Text -ForegroundColor $Color
    }
}

# Function to display banner
function Show-Banner {
    param([string]$Text, [string]$Color = "Yellow")
    Write-Host ""
    Write-Color "********************************************************************************" $Color
    $padding = [math]::Max(0, (78 - $Text.Length) / 2)
    $leftPad = ' ' * [math]::Floor($padding)
    $rightPad = ' ' * [math]::Ceiling($padding)
    Write-Color "*$leftPad$Text$rightPad*" $Color
    Write-Color "********************************************************************************" $Color
    Write-Host ""
}

# Function to detect and display available drives
function Get-AvailableDrives {
    Write-Color "Detecting available drives..." "Cyan"
    Write-Host ""

    $drives = @()
    $driveCount = 0

    # Get all logical drives
    $allDrives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -ne "C:" }

    Write-Color "REMOVABLE DRIVES (USB/External):" "Yellow"
    $removableDrives = $allDrives | Where-Object { $_.DriveType -eq 2 }

    foreach ($drive in $removableDrives) {
        $driveCount++
        $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
        $volumeLabel = if ($drive.VolumeName) { "`"$($drive.VolumeName)`"" } else { "(No Label)" }

        Write-Color "[$driveCount] $($drive.DeviceID) $volumeLabel (USB/Removable) - $freeSpaceGB GB free / $totalSpaceGB GB total" "White"

        $drives += @{
            Number = $driveCount
            DeviceID = $drive.DeviceID
            VolumeName = $drive.VolumeName
            FreeSpace = $drive.FreeSpace
            Size = $drive.Size
            Type = "USB/Removable"
        }
    }

    Write-Host ""
    Write-Color "FIXED DRIVES (Local Hard Drives):" "Yellow"
    $fixedDrives = $allDrives | Where-Object { $_.DriveType -eq 3 }

    foreach ($drive in $fixedDrives) {
        $driveCount++
        $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
        $volumeLabel = if ($drive.VolumeName) { "`"$($drive.VolumeName)`"" } else { "(No Label)" }

        Write-Color "[$driveCount] $($drive.DeviceID) $volumeLabel (Fixed Drive) - $freeSpaceGB GB free / $totalSpaceGB GB total" "White"

        $drives += @{
            Number = $driveCount
            DeviceID = $drive.DeviceID
            VolumeName = $drive.VolumeName
            FreeSpace = $drive.FreeSpace
            Size = $drive.Size
            Type = "Fixed Drive"
        }
    }

    if ($driveCount -eq 0) {
        Write-Color "ERROR: No suitable drives found!" "Red"
        Write-Color "Please connect a USB drive or ensure other drives are available." "Red"
        Read-Host "Press Enter to exit"
        exit 1
    }

    Write-Host ""
    Write-Color "[0] Exit" "Gray"
    Write-Host ""

    return $drives
}

# Function to select drive
function Select-Drive {
    param([array]$Drives)

    do {
        $choice = Read-Host "Please select a drive (1-$($Drives.Count) or 0 to exit)"

        if ($choice -eq "0") {
            Write-Color "Operation cancelled by user." "Yellow"
            exit 0
        }

        $selectedDrive = $Drives | Where-Object { $_.Number -eq [int]$choice }

        if (-not $selectedDrive) {
            Write-Color "Invalid selection. Please try again." "Red"
        }
    } while (-not $selectedDrive)

    return $selectedDrive
}

# Function to check drive space
function Test-DriveSpace {
    param([hashtable]$Drive)

    $requiredSpace = 25GB

    Write-Color "Checking available space on $($Drive.DeviceID)..." "Cyan"

    if ($Drive.FreeSpace -lt $requiredSpace) {
        $freeSpaceGB = [math]::Round($Drive.FreeSpace / 1GB, 2)
        Write-Color "WARNING: $($Drive.DeviceID) may not have enough free space!" "Yellow"
        Write-Color "Available: $freeSpaceGB GB" "Yellow"
        Write-Color "Recommended: At least 25 GB free space" "Yellow"
        Write-Host ""

        $continue = Read-Host "Do you want to continue anyway? (Y/N)"
        if ($continue -notmatch '^(Y|y)$') {
            Write-Color "Operation cancelled by user." "Yellow"
            exit 0
        }
    }

    Write-Color "Drive space check passed." "Green"
}

# Function to test network connectivity
function Test-NetworkConnectivity {
    Write-Color "Testing network connectivity..." "Cyan"

    $servers = @("bay-msfsnas01", "storagehd")
    $allConnected = $true

    foreach ($server in $servers) {
        try {
            $result = Test-Connection -ComputerName $server -Count 1 -Quiet -ErrorAction Stop
            if ($result) {
                Write-Color "✓ $server is reachable" "Green"
            } else {
                Write-Color "✗ $server is not reachable" "Red"
                $allConnected = $false
            }
        }
        catch {
            Write-Color "✗ $server is not reachable - $($_.Exception.Message)" "Red"
            $allConnected = $false
        }
    }

    if (-not $allConnected) {
        Write-Color "WARNING: Some network servers are not reachable. Copy operations may fail." "Yellow"
        $continue = Read-Host "Do you want to continue anyway? (Y/N)"
        if ($continue -notmatch '^(Y|y)$') {
            Write-Color "Operation cancelled by user." "Yellow"
            exit 0
        }
    }

    Write-Host ""
}

# Function to copy files with progress
function Copy-InstallerFiles {
    param([string]$TargetDrive)

    # Define source and destination paths
    $copyTasks = @(
        @{ Name = "Splashtop"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\Splashtop_Push"; Dest = "$TargetDrive\Splashtop_Push"; IsFolder = $true },
        @{ Name = ".NET 3.5 SXS"; Source = "\\bay-msfsnas01\data\FS\PUBLIC\installdotnet3_5\Win11\sxs"; Dest = "$TargetDrive\sxs"; IsFolder = $true },
        @{ Name = "Lexmark Driver"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\source\Lexmark\Lexmark_Universal_v2_UD1_PostScript_3_Emulation"; Dest = "$TargetDrive\Lexmark_Universal_v2_UD1_PostScript_3_Emulation"; IsFolder = $true },
        @{ Name = "Office 365"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\o365"; Dest = "$TargetDrive\o365"; IsFolder = $true },
        @{ Name = "Citrix 4.9 LTSR2"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\4.9_LTSR2"; Dest = "$TargetDrive\4.9_LTSR2"; IsFolder = $true },
        @{ Name = "Nuance"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\CustomCitrix\4.9_LTSR2\Nuance"; Dest = "$TargetDrive\Nuance121"; IsFolder = $true },
        @{ Name = "FSTools"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\FSTools"; Dest = "$TargetDrive\FSTools"; IsFolder = $true },
        @{ Name = "Lexmark GDI"; Source = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\LexmarkGDI"; Dest = "$TargetDrive\LexmarkGDI"; IsFolder = $true },
        @{ Name = "Imprivata Push"; Source = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Imprivata_push"; Dest = "$TargetDrive\Imprivata_push"; IsFolder = $true },
        @{ Name = "Java RE 7"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\WSP\7\jre1.7.0_45.msi"; Dest = "$TargetDrive\jre1.7.0_45.msi"; IsFolder = $false },
        @{ Name = "Google Chrome"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\Google\GoogleChromeStandaloneEnterprise64.msi"; Dest = "$TargetDrive\GoogleChromeStandaloneEnterprise64.msi"; IsFolder = $false },
        @{ Name = "Photo Viewer Registry"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Win_Photo_Viewer.reg"; Dest = "$TargetDrive\Win_Photo_Viewer.reg"; IsFolder = $false },
        @{ Name = "Volume Script"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Volume.ps1"; Dest = "$TargetDrive\Volume.ps1"; IsFolder = $false },
        @{ Name = "BitLocker Script"; Source = "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\BitLockerAD.ps1"; Dest = "$TargetDrive\BitLockerAD.ps1"; IsFolder = $false },
        @{ Name = "Dell Command Update"; Source = "\\bay-msfsnas01\data\FS\Source\Commandupdate.EXE"; Dest = "$TargetDrive\Commandupdate.EXE"; IsFolder = $false }
    )

    Write-Color "Starting copy operations to $TargetDrive..." "Cyan"
    Write-Host ""

    # Create base directories
    Write-Color "[1/16] Creating directory structure on $TargetDrive..." "Yellow"
    foreach ($task in $copyTasks) {
        if ($task.IsFolder) {
            $null = New-Item -Path $task.Dest -ItemType Directory -Force -ErrorAction SilentlyContinue
        }
    }
    Write-Color "Directory structure created successfully." "Green"
    Write-Host ""

    # Copy files
    $taskNumber = 2
    foreach ($task in $copyTasks) {
        Write-Color "[$taskNumber/16] Copying $($task.Name)..." "Yellow"

        try {
            if ($task.IsFolder) {
                # Check if source exists first
                if (-not (Test-Path $task.Source)) {
                    Write-Color "ERROR: Source path not found: $($task.Source)" "Red"
                    continue
                }

                # Use robocopy for folders with better error handling
                $robocopyArgs = @(
                    "`"$($task.Source)`"",
                    "`"$($task.Dest)`"",
                    "/E",           # Copy subdirectories including empty ones
                    "/R:5",         # Retry 5 times on failed copies
                    "/W:30",        # Wait 30 seconds between retries
                    "/MT:4",        # Use 4 threads (reduced from 8 for stability)
                    "/NP",          # No progress
                    "/NDL",         # No directory list
                    "/NJH",         # No job header
                    "/NJS",         # No job summary
                    "/COPY:DAT",    # Copy Data, Attributes, and Timestamps
                    "/DCOPY:DAT",   # Copy directory Data, Attributes, and Timestamps
                    "/XO"           # Exclude older files
                )

                Write-Color "  Source: $($task.Source)" "Gray"
                Write-Color "  Destination: $($task.Dest)" "Gray"

                $result = Start-Process -FilePath "robocopy" -ArgumentList $robocopyArgs -Wait -PassThru -NoNewWindow

                # Robocopy exit codes: 0-7 are success, 8+ are errors
                switch ($result.ExitCode) {
                    0 { Write-Color "$($task.Name) copy completed successfully (No files copied - already up to date)" "Green" }
                    1 { Write-Color "$($task.Name) copy completed successfully (Files copied)" "Green" }
                    2 { Write-Color "$($task.Name) copy completed successfully (Extra files/directories detected)" "Green" }
                    3 { Write-Color "$($task.Name) copy completed successfully (Files copied and extra files detected)" "Green" }
                    4 { Write-Color "$($task.Name) copy completed with warnings (Some mismatched files/directories)" "Yellow" }
                    5 { Write-Color "$($task.Name) copy completed with warnings (Files copied and some mismatched)" "Yellow" }
                    6 { Write-Color "$($task.Name) copy completed with warnings (Extra and mismatched files)" "Yellow" }
                    7 { Write-Color "$($task.Name) copy completed with warnings (Files copied, extra and mismatched files)" "Yellow" }
                    8 { Write-Color "ERROR: $($task.Name) copy failed - Some files or directories could not be copied" "Red" }
                    16 { Write-Color "ERROR: $($task.Name) copy failed - Serious error, no files copied. Check permissions and network connectivity" "Red" }
                    default { Write-Color "ERROR: $($task.Name) copy failed with exit code: $($result.ExitCode)" "Red" }
                }
            } else {
                # Check if source file exists first
                if (-not (Test-Path $task.Source)) {
                    Write-Color "ERROR: Source file not found: $($task.Source)" "Red"
                    continue
                }

                # Use Copy-Item for individual files
                Copy-Item -Path $task.Source -Destination $task.Dest -Force -ErrorAction Stop
                Write-Color "$($task.Name) copy completed successfully" "Green"
            }
        }
        catch {
            Write-Color "ERROR: Failed to copy $($task.Name) - $($_.Exception.Message)" "Red"
            Write-Color "  Source: $($task.Source)" "Gray"
            Write-Color "  Destination: $($task.Dest)" "Gray"
        }

        $taskNumber++
        Write-Host ""
    }
}

# Main script execution
Clear-Host

Show-Banner "INSTALLER FILES COPY UTILITY" "Magenta"

Write-Color "This utility will copy all required installer files from network shares" "Green"
Write-Color "to a selected USB or local drive for offline installation use." "Green"
Write-Host ""
Write-Color "Estimated total size: ~15-20 GB" "Yellow"
Write-Color "Estimated time: 30-60 minutes (depending on network speed)" "Yellow"
Write-Host ""

# Detect available drives
$availableDrives = Get-AvailableDrives

# Select drive
$selectedDrive = Select-Drive -Drives $availableDrives

Write-Host ""
Write-Color "Selected drive: $($selectedDrive.DeviceID)" "Green"

# Check drive space
Test-DriveSpace -Drive $selectedDrive

# Test network connectivity
Test-NetworkConnectivity

# Confirm operation
Write-Host ""
$confirm = Read-Host "Do you want to proceed with copying installer files to $($selectedDrive.DeviceID)? (Y/N)"
if ($confirm -notmatch '^(Y|y)$') {
    Write-Color "Operation cancelled by user." "Yellow"
    Read-Host "Press Enter to exit"
    exit 0
}

# Copy files
Copy-InstallerFiles -TargetDrive $selectedDrive.DeviceID

# Show completion message
Show-Banner "COPY OPERATION COMPLETE" "Green"

Write-Color "All installer files have been copied to $($selectedDrive.DeviceID)." "Green"
Write-Host ""
Write-Color "NEXT STEPS:" "Yellow"
Write-Color "1. If using USB drive, copy files from $($selectedDrive.DeviceID) to D:\ on target computer" "White"
Write-Color "2. Run the migration script and select 'Yes' for local installer files" "White"
Write-Color "3. Migration script will look for files on D:\ drive" "White"
Write-Host ""

Read-Host "Press Enter to exit"