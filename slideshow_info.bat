@ECHO OFF
COLOR 0f

REM Kill any existing slideshow processes at script start
taskkill /F /IM pptview.exe >NUL 2>&1

SET "NETWORK_FILE=\\bay-msfsnas01\data\MKT\InfoBoard\INFO.pptx"
SET "LOCAL_FILE=C:\files\INFO.pptx"
SET "LOCAL_ARCHIVE=C:\files\old_boards"

REM === Ensure local folders exist ===
IF NOT EXIST "C:\files\" (
    MKDIR "C:\files\"
)
IF NOT EXIST "%LOCAL_ARCHIVE%" (
    MKDIR "%LOCAL_ARCHIVE%"
)

:MAIN_LOOP

REM === CHECK NETWORK FILE EXISTS ===
IF NOT EXIST "%NETWORK_FILE%" (
    CLS
    ECHO Network file not found. Retrying in 1 hour...
    TIMEOUT /T 3600 >NUL
    GOTO MAIN_LOOP
)

REM === COPY IF LOCAL FILE MISSING ===
IF NOT EXIST "%LOCAL_FILE%" (
    ECHO Local file missing - copying from network and launching slideshow...
    COPY /Y "%NETWORK_FILE%" "%LOCAL_FILE%"
    GOTO LAUNCH_SLIDESHOW
)

REM === CHECK FILE TIMESTAMPS ===
REM Clear variables first
SET NETWORK_TICKS=
SET LOCAL_TICKS=

FOR /F "usebackq delims=" %%A IN (`powershell -command "try { (Get-Item '%NETWORK_FILE%').LastWriteTime.Ticks } catch { 'ERROR' }"`) DO SET NETWORK_TICKS=%%A
FOR /F "usebackq delims=" %%A IN (`powershell -command "try { (Get-Item '%LOCAL_FILE%').LastWriteTime.Ticks } catch { 'ERROR' }"`) DO SET LOCAL_TICKS=%%A

REM === Check if variables are properly set ===
IF "%NETWORK_TICKS%"=="" GOTO FILE_COMPARE_FALLBACK
IF "%LOCAL_TICKS%"=="" GOTO FILE_COMPARE_FALLBACK
IF "%NETWORK_TICKS%"=="ERROR" GOTO FILE_COMPARE_FALLBACK
IF "%LOCAL_TICKS%"=="ERROR" GOTO FILE_COMPARE_FALLBACK

REM === Use PowerShell for large number comparison ===
FOR /F %%A IN ('powershell -command "if ([int64]'%NETWORK_TICKS%' -gt [int64]'%LOCAL_TICKS%') { 'NEWER' } else { 'SAME' }"') DO SET COMPARISON_RESULT=%%A

IF "%COMPARISON_RESULT%"=="NEWER" (
    REM === ARCHIVE OLD LOCAL FILE TO NETWORK ARCHIVE ===
    FOR /F "tokens=2 delims== " %%I IN ('wmic os get localdatetime /value ^| find "="') DO SET dt=%%I
    SET "YYYY=%dt:~0,4%"
    SET "MM=%dt:~4,2%"
    SET "DD=%dt:~6,2%"
    SET "ARCHIVE_FILE=INFO_%MM%-%DD%.pptx"
    IF EXIST "%LOCAL_FILE%" (
        MOVE /Y "%LOCAL_FILE%" "%LOCAL_ARCHIVE%\%ARCHIVE_FILE%"
        IF ERRORLEVEL 1 (
            DEL /Q "%LOCAL_FILE%" 2>NUL
        )
    )
    REM === COPY NEW FILE FROM NETWORK AND LAUNCH ===
    TASKKILL /F /IM PPTVIEW.EXE >NUL 2>&1
    TASKKILL /F /IM POWERPNT.EXE >NUL 2>&1
    COPY /Y "%NETWORK_FILE%" "%LOCAL_FILE%"
    GOTO LAUNCH_SLIDESHOW
) ELSE (
    GOTO NO_UPDATE
)

:NO_UPDATE
REM === NO UPDATE - CHECK IF SLIDESHOW WINDOW IS RUNNING ===
powershell -command "if (Get-Process pptview -ErrorAction SilentlyContinue | Where-Object { $_.MainWindowHandle -ne 0 }) { exit 0 } else { exit 1 }"
IF ERRORLEVEL 1 (
    GOTO LAUNCH_SLIDESHOW
) ELSE (
    REM === COUNTDOWN TO NEXT CHECK ===
    FOR /L %%M IN (60,-1,1) DO (
        CLS
        ECHO        SLIDESHOW ACTIVE - NEXT CHECK IN %%M MINUTES
        TIMEOUT /T 60 >NUL
    )
)
GOTO MAIN_LOOP

:FILE_COMPARE_FALLBACK
FC /B "%NETWORK_FILE%" "%LOCAL_FILE%" >NUL 2>&1
IF ERRORLEVEL 1 (
    FOR /F "tokens=2 delims== " %%I IN ('wmic os get localdatetime /value ^| find "="') DO SET dt=%%I
    SET "YYYY=%dt:~0,4%"
    SET "MM=%dt:~4,2%"
    SET "DD=%dt:~6,2%"
    SET "ARCHIVE_FILE=INFO_%MM%-%DD%.pptx"
    IF EXIST "%LOCAL_FILE%" (
        MOVE /Y "%LOCAL_FILE%" "%LOCAL_ARCHIVE%\%ARCHIVE_FILE%"
        IF ERRORLEVEL 1 (
            DEL /Q "%LOCAL_FILE%" 2>NUL
        )
    )
    TASKKILL /F /IM PPTVIEW.EXE >NUL 2>&1
    TASKKILL /F /IM POWERPNT.EXE >NUL 2>&1
    COPY /Y "%NETWORK_FILE%" "%LOCAL_FILE%"
    GOTO LAUNCH_SLIDESHOW
) ELSE (
    GOTO MAIN_LOOP
)

:LAUNCH_SLIDESHOW
FOR /L %%S IN (20,-1,1) DO (
    CLS
    ECHO        WAITING TO LAUNCH SLIDESHOW IN %%S SECONDS
    PING -n 2 127.0.0.1 >NUL
)
CLS
ECHO        *** SLIDESHOW NOW STARTING ***
PING -n 2 127.0.0.1 >NUL

REM === LAUNCH WITH PPTVIEW ===
START "" "%LOCAL_FILE%"

REM === WAIT BEFORE NEXT CHECK ===
TIMEOUT /T 3600 >NUL
GOTO MAIN_LOOP
