# Enhanced PSSession Manager with Connection Pooling
# This provides better session management and persistence

# Global session pool
$script:SessionPool = @{}
$script:SessionLastUsed = @{}
$script:MaxIdleMinutes = 30
$script:MaxSessionsPerComputer = 3

function Initialize-SessionManager {
    param([PSCredential]$Credential)
    
    $script:storedCredentials = $Credential
    
    # Start background job to clean up idle sessions
    Start-Job -ScriptBlock {
        param($MaxIdleMinutes)
        
        while ($true) {
            Start-Sleep -Seconds 300 # Check every 5 minutes
            
            # This would need to be implemented with proper session tracking
            # For now, just a placeholder for the concept
        }
    } -ArgumentList $script:MaxIdleMinutes | Out-Null
}

function Get-PersistentSession {
    param(
        [string]$TargetComputer,
        [switch]$ForceNew
    )
    
    $computerKey = $TargetComputer.ToLower()
    
    # Check if we have a valid existing session
    if (-not $ForceNew -and $script:SessionPool.ContainsKey($computerKey)) {
        $existingSessions = $script:SessionPool[$computerKey]
        
        foreach ($session in $existingSessions) {
            if ($session.State -eq 'Opened') {
                try {
                    # Test the session
                    $testResult = Invoke-Command -Session $session -ScriptBlock { $env:COMPUTERNAME } -ErrorAction Stop
                    
                    # Update last used time
                    $script:SessionLastUsed[$session.Id] = Get-Date
                    
                    Write-Host "✓ Reusing existing session to $TargetComputer" -ForegroundColor Green
                    return $session
                }
                catch {
                    # Session is broken, remove it
                    Write-Host "⚠ Removing broken session to $TargetComputer" -ForegroundColor Yellow
                    Remove-PSSession $session -ErrorAction SilentlyContinue
                    $script:SessionPool[$computerKey] = $script:SessionPool[$computerKey] | Where-Object { $_.Id -ne $session.Id }
                }
            }
        }
    }
    
    # Create new session
    Write-Host "Creating new persistent session to $TargetComputer..." -ForegroundColor Cyan
    
    try {
        # Enhanced session options for persistence
        $sessionOption = New-PSSessionOption `
            -IdleTimeout ([int]::MaxValue) `
            -OpenTimeout 60000 `
            -OperationTimeout 600000 `
            -CancelTimeout 30000 `
            -MaximumReceivedDataSizePerCommand 500MB `
            -MaximumReceivedObjectSize 200MB
        
        $newSession = New-PSSession `
            -ComputerName $TargetComputer `
            -Credential $script:storedCredentials `
            -SessionOption $sessionOption `
            -ErrorAction Stop
        
        # Initialize the session pool for this computer if needed
        if (-not $script:SessionPool.ContainsKey($computerKey)) {
            $script:SessionPool[$computerKey] = @()
        }
        
        # Add to session pool
        $script:SessionPool[$computerKey] += $newSession
        $script:SessionLastUsed[$newSession.Id] = Get-Date
        
        # Limit sessions per computer
        if ($script:SessionPool[$computerKey].Count -gt $script:MaxSessionsPerComputer) {
            $oldestSession = $script:SessionPool[$computerKey] | Sort-Object { $script:SessionLastUsed[$_.Id] } | Select-Object -First 1
            Remove-PSSession $oldestSession -ErrorAction SilentlyContinue
            $script:SessionPool[$computerKey] = $script:SessionPool[$computerKey] | Where-Object { $_.Id -ne $oldestSession.Id }
            $script:SessionLastUsed.Remove($oldestSession.Id)
        }
        
        Write-Host "✓ New session created to $TargetComputer (ID: $($newSession.Id))" -ForegroundColor Green
        return $newSession
    }
    catch {
        Write-Host "✗ Failed to create session to $TargetComputer`: $_" -ForegroundColor Red
        return $null
    }
}

function Invoke-PersistentCommand {
    param(
        [string]$TargetComputer,
        [scriptblock]$ScriptBlock,
        [object[]]$ArgumentList = @(),
        [int]$MaxRetries = 2,
        [switch]$UseFallback
    )
    
    $attempt = 0
    
    while ($attempt -le $MaxRetries) {
        $attempt++
        
        # Get or create persistent session
        $session = Get-PersistentSession -TargetComputer $TargetComputer
        
        if ($null -eq $session) {
            if ($UseFallback) {
                Write-Host "Using direct connection fallback..." -ForegroundColor Yellow
                try {
                    if ($ArgumentList.Count -gt 0) {
                        return Invoke-Command -ComputerName $TargetComputer -Credential $script:storedCredentials -ScriptBlock $ScriptBlock -ArgumentList $ArgumentList -ErrorAction Stop
                    } else {
                        return Invoke-Command -ComputerName $TargetComputer -Credential $script:storedCredentials -ScriptBlock $ScriptBlock -ErrorAction Stop
                    }
                }
                catch {
                    Write-Host "Direct connection also failed: $_" -ForegroundColor Red
                    return "Error: Both session and direct connection failed"
                }
            }
            return "Error: Could not establish session"
        }
        
        try {
            # Execute command
            if ($ArgumentList.Count -gt 0) {
                $result = Invoke-Command -Session $session -ScriptBlock $ScriptBlock -ArgumentList $ArgumentList -ErrorAction Stop
            } else {
                $result = Invoke-Command -Session $session -ScriptBlock $ScriptBlock -ErrorAction Stop
            }
            
            # Update last used time
            $script:SessionLastUsed[$session.Id] = Get-Date
            
            return $result
        }
        catch {
            Write-Host "Command failed on attempt $attempt`: $_" -ForegroundColor Yellow
            
            # Remove the failed session from pool
            $computerKey = $TargetComputer.ToLower()
            if ($script:SessionPool.ContainsKey($computerKey)) {
                $script:SessionPool[$computerKey] = $script:SessionPool[$computerKey] | Where-Object { $_.Id -ne $session.Id }
                $script:SessionLastUsed.Remove($session.Id)
            }
            Remove-PSSession $session -ErrorAction SilentlyContinue
            
            if ($attempt -le $MaxRetries) {
                Write-Host "Retrying with new session..." -ForegroundColor Yellow
                Start-Sleep -Seconds 5
            }
        }
    }
    
    return "Error: Command failed after $MaxRetries attempts"
}

function Clear-SessionPool {
    param([string]$TargetComputer)
    
    if ($TargetComputer) {
        $computerKey = $TargetComputer.ToLower()
        if ($script:SessionPool.ContainsKey($computerKey)) {
            foreach ($session in $script:SessionPool[$computerKey]) {
                Remove-PSSession $session -ErrorAction SilentlyContinue
                $script:SessionLastUsed.Remove($session.Id)
            }
            $script:SessionPool.Remove($computerKey)
            Write-Host "✓ Cleared session pool for $TargetComputer" -ForegroundColor Green
        }
    } else {
        # Clear all sessions
        foreach ($computerSessions in $script:SessionPool.Values) {
            foreach ($session in $computerSessions) {
                Remove-PSSession $session -ErrorAction SilentlyContinue
            }
        }
        $script:SessionPool.Clear()
        $script:SessionLastUsed.Clear()
        Write-Host "✓ Cleared all sessions from pool" -ForegroundColor Green
    }
}

function Get-SessionPoolStatus {
    Write-Host "`nSession Pool Status:" -ForegroundColor Cyan
    
    if ($script:SessionPool.Count -eq 0) {
        Write-Host "  No active sessions" -ForegroundColor Gray
        return
    }
    
    foreach ($computer in $script:SessionPool.Keys) {
        $sessions = $script:SessionPool[$computer]
        Write-Host "  $computer`: $($sessions.Count) session(s)" -ForegroundColor White
        
        foreach ($session in $sessions) {
            $lastUsed = $script:SessionLastUsed[$session.Id]
            $idleTime = (Get-Date) - $lastUsed
            $status = if ($session.State -eq 'Opened') { "Active" } else { $session.State }
            Write-Host "    ID $($session.Id): $status (idle: $([math]::Round($idleTime.TotalMinutes, 1)) min)" -ForegroundColor Gray
        }
    }
}

# Export functions for use in other scripts
Export-ModuleMember -Function Initialize-SessionManager, Get-PersistentSession, Invoke-PersistentCommand, Clear-SessionPool, Get-SessionPoolStatus
