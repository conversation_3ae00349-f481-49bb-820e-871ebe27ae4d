# Clean USB Copy Script - ISE Compatible
# Developed by: The greatest technician that ever lived
# Purpose: Reliable USB copying without Unicode characters

param([switch]$WhatIf)

function Write-Status($Message, $Type = "Info") {
    $color = switch ($Type) {
        "Success" { "Green" }
        "Warning" { "Yellow" }
        "Error" { "Red" }
        "Info" { "Cyan" }
        default { "White" }
    }
    Write-Host $Message -ForegroundColor $color
}

function Format-Size([long]$Bytes) {
    if ($Bytes -eq 0) { return "0 B" }
    $sizes = @("B", "KB", "MB", "GB")
    $index = 0
    $size = [double]$Bytes
    while ($size -ge 1024 -and $index -lt 3) {
        $size /= 1024
        $index++
    }
    return "{0:N2} {1}" -f $size, $sizes[$index]
}

Write-Status "*** CLEAN USB COPY UTILITY ***" "Info"
if ($WhatIf) { Write-Status "WHAT-IF MODE ENABLED" "Warning" }
Write-Host ""

# Step 1: Find USB drives
Write-Status "Step 1: Finding USB drives..." "Info"
$usbDrives = @()

$allDrives = Get-WmiObject Win32_LogicalDisk
foreach ($drive in $allDrives) {
    if ($drive.DriveType -eq 2 -and $drive.Size -gt 0) {
        $label = if ($drive.VolumeName) { $drive.VolumeName } else { "No Label" }
        $sizeGB = [math]::Round($drive.Size / 1GB, 2)
        $freeGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        
        $usbDrives += [PSCustomObject]@{
            Drive = $drive.DeviceID
            Label = $label
            SizeGB = $sizeGB
            FreeGB = $freeGB
            Display = "$($drive.DeviceID) [$label] ($sizeGB GB total, $freeGB GB free)"
        }
    }
}

if ($usbDrives.Count -eq 0) {
    Write-Status "ERROR: No USB drives found!" "Error"
    Write-Status "All drives detected:" "Info"
    foreach ($drive in $allDrives) {
        $type = switch ($drive.DriveType) {
            2 { "Removable" }
            3 { "Fixed" }
            4 { "Network" }
            5 { "CD-ROM" }
            default { "Other" }
        }
        $size = if ($drive.Size) { [math]::Round($drive.Size / 1GB, 2) } else { 0 }
        Write-Host "  $($drive.DeviceID) - $type - $size GB" -ForegroundColor Gray
    }
    Write-Status "Make sure your USB drive is inserted and try again." "Warning"
    exit 1
}

Write-Status "SUCCESS: Found $($usbDrives.Count) USB drive(s)" "Success"
for ($i = 0; $i -lt $usbDrives.Count; $i++) {
    Write-Host "  [$i] $($usbDrives[$i].Display)" -ForegroundColor Gray
}

# Step 2: Get user selection
Write-Host ""
do {
    $selection = Read-Host "Select USB drive (0-$($usbDrives.Count - 1))"
    $index = $null
    if ([int]::TryParse($selection, [ref]$index) -and $index -ge 0 -and $index -lt $usbDrives.Count) {
        break
    }
    Write-Status "Invalid selection. Please try again." "Error"
} while ($true)

$selectedUSB = $usbDrives[$index]
Write-Status "Selected: $($selectedUSB.Display)" "Success"

# Step 3: Check source directory
Write-Host ""
Write-Status "Step 2: Checking source files..." "Info"
$sourcePath = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Usb drive files"
Write-Host "Source: $sourcePath" -ForegroundColor Gray

if (-not (Test-Path $sourcePath)) {
    Write-Status "ERROR: Source directory not found!" "Error"
    
    # Check if base path exists
    $basePath = "\\storagehd\Desktopshare\Scripts\Andrew\Needed"
    if (Test-Path $basePath) {
        Write-Status "Base path exists, but USB files folder is missing." "Warning"
        Write-Status "Run Copy-NetworkFiles.ps1 first to create the USB files." "Info"
    } else {
        Write-Status "Network path not accessible. Check connectivity." "Error"
    }
    exit 1
}

# Get source files and calculate size
$sourceItems = Get-ChildItem -Path $sourcePath -ErrorAction SilentlyContinue
$totalSize = 0
$fileCount = 0
$folderCount = 0

foreach ($item in $sourceItems) {
    if ($item.PSIsContainer) {
        $folderCount++
        $folderSize = (Get-ChildItem -Path $item.FullName -Recurse -File -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
        $totalSize += $folderSize
    } else {
        $fileCount++
        $totalSize += $item.Length
    }
}

$totalSizeGB = [math]::Round($totalSize / 1GB, 2)
Write-Status "SUCCESS: Found $folderCount folders, $fileCount files" "Success"
Write-Status "Total size: $(Format-Size $totalSize) ($totalSizeGB GB)" "Info"

# Step 4: Check space
Write-Host ""
Write-Status "Step 3: Checking space..." "Info"
$requiredSpaceGB = $totalSizeGB * 1.1  # 10% buffer

if ($selectedUSB.FreeGB -lt $requiredSpaceGB) {
    Write-Status "ERROR: Insufficient space!" "Error"
    Write-Status "Required: $requiredSpaceGB GB (with 10% buffer)" "Error"
    Write-Status "Available: $($selectedUSB.FreeGB) GB" "Error"
    Write-Status "Shortfall: $([math]::Round($requiredSpaceGB - $selectedUSB.FreeGB, 2)) GB" "Error"
    exit 1
} else {
    Write-Status "SUCCESS: Space check passed - $($selectedUSB.FreeGB) GB available" "Success"
}

# Step 5: Confirm operation
Write-Host ""
if (-not $WhatIf) {
    Write-Status "Ready to copy $totalSizeGB GB to $($selectedUSB.Drive)" "Warning"
    $confirm = Read-Host "Continue? (Y/N)"
    if ($confirm -notmatch '^[Yy]$') {
        Write-Status "Operation cancelled." "Info"
        exit 0
    }
}

# Step 6: Copy files
Write-Host ""
Write-Status "Step 4: Copying files..." "Info"
$destination = $selectedUSB.Drive + "\"
$successCount = 0
$errorCount = 0
$startTime = Get-Date

foreach ($item in $sourceItems) {
    $itemName = $item.Name
    $sourceFull = $item.FullName
    $destFull = Join-Path $destination $itemName
    
    Write-Host "Copying: $itemName" -ForegroundColor Yellow
    
    if ($WhatIf) {
        Write-Host "  WHAT-IF: Would copy to $destFull" -ForegroundColor Cyan
        $successCount++
        continue
    }
    
    try {
        if ($item.PSIsContainer) {
            # Copy folder using PowerShell
            Write-Host "  Copying folder..." -ForegroundColor Gray
            Copy-Item -Path $sourceFull -Destination $destination -Recurse -Force
            Write-Host "  SUCCESS: Folder copied" -ForegroundColor Green
        } else {
            # Copy file
            Write-Host "  Copying file..." -ForegroundColor Gray
            Copy-Item -Path $sourceFull -Destination $destFull -Force
            Write-Host "  SUCCESS: File copied" -ForegroundColor Green
        }
        $successCount++
    }
    catch {
        Write-Host "  ERROR: Copy failed - $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
    }
}

# Step 7: Summary
$endTime = Get-Date
$duration = ($endTime - $startTime).TotalSeconds

Write-Host ""
Write-Status "*** COPY OPERATION COMPLETE ***" "Info"
Write-Status "Successful: $successCount items" "Success"

if ($errorCount -gt 0) {
    Write-Status "Failed: $errorCount items" "Error"
} else {
    Write-Status "No errors!" "Success"
}

Write-Status "Time taken: $([math]::Round($duration, 1)) seconds" "Info"

if (-not $WhatIf) {
    # Check final space usage
    $finalDrive = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq $selectedUSB.Drive }
    $finalFreeGB = [math]::Round($finalDrive.FreeSpace / 1GB, 2)
    $usedSpace = $selectedUSB.FreeGB - $finalFreeGB
    
    Write-Status "Space used: $(Format-Size ($usedSpace * 1GB))" "Info"
    Write-Status "Space remaining: $finalFreeGB GB" "Info"
    
    if ($errorCount -eq 0) {
        Write-Status "SUCCESS: USB drive is ready for migration!" "Success"
    }
}

if ($WhatIf) {
    Write-Status "This was a WHAT-IF run. Use without -WhatIf to actually copy files." "Warning"
}

Write-Host ""
Write-Host "Press Enter to exit..." -ForegroundColor Gray
Read-Host
