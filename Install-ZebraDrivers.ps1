# Zebra Printer Driver Installation Script
# Developed by: The greatest technician that ever lived
# Purpose: Copy and install Zebra ZDesigner drivers to remote domain computers

param(
    [Parameter(Mandatory=$true)]
    [string]$TargetComputer,
    
    [Parameter(Mandatory=$false)]
    [switch]$UseLocalSource,
    
    [Parameter(Mandatory=$false)]
    [string]$LocalSourcePath = "D:\ZebraDriver",
    
    [Parameter(Mandatory=$false)]
    [string]$NetworkSourcePath = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\ZebraDriver",
    
    [Parameter(Mandatory=$false)]
    [switch]$AutoYes
)

# Color output function
function Write-Color {
    param([string]$Message, [string]$Color = "White", [switch]$NoNewline)
    if ($NoNewline) { Write-Host $Message -ForegroundColor $Color -NoNewline }
    else { Write-Host $Message -ForegroundColor $Color }
}

# Banner function
function Show-Banner {
    param([string]$Message, [string]$Color = "Cyan")
    $width = [Math]::Max(60, $Message.Length + 10)
    Write-Host ""
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ("*" + (" " * (($width - $Message.Length - 2) / 2)) + $Message + (" " * (($width - $Message.Length - 2) / 2)) + "*") -ForegroundColor $Color
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ""
}

# Test PC connectivity and DNS
function Test-PCConnectivity {
    param([string]$ComputerName)
    
    Write-Color "Testing connectivity to $ComputerName..." "Yellow"
    
    # Test ping
    $pingResult = Test-Connection -ComputerName $ComputerName -Count 2 -Quiet
    if (-not $pingResult) {
        return @{ Online = $false; DNSStatus = "Offline"; Error = "Computer is not responding to ping" }
    }
    
    # Test DNS resolution
    try {
        $dnsResult = Resolve-DnsName -Name $ComputerName -ErrorAction Stop
        $resolvedName = $dnsResult.Name

        # Extract just the computer name (before first dot) for comparison
        $inputComputerName = $ComputerName.Split('.')[0].ToLower()
        $resolvedComputerName = $resolvedName.Split('.')[0].ToLower()

        if ($resolvedComputerName -eq $inputComputerName) {
            return @{ Online = $true; DNSStatus = "Valid"; Error = $null }
        } else {
            return @{ Online = $true; DNSStatus = "Mismatch"; Error = "DNS name mismatch: $resolvedName" }
        }
    }
    catch {
        return @{ Online = $true; DNSStatus = "Failed"; Error = "DNS resolution failed: $($_.Exception.Message)" }
    }
}

# Initialize PSRemoting
function Initialize-PSRemoting {
    param([string]$TargetComputer)
    
    Write-Color "Initializing PSRemoting to $TargetComputer..." "Yellow"
    
    try {
        # Test if PSRemoting is already working
        $testSession = New-PSSession -ComputerName $TargetComputer -ErrorAction Stop
        Remove-PSSession $testSession
        Write-Color "✓ PSRemoting is already configured" "Green"
        return $true
    }
    catch {
        Write-Color "PSRemoting not available, attempting to enable..." "Yellow"
        
        # Try to enable PSRemoting via CIM/DCOM
        try {
            $cimSession = New-CimSession -ComputerName $TargetComputer -ErrorAction Stop
            
            # Enable PSRemoting
            Invoke-CimMethod -CimSession $cimSession -ClassName Win32_Process -MethodName Create -Arguments @{
                CommandLine = "powershell.exe -Command Enable-PSRemoting -Force -SkipNetworkProfileCheck"
            } | Out-Null
            
            Start-Sleep -Seconds 10
            Remove-CimSession $cimSession
            
            # Test again
            $testSession = New-PSSession -ComputerName $TargetComputer -ErrorAction Stop
            Remove-PSSession $testSession
            Write-Color "✓ PSRemoting enabled successfully" "Green"
            return $true
        }
        catch {
            Write-Color "✗ Failed to enable PSRemoting: $($_.Exception.Message)" "Red"
            return $false
        }
    }
}

# Execute remote command with fallback
function Invoke-RemoteCommand {
    param(
        [string]$TargetComputer,
        [scriptblock]$ScriptBlock,
        [array]$ArgumentList = @()
    )
    
    try {
        # Try PSSession first
        $session = New-PSSession -ComputerName $TargetComputer -ErrorAction Stop
        $result = Invoke-Command -Session $session -ScriptBlock $ScriptBlock -ArgumentList $ArgumentList
        Remove-PSSession $session
        return $result
    }
    catch {
        Write-Color "PSSession failed, trying CIM/DCOM fallback..." "Yellow"
        
        try {
            # Create a temporary script file for CIM execution
            $tempScript = [System.IO.Path]::GetTempFileName() + ".ps1"
            $scriptContent = $ScriptBlock.ToString()
            $scriptContent | Out-File -FilePath $tempScript -Encoding UTF8
            
            # Copy script to remote computer
            $remoteScriptPath = "\\$TargetComputer\c$\temp_zebra_script.ps1"
            Copy-Item -Path $tempScript -Destination $remoteScriptPath -Force
            
            # Execute via CIM
            $cimSession = New-CimSession -ComputerName $TargetComputer
            $result = Invoke-CimMethod -CimSession $cimSession -ClassName Win32_Process -MethodName Create -Arguments @{
                CommandLine = "powershell.exe -ExecutionPolicy Bypass -File C:\temp_zebra_script.ps1"
            }
            
            Start-Sleep -Seconds 5
            Remove-CimSession $cimSession
            
            # Clean up
            Remove-Item -Path $tempScript -Force -ErrorAction SilentlyContinue
            Remove-Item -Path $remoteScriptPath -Force -ErrorAction SilentlyContinue
            
            return "Command executed via CIM (output not captured)"
        }
        catch {
            Write-Color "Both PSSession and CIM methods failed: $($_.Exception.Message)" "Red"
            return "Error: Remote execution failed"
        }
    }
}

# Copy Zebra drivers to target computer
function Copy-ZebraDrivers {
    param([string]$TargetComputer)
    
    Write-Color "Copying Zebra drivers to target computer..." "Cyan"
    
    # Determine source path
    $sourcePath = if ($UseLocalSource) { $LocalSourcePath } else { $NetworkSourcePath }
    $destPath = "\\$TargetComputer\c$\Files\Drivers\Print\Zebra"
    
    Write-Color "Source: $sourcePath" "Gray"
    Write-Color "Destination: $destPath" "Gray"
    
    # Verify source exists
    if (-not (Test-Path $sourcePath)) {
        throw "Source path not found: $sourcePath"
    }
    
    # Create destination directory structure
    try {
        Write-Color "Creating directory structure: $destPath" "Gray"

        # Use New-Item with -Force to create the entire path at once
        if (-not (Test-Path $destPath)) {
            $null = New-Item -Path $destPath -ItemType Directory -Force -ErrorAction Stop
            Write-Color "✓ Created directory: $destPath" "Green"
        } else {
            Write-Color "✓ Directory already exists: $destPath" "Green"
        }

        # Verify it's actually a directory, not a file
        $item = Get-Item -Path $destPath -ErrorAction Stop
        if ($item.PSIsContainer) {
            Write-Color "✓ Confirmed destination is a directory" "Green"
        } else {
            # If it's a file, remove it and create the directory
            Write-Color "⚠ Destination exists as a file, removing and creating directory..." "Yellow"
            Remove-Item -Path $destPath -Force
            $null = New-Item -Path $destPath -ItemType Directory -Force -ErrorAction Stop
            Write-Color "✓ Directory created successfully" "Green"
        }
    }
    catch {
        throw "Failed to create directory structure: $($_.Exception.Message)"
    }
    
    # Copy files
    try {
        Write-Color "Copying Zebra driver files..." "Yellow"

        # Copy all contents from source to destination (directory already created above)
        Copy-Item -Path "$sourcePath\*" -Destination $destPath -Recurse -Force

        # Verify copy
        $copiedFiles = Get-ChildItem -Path $destPath -Recurse -ErrorAction SilentlyContinue
        Write-Color "✓ Successfully copied $($copiedFiles.Count) files/folders" "Green"

        # Check for ZDesigner.inf specifically
        $infFile = Join-Path $destPath "ZDesigner.inf"
        if (Test-Path $infFile) {
            Write-Color "✓ ZDesigner.inf found in destination" "Green"
        } else {
            Write-Color "⚠ Warning: ZDesigner.inf not found in root of destination" "Yellow"
        }

        return $true
    }
    catch {
        throw "Failed to copy files: $($_.Exception.Message)"
    }
}

# Install Zebra drivers on target computer
function Install-ZebraDrivers {
    param([string]$TargetComputer)
    
    Write-Color "Installing Zebra drivers on target computer..." "Cyan"
    
    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -ScriptBlock {
        $zebraDriverPath = "C:\Files\Drivers\Print\Zebra"
        $zebraInfFile = "$zebraDriverPath\ZDesigner.inf"
        
        Write-Host "Looking for Zebra driver at: $zebraInfFile" -ForegroundColor Yellow
        
        if (Test-Path $zebraInfFile) {
            try {
                Write-Host "Installing ZDesigner driver..." -ForegroundColor Yellow
                
                # Install the driver using pnputil
                $pnpResult = & pnputil /add-driver $zebraInfFile /install 2>&1
                Write-Host "PnPUtil result: $pnpResult" -ForegroundColor Gray

                # Parse the INF file to find all available driver names
                Write-Host "Parsing INF file for all available Zebra drivers..." -ForegroundColor Yellow
                $infContent = Get-Content $zebraInfFile -ErrorAction SilentlyContinue
                $driverNames = @()

                # Look for driver names in the INF file (they're usually in quotes)
                foreach ($line in $infContent) {
                    if ($line -match '^\s*"([^"]*ZDesigner[^"]*)"' -or $line -match '^\s*"([^"]*Zebra[^"]*)"') {
                        $driverName = $matches[1]
                        if ($driverName -and $driverNames -notcontains $driverName) {
                            $driverNames += $driverName
                        }
                    }
                }

                Write-Host "Found $($driverNames.Count) driver definitions in INF file" -ForegroundColor Green

                # Add each driver to the system
                $addedDrivers = 0
                $failedDrivers = 0
                $totalDrivers = $driverNames.Count

                Write-Host "Adding drivers to system..." -ForegroundColor Yellow
                foreach ($driverName in $driverNames) {
                    $currentIndex = $addedDrivers + $failedDrivers + 1
                    Write-Host "`r  Processing driver $currentIndex of $totalDrivers..." -ForegroundColor Gray -NoNewline

                    try {
                        Add-PrinterDriver -Name $driverName -ErrorAction Stop
                        $addedDrivers++
                    }
                    catch {
                        $failedDrivers++
                    }
                }
                Write-Host "`r  Completed processing $totalDrivers drivers.                    " -ForegroundColor Green

                # Also check what drivers are now available in the system
                $zebraDrivers = Get-PrinterDriver -ErrorAction SilentlyContinue | Where-Object {
                    $_.Name -like "*ZDesigner*" -or $_.Name -like "*Zebra*"
                }

                Write-Host "Summary:" -ForegroundColor Cyan
                Write-Host "  - Drivers found in INF: $($driverNames.Count)" -ForegroundColor Gray
                Write-Host "  - Drivers successfully added: $addedDrivers" -ForegroundColor Gray
                Write-Host "  - Drivers failed to add: $failedDrivers" -ForegroundColor Gray
                Write-Host "  - Total Zebra drivers available in system: $($zebraDrivers.Count)" -ForegroundColor Gray

                $driverCount = $zebraDrivers.Count

                # Clean up driver files
                Remove-Item -Path "C:\Files\Drivers" -Recurse -Force -ErrorAction SilentlyContinue
                Write-Host "Driver files cleaned up" -ForegroundColor Green

                return "Success: Zebra drivers installed ($driverCount drivers available)"
            }
            catch {
                return "Error: Failed to install Zebra drivers - $($_.Exception.Message)"
            }
        } else {
            # List what's actually there for troubleshooting
            Write-Host "ZDesigner.inf not found. Contents of $zebraDriverPath:" -ForegroundColor Yellow
            if (Test-Path $zebraDriverPath) {
                Get-ChildItem -Path $zebraDriverPath -Recurse | ForEach-Object {
                    Write-Host "  $($_.FullName)" -ForegroundColor Gray
                }
            } else {
                Write-Host "  Zebra driver folder does not exist" -ForegroundColor Red
            }
            return "Error: ZDesigner.inf not found"
        }
    }
    
    return $result
}

# Main execution
try {
    Show-Banner "ZEBRA PRINTER DRIVER INSTALLER" "Green"
    Write-Color "Target Computer: $TargetComputer" "Cyan"
    Write-Color "Source Mode: $(if ($UseLocalSource) { 'Local Drive' } else { 'Network Path' })" "Cyan"
    Write-Host ""
    
    # Step 1: Test connectivity
    Write-Color "[1/4] Testing PC connectivity..." "Yellow"
    $connectivity = Test-PCConnectivity -ComputerName $TargetComputer
    
    if (-not $connectivity.Online) {
        throw "Target computer is offline: $($connectivity.Error)"
    }
    
    if ($connectivity.DNSStatus -eq "Mismatch" -and -not $AutoYes) {
        $continue = Read-Host "DNS mismatch detected: $($connectivity.Error). Continue anyway? (Y/N)"
        if ($continue -notmatch '^(Y|y)$') {
            throw "Installation cancelled due to DNS mismatch"
        }
    }
    
    Write-Color "✓ Target computer is online and accessible" "Green"
    
    # Step 2: Initialize PSRemoting
    Write-Color "[2/4] Setting up remote connection..." "Yellow"
    $remotingSuccess = Initialize-PSRemoting -TargetComputer $TargetComputer
    
    if ($remotingSuccess) {
        Write-Color "✓ Remote connection established" "Green"
    } else {
        Write-Color "⚠ PSRemoting failed, will use fallback methods" "Yellow"
    }
    
    # Step 3: Copy drivers
    Write-Color "[3/4] Copying Zebra drivers..." "Yellow"
    Copy-ZebraDrivers -TargetComputer $TargetComputer
    Write-Color "✓ Zebra drivers copied successfully" "Green"
    
    # Step 4: Install drivers
    Write-Color "[4/4] Installing Zebra drivers..." "Yellow"
    $installResult = Install-ZebraDrivers -TargetComputer $TargetComputer
    Write-Color $installResult "Gray"
    
    if ($installResult -like "Success:*") {
        Show-Banner "INSTALLATION COMPLETED SUCCESSFULLY" "Green"
        Write-Color "Zebra drivers have been installed on $TargetComputer" "Green"
        Write-Color "You can now add Zebra printers using the installed drivers" "Cyan"
    } else {
        Show-Banner "INSTALLATION COMPLETED WITH WARNINGS" "Yellow"
        Write-Color "Check the output above for details" "Yellow"
    }
}
catch {
    Show-Banner "INSTALLATION FAILED" "Red"
    Write-Color "Error: $($_.Exception.Message)" "Red"
    Write-Color "Please check the error details and try again" "Yellow"
    exit 1
}

Write-Host ""
Write-Color "Script completed. Press Enter to exit..." "Gray"
if (-not $AutoYes) {
    try {
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    catch {
        # Fallback for environments that don't support ReadKey
        $null = Read-Host
    }
}
