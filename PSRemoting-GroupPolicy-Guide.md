# Group Policy Configuration for PSRemoting

## Overview
Instead of manually enabling PSRemoting on each computer, configure it domain-wide using Group Policy. This ensures consistent configuration and eliminates the need to repeatedly enable PSRemoting.

## Group Policy Settings

### 1. Computer Configuration > Policies > Administrative Templates > Windows Components > Windows Remote Management (WinRM) > WinRM Service

**Enable these settings:**

- **Allow remote server management through WinRM**: Enabled
  - IPv4 filter: `*` (or specific IP ranges)
  - IPv6 filter: `*` (or specific IP ranges)

- **Allow Basic authentication**: Disabled (for security)
- **Allow Kerberos authentication**: Enabled
- **Allow Negotiate authentication**: Enabled
- **Allow unencrypted traffic**: Disabled (for security)

### 2. Computer Configuration > Policies > Administrative Templates > Windows Components > Windows Remote Management (WinRM) > WinRM Client

**Enable these settings:**

- **Allow Basic authentication**: Disabled
- **Allow Kerberos authentication**: Enabled
- **Allow Negotiate authentication**: Enabled
- **Allow unencrypted traffic**: Disabled
- **Trusted Hosts**: Configure as needed for your environment

### 3. Computer Configuration > Policies > Windows Settings > Security Settings > System Services

**Configure WinRM service:**

- **Windows Remote Management (WS-Management)**: Automatic startup

### 4. Computer Configuration > Policies > Windows Settings > Security Settings > Windows Firewall with Advanced Security

**Create inbound rules:**

- **Windows Remote Management (HTTP-In)**: Allow
- **Windows Remote Management (HTTPS-In)**: Allow (if using HTTPS)

## PowerShell Script to Verify GP Configuration

```powershell
# Check if PSRemoting is enabled via Group Policy
function Test-PSRemotingGPO {
    param([string[]]$TargetComputers)
    
    foreach ($computer in $TargetComputers) {
        Write-Host "Testing $computer..." -ForegroundColor Cyan
        
        try {
            # Test WinRM service
            $service = Get-Service -ComputerName $computer -Name WinRM -ErrorAction Stop
            Write-Host "  WinRM Service: $($service.Status)" -ForegroundColor $(if($service.Status -eq 'Running'){'Green'}else{'Red'})
            
            # Test PSRemoting
            $session = New-PSSession -ComputerName $computer -ErrorAction Stop
            $result = Invoke-Command -Session $session -ScriptBlock { $env:COMPUTERNAME }
            Remove-PSSession $session
            
            Write-Host "  PSRemoting: Working (Connected to $result)" -ForegroundColor Green
        }
        catch {
            Write-Host "  PSRemoting: Failed - $_" -ForegroundColor Red
        }
    }
}
```

## Registry Settings (Alternative to GP)

If Group Policy isn't available, you can configure these registry settings:

```powershell
# Registry configuration for PSRemoting
$registrySettings = @{
    "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WinRM\Service" = @{
        "AllowAutoConfig" = 1
        "IPv4Filter" = "*"
        "IPv6Filter" = "*"
    }
    "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WinRM\Client" = @{
        "AllowBasic" = 0
        "AllowCredSSP" = 0
        "AllowUnencryptedTraffic" = 0
    }
}

foreach ($path in $registrySettings.Keys) {
    if (-not (Test-Path $path)) {
        New-Item -Path $path -Force | Out-Null
    }
    
    foreach ($setting in $registrySettings[$path].Keys) {
        Set-ItemProperty -Path $path -Name $setting -Value $registrySettings[$path][$setting]
    }
}
```

## Benefits of GP Configuration

1. **Consistent Configuration**: All domain computers get the same PSRemoting settings
2. **Automatic Application**: New computers automatically get PSRemoting enabled
3. **Security**: Centralized security policy enforcement
4. **No Manual Intervention**: Eliminates need to enable PSRemoting in scripts
5. **Compliance**: Ensures all systems meet organizational standards

## Security Considerations

- Use Kerberos authentication when possible
- Disable Basic authentication
- Configure appropriate firewall rules
- Use HTTPS for WinRM when possible
- Implement proper access controls
- Monitor PSRemoting usage

## Troubleshooting

Common issues and solutions:

1. **Firewall blocking**: Ensure Windows Firewall rules are configured
2. **Authentication failures**: Check Kerberos/domain trust
3. **Service not starting**: Verify service startup type and dependencies
4. **GP not applying**: Check GP processing and replication
5. **Network connectivity**: Verify DNS resolution and network access
