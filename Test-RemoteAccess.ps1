# Simple diagnostic script to test remote access methods
param(
    [string]$ComputerName = "brbr319edt724d"
)

Write-Host "Testing remote access to: $ComputerName" -ForegroundColor Cyan
Write-Host "============================================" -ForegroundColor Cyan

# Test 1: Basic connectivity
Write-Host "`n1. Testing basic connectivity..." -ForegroundColor Yellow
try {
    if (Test-Connection -ComputerName $ComputerName -Count 2 -Quiet) {
        Write-Host "   SUCCESS: Computer is reachable" -ForegroundColor Green
    } else {
        Write-Host "   FAILED: Computer is not reachable" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "   ERROR: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Remote Registry
Write-Host "`n2. Testing Remote Registry access..." -ForegroundColor Yellow
try {
    $reg = [Microsoft.Win32.RegistryKey]::OpenRemoteBaseKey('LocalMachine', $ComputerName)
    $testKey = $reg.OpenSubKey("SOFTWARE\Microsoft\Windows\CurrentVersion")
    if ($testKey) {
        Write-Host "   SUCCESS: Remote Registry is accessible" -ForegroundColor Green
        $testKey.Close()
    }
    $reg.Close()
} catch {
    Write-Host "   FAILED: Remote Registry not accessible - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: WMI Access
Write-Host "`n3. Testing WMI access..." -ForegroundColor Yellow
try {
    $os = Get-WmiObject -Class Win32_OperatingSystem -ComputerName $ComputerName -ErrorAction Stop
    Write-Host "   SUCCESS: WMI is accessible" -ForegroundColor Green
    Write-Host "   OS: $($os.Caption)" -ForegroundColor White
} catch {
    Write-Host "   FAILED: WMI not accessible - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: WinRM/PSRemoting
Write-Host "`n4. Testing WinRM/PSRemoting..." -ForegroundColor Yellow
try {
    Test-WSMan -ComputerName $ComputerName -ErrorAction Stop | Out-Null
    Write-Host "   SUCCESS: WinRM is accessible" -ForegroundColor Green
} catch {
    Write-Host "   FAILED: WinRM not accessible - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Simple PowerShell Remoting
Write-Host "`n5. Testing PowerShell Remoting..." -ForegroundColor Yellow
try {
    $result = Invoke-Command -ComputerName $ComputerName -ScriptBlock { $env:COMPUTERNAME } -ErrorAction Stop
    Write-Host "   SUCCESS: PowerShell Remoting works - Connected to $result" -ForegroundColor Green
} catch {
    Write-Host "   FAILED: PowerShell Remoting failed - $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Try to get a simple application list
Write-Host "`n6. Testing application enumeration..." -ForegroundColor Yellow
try {
    # Try remote registry first
    $reg = [Microsoft.Win32.RegistryKey]::OpenRemoteBaseKey('LocalMachine', $ComputerName)
    $uninstallKey = $reg.OpenSubKey("SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall")
    if ($uninstallKey) {
        $subKeys = $uninstallKey.GetSubKeyNames()
        $appCount = 0
        foreach ($subKeyName in $subKeys[0..9]) {  # Test first 10 only
            $subKey = $uninstallKey.OpenSubKey($subKeyName)
            $displayName = $subKey.GetValue("DisplayName")
            if ($displayName) {
                $appCount++
                Write-Host "   Found: $displayName" -ForegroundColor White
            }
            $subKey.Close()
        }
        $uninstallKey.Close()
        Write-Host "   SUCCESS: Found $appCount applications (showing first 10)" -ForegroundColor Green
    }
    $reg.Close()
} catch {
    Write-Host "   FAILED: Could not enumerate applications - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n============================================" -ForegroundColor Cyan
Write-Host "Diagnostic complete" -ForegroundColor Cyan
