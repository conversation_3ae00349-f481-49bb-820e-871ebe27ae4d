@echo off
setlocal enabledelayedexpansion

:: ===============================================================================
:: INSTALLER FILES COPY UTILITY WITH USB DRIVE SELECTION
:: This batch file copies all required installer files from network shares to
:: a selected USB or local drive for use with the migration script
:: ===============================================================================

echo.
echo ********************************************************************************
echo *                    INSTALLER FILES COPY UTILITY                             *
echo *                                                                              *
echo * This utility will copy all required installer files from network shares     *
echo * to a selected USB or local drive for offline installation use.              *
echo *                                                                              *
echo * Estimated total size: ~15-20 GB                                             *
echo * Estimated time: 30-60 minutes (depending on network speed)                  *
echo ********************************************************************************
echo.

:: Detect and list available drives
call :DetectDrives
if %errorlevel% neq 0 exit /b 1

:: Check available space on selected drive (requires at least 25GB free)
call :CheckDriveSpace "%SELECTED_DRIVE%"
if %errorlevel% neq 0 exit /b 1

:: Prompt user to continue
set /p confirm="Do you want to proceed with copying installer files to %SELECTED_DRIVE%? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Operation cancelled by user.
    pause
    exit /b 0
)

echo.
echo Starting copy operations to %SELECTED_DRIVE%...
echo.

:: Create base directories on selected drive
echo [1/16] Creating directory structure on %SELECTED_DRIVE%...
mkdir "%SELECTED_DRIVE%\Splashtop_Push" 2>nul
mkdir "%SELECTED_DRIVE%\sxs" 2>nul
mkdir "%SELECTED_DRIVE%\Lexmark_Universal_v2_UD1_PostScript_3_Emulation" 2>nul
mkdir "%SELECTED_DRIVE%\o365" 2>nul
mkdir "%SELECTED_DRIVE%\4.9_LTSR2" 2>nul
mkdir "%SELECTED_DRIVE%\Nuance121" 2>nul
mkdir "%SELECTED_DRIVE%\FSTools" 2>nul
mkdir "%SELECTED_DRIVE%\LexmarkGDI" 2>nul

echo Directory structure created successfully.
echo.

:: Copy Splashtop
echo [2/16] Copying Splashtop files...
robocopy "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\Splashtop_Push" "%SELECTED_DRIVE%\Splashtop_Push" /E /R:3 /W:10 /MT:8 /NP
if %errorlevel% geq 8 (
    echo WARNING: Splashtop copy completed with errors
) else (
    echo Splashtop copy completed successfully
)
echo.

:: Copy .NET 3.5 SXS
echo [3/16] Copying .NET 3.5 SXS files...
robocopy "\\bay-msfsnas01\data\FS\PUBLIC\installdotnet3_5\Win11\sxs" "%SELECTED_DRIVE%\sxs" /E /R:3 /W:10 /MT:8 /NP
if %errorlevel% geq 8 (
    echo WARNING: .NET SXS copy completed with errors
) else (
    echo .NET SXS copy completed successfully
)
echo.

:: Copy Lexmark Driver
echo [4/16] Copying Lexmark Universal Driver...
robocopy "\\bay-msfsnas01\data\FS\SFILES\__install__\source\Lexmark\Lexmark_Universal_v2_UD1_PostScript_3_Emulation" "%SELECTED_DRIVE%\Lexmark_Universal_v2_UD1_PostScript_3_Emulation" /E /R:3 /W:10 /MT:8 /NP
if %errorlevel% geq 8 (
    echo WARNING: Lexmark Driver copy completed with errors
) else (
    echo Lexmark Driver copy completed successfully
)
echo.

:: Copy Office 365
echo [5/16] Copying Office 365 files...
robocopy "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\o365" "%SELECTED_DRIVE%\o365" /E /R:3 /W:10 /MT:8 /NP
if %errorlevel% geq 8 (
    echo WARNING: Office 365 copy completed with errors
) else (
    echo Office 365 copy completed successfully
)
echo.

:: Copy Citrix
echo [6/16] Copying Citrix 4.9 LTSR2...
robocopy "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\programs\4.9_LTSR2" "%SELECTED_DRIVE%\4.9_LTSR2" /E /R:3 /W:10 /MT:8 /NP
if %errorlevel% geq 8 (
    echo WARNING: Citrix copy completed with errors
) else (
    echo Citrix copy completed successfully
)
echo.

:: Copy Nuance
echo [7/16] Copying Nuance files...
robocopy "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\CustomCitrix\4.9_LTSR2\Nuance" "%SELECTED_DRIVE%\Nuance121" /E /R:3 /W:10 /MT:8 /NP
if %errorlevel% geq 8 (
    echo WARNING: Nuance copy completed with errors
) else (
    echo Nuance copy completed successfully
)
echo.

:: Copy Java
echo [8/16] Copying Java RE 7...
copy "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\WSP\7\jre1.7.0_45.msi" "%SELECTED_DRIVE%\jre1.7.0_45.msi"
if %errorlevel% neq 0 (
    echo WARNING: Java copy failed
) else (
    echo Java copy completed successfully
)
echo.

:: Copy Chrome
echo [9/16] Copying Google Chrome...
copy "\\bay-msfsnas01\data\FS\SFILES\__install__\_Install\Dept Related programs and files\Google\GoogleChromeStandaloneEnterprise64.msi" "%SELECTED_DRIVE%\GoogleChromeStandaloneEnterprise64.msi"
if %errorlevel% neq 0 (
    echo WARNING: Chrome copy failed
) else (
    echo Chrome copy completed successfully
)
echo.

:: Copy Windows Photo Viewer Registry
echo [10/16] Copying Windows Photo Viewer registry file...
copy "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Win_Photo_Viewer.reg" "%SELECTED_DRIVE%\Win_Photo_Viewer.reg"
if %errorlevel% neq 0 (
    echo WARNING: Photo Viewer registry copy failed
) else (
    echo Photo Viewer registry copy completed successfully
)
echo.

:: Copy Volume Script
echo [11/16] Copying Volume PowerShell script...
copy "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\Volume.ps1" "%SELECTED_DRIVE%\Volume.ps1"
if %errorlevel% neq 0 (
    echo WARNING: Volume script copy failed
) else (
    echo Volume script copy completed successfully
)
echo.

:: Copy BitLocker Script
echo [12/16] Copying BitLocker PowerShell script...
copy "\\bay-msfsnas01\data\FS\SFILES\__install__\PS\BitLockerAD.ps1" "%SELECTED_DRIVE%\BitLockerAD.ps1"
if %errorlevel% neq 0 (
    echo WARNING: BitLocker script copy failed
) else (
    echo BitLocker script copy completed successfully
)
echo.

:: Copy Dell Command Update
echo [13/16] Copying Dell Command Update...
copy "\\bay-msfsnas01\data\FS\Source\Commandupdate.EXE" "%SELECTED_DRIVE%\Commandupdate.EXE"
if %errorlevel% neq 0 (
    echo WARNING: Dell Command Update copy failed
) else (
    echo Dell Command Update copy completed successfully
)
echo.

:: Copy FSTools
echo [14/16] Copying FSTools...
robocopy "\\bay-msfsnas01\data\FS\SFILES\__install__\Source\FSTools" "%SELECTED_DRIVE%\FSTools" /E /R:3 /W:10 /MT:8 /NP
if %errorlevel% geq 8 (
    echo WARNING: FSTools copy completed with errors
) else (
    echo FSTools copy completed successfully
)
echo.

:: Copy Lexmark GDI Driver
echo [15/16] Copying Lexmark GDI Driver...
robocopy "\\storagehd\Desktopshare\Scripts\Andrew\Needed\LexmarkGDI" "%SELECTED_DRIVE%\LexmarkGDI" /E /R:3 /W:10 /MT:8 /NP
if %errorlevel% geq 8 (
    echo WARNING: Lexmark GDI copy completed with errors
) else (
    echo Lexmark GDI copy completed successfully
)
echo.

:: Copy Imprivata Agent MSI
echo [16/16] Copying Imprivata Agent MSI...
copy "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Imprivata_push\ImprivataAgent_x64.msi" "%SELECTED_DRIVE%\ImprivataAgent_x64.msi"
if %errorlevel% neq 0 (
    echo WARNING: Imprivata Agent copy completed with errors
) else (
    echo Imprivata Agent copy completed successfully
)
echo.

echo ********************************************************************************
echo *                           COPY OPERATION COMPLETE                           *
echo ********************************************************************************
echo.
echo All installer files have been copied to %SELECTED_DRIVE%.
echo.
echo Files copied to:
echo   %SELECTED_DRIVE%\Splashtop_Push\
echo   %SELECTED_DRIVE%\sxs\
echo   %SELECTED_DRIVE%\Lexmark_Universal_v2_UD1_PostScript_3_Emulation\
echo   %SELECTED_DRIVE%\o365\
echo   %SELECTED_DRIVE%\4.9_LTSR2\
echo   %SELECTED_DRIVE%\Nuance121\
echo   %SELECTED_DRIVE%\FSTools\
echo   %SELECTED_DRIVE%\LexmarkGDI\
echo   %SELECTED_DRIVE%\Imprivata_push\
echo   %SELECTED_DRIVE%\jre1.7.0_45.msi
echo   %SELECTED_DRIVE%\GoogleChromeStandaloneEnterprise64.msi
echo   %SELECTED_DRIVE%\Win_Photo_Viewer.reg
echo   %SELECTED_DRIVE%\Volume.ps1
echo   %SELECTED_DRIVE%\BitLockerAD.ps1
echo   %SELECTED_DRIVE%\Commandupdate.EXE
echo.
echo Total files copied: 16 items
echo Selected drive: %SELECTED_DRIVE%
echo.
echo NEXT STEPS:
echo 1. If using USB drive, copy files from %SELECTED_DRIVE% to D:\ on target computer
echo 2. Run the migration script and select "Yes" for local installer files
echo 3. Migration script will look for files on D:\ drive
echo.
pause
goto :eof

:: ===============================================================================
:: FUNCTIONS
:: ===============================================================================

:DetectDrives
echo Detecting available drives...
echo.
set "drive_count=0"
set "drives_list="

:: Check for removable drives (USB) first
echo REMOVABLE DRIVES (USB/External):
call :CheckDriveType 2 "USB/Removable"

:: Check for fixed drives (excluding C:)
echo.
echo FIXED DRIVES (Local Hard Drives):
call :CheckDriveType 3 "Fixed Drive"

if %drive_count% equ 0 (
    echo ERROR: No suitable drives found!
    echo Please connect a USB drive or ensure other drives are available.
    pause
    exit /b 1
)

echo.
echo [0] Exit
echo.
set /p drive_choice="Please select a drive (1-%drive_count% or 0 to exit): "

if "%drive_choice%"=="0" (
    echo Operation cancelled by user.
    exit /b 1
)

if %drive_choice% lss 1 if %drive_choice% gtr %drive_count% (
    echo Invalid selection. Please try again.
    goto :DetectDrives
)

:: Get selected drive
set "counter=0"
for %%d in (%drives_list%) do (
    set /a counter+=1
    if !counter! equ %drive_choice% (
        set "SELECTED_DRIVE=%%d"
        goto :DriveSelected
    )
)

:DriveSelected
echo.
echo Selected drive: %SELECTED_DRIVE%
goto :eof

:CheckDriveType
set "drivetype=%~1"
set "typename=%~2"

:: Loop through all drive letters
for %%d in (D E F G H I J K L M N O P Q R S T U V W X Y Z) do (
    if exist %%d:\ (
        :: Get drive type using fsutil
        for /f "tokens=2 delims=:" %%t in ('fsutil fsinfo drivetype %%d: 2^>nul') do (
            set "detected_type=%%t"
            set "detected_type=!detected_type: =!"

            :: Check if this matches the requested drive type
            if "%drivetype%"=="2" if /i "!detected_type!"=="Removable" (
                call :AddDrive "%%d:" "%typename%"
            )
            if "%drivetype%"=="3" if /i "!detected_type!"=="Fixed" (
                if /i not "%%d:"=="C:" (
                    call :AddDrive "%%d:" "%typename%"
                )
            )
        )
    )
)
goto :eof

:AddDrive
set "drive=%~1"
set "type=%~2"

:: Get volume label and size info
for /f "tokens=3" %%s in ('dir %drive% ^| find "bytes free"') do set "freespace=%%s"
for /f "tokens=1" %%s in ('dir %drive% ^| find "bytes free"') do set "totalspace=%%s"

:: Get volume label
for /f "tokens=5*" %%v in ('vol %drive% 2^>nul') do set "volumename=%%v %%w"

:: Clean up the values
set "freespace=%freespace:,=%"
set "totalspace=%totalspace:,=%"
if "%volumename%"==" " set "volumename="

set /a drive_count+=1
set "drives_list=!drives_list! %drive%"

call :DisplayDriveInfo "!drive_count!" "%drive%" "!freespace!" "!totalspace!" "%type%" "!volumename!"
goto :eof

:DisplayDriveInfo
set "num=%~1"
set "drive=%~2"
set "freespace=%~3"
set "totalspace=%~4"
set "type=%~5"
set "volumename=%~6"

:: Convert bytes to GB
set /a freespace_gb=%freespace%/1073741824 2>nul
set /a totalspace_gb=%totalspace%/1073741824 2>nul

:: Handle conversion errors
if "%freespace_gb%"=="" set "freespace_gb=0"
if "%totalspace_gb%"=="" set "totalspace_gb=0"

:: Display drive info with volume name if available
if "%volumename%" neq "" (
    echo [%num%] %drive% "%volumename%" (%type%) - %freespace_gb% GB free / %totalspace_gb% GB total
) else (
    echo [%num%] %drive% (%type%) - %freespace_gb% GB free / %totalspace_gb% GB total
)
goto :eof

:CheckDriveSpace
set "drive=%~1"
echo Checking available space on %drive%...

for /f "tokens=3" %%a in ('dir %drive% ^| find "bytes free"') do set freespace=%%a
set freespace=%freespace:,=%

if %freespace% LSS 25000000000 (
    echo WARNING: %drive% may not have enough free space!
    echo Available: %freespace% bytes
    echo Recommended: At least 25 GB free space
    echo.
    set /p continue="Do you want to continue anyway? (Y/N): "
    if /i not "!continue!"=="Y" exit /b 1
)

echo Drive space check passed.
goto :eof
