# Adobe Remote Deployment - Usage Examples
# Developed by: The greatest technician that ever lived

# Example 1: Deploy to single computer with network download
# .\Deploy-Adobe-Remote.ps1 -ComputerNames "PC001"

# Example 2: Deploy to multiple computers with network download
# .\Deploy-Adobe-Remote.ps1 -ComputerNames "PC001","PC002","PC003"

# Example 3: Deploy using local installer file
# .\Deploy-Adobe-Remote.ps1 -ComputerNames "PC001" -UseLocalInstaller -LocalInstallerPath "D:\Adobe\adobe.bat"

# Example 4: Deploy with specific credentials
# $cred = Get-Credential
# .\Deploy-Adobe-Remote.ps1 -ComputerNames "PC001","PC002" -Credential $cred

# Example 5: Interactive deployment with computer name input
Write-Host "========================================================================" -ForegroundColor Cyan
Write-Host "           Adobe Acrobat DC Remote Deployment" -ForegroundColor Cyan
Write-Host "           Interactive Mode" -ForegroundColor Cyan
Write-Host "========================================================================" -ForegroundColor Cyan

# Get computer names
Write-Host "`nEnter target computer names (comma-separated):" -ForegroundColor Yellow
$computerInput = Read-Host "Computer Names"

if ([string]::IsNullOrWhiteSpace($computerInput)) {
    Write-Error "No computer names provided"
    exit 1
}

$computers = $computerInput -split ',' | ForEach-Object { $_.Trim() }

# Choose installation method
Write-Host "`nChoose installation method:" -ForegroundColor Yellow
Write-Host "1. Network download (default)"
Write-Host "2. Use local installer file"
$choice = Read-Host "Enter choice (1 or 2)"

$useLocal = $false
$localPath = ""

if ($choice -eq "2") {
    $useLocal = $true
    Write-Host "`nEnter path to local adobe.bat file:" -ForegroundColor Yellow
    $localPath = Read-Host "Local Path"
    
    if (-not (Test-Path $localPath)) {
        Write-Error "Local installer file not found: $localPath"
        exit 1
    }
}

# Get credentials
Write-Host "`nEnter domain credentials for remote access:" -ForegroundColor Yellow
$credential = Get-Credential -Message "Enter domain credentials"

if (-not $credential) {
    Write-Error "Credentials are required"
    exit 1
}

# Execute deployment
if ($useLocal) {
    & ".\Deploy-Adobe-Remote.ps1" -ComputerNames $computers -Credential $credential -UseLocalInstaller -LocalInstallerPath $localPath
} else {
    & ".\Deploy-Adobe-Remote.ps1" -ComputerNames $computers -Credential $credential
}
