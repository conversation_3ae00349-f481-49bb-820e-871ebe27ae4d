# Essential Programs Installation Script
# Installs common business applications via multiple package managers
# Developed by: The greatest technician that ever lived

param(
    [string]$TargetPC,
    [PSCredential]$Credential,
    [switch]$ListOnly,
    [switch]$AutoInstall,
    [array]$Categories = @("Essential", "Office", "Security", "Utilities", "Healthcare")
)

# Function to display banner
function Show-ProgramsBanner {
    Clear-Host
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "    Essential Programs Installer" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "Developed by: The greatest technician that ever lived" -ForegroundColor Green
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host ""
}

# Define essential programs with multiple installation methods
$EssentialPrograms = @{
    "Essential" = @(
        @{
            Name = "Google Chrome"
            Description = "Web browser"
            Chocolatey = "googlechrome"
            Winget = "Google.Chrome"
            Priority = 1
        },
        @{
            Name = "Mozilla Firefox"
            Description = "Alternative web browser"
            Chocolatey = "firefox"
            Winget = "Mozilla.Firefox"
            Priority = 2
        },
        @{
            Name = "7-Zip"
            Description = "File archiver"
            Chocolatey = "7zip"
            Winget = "7zip.7zip"
            Priority = 1
        },
        @{
            Name = "Adobe Acrobat Reader"
            Description = "PDF reader"
            Chocolatey = "adobereader"
            Winget = "Adobe.Acrobat.Reader.64-bit"
            Priority = 1
        },
        @{
            Name = "VLC Media Player"
            Description = "Media player"
            Chocolatey = "vlc"
            Winget = "VideoLAN.VLC"
            Priority = 2
        }
    )
    
    "Office" = @(
        @{
            Name = "Microsoft Office 365"
            Description = "Office suite"
            Chocolatey = $null
            Winget = "Microsoft.Office"
            Priority = 1
            Notes = "May require organization license"
        },
        @{
            Name = "LibreOffice"
            Description = "Free office suite alternative"
            Chocolatey = "libreoffice-fresh"
            Winget = "TheDocumentFoundation.LibreOffice"
            Priority = 3
        },
        @{
            Name = "Microsoft Teams"
            Description = "Communication platform"
            Chocolatey = "microsoft-teams"
            Winget = "Microsoft.Teams"
            Priority = 1
        },
        @{
            Name = "Zoom"
            Description = "Video conferencing"
            Chocolatey = "zoom"
            Winget = "Zoom.Zoom"
            Priority = 2
        }
    )
    
    "Security" = @(
        @{
            Name = "Windows Defender"
            Description = "Built-in antivirus"
            Chocolatey = $null
            Winget = $null
            Priority = 1
            Notes = "Built into Windows"
        },
        @{
            Name = "Malwarebytes"
            Description = "Anti-malware"
            Chocolatey = "malwarebytes"
            Winget = "Malwarebytes.Malwarebytes"
            Priority = 2
        }
    )
    
    "Utilities" = @(
        @{
            Name = "Notepad++"
            Description = "Advanced text editor"
            Chocolatey = "notepadplusplus"
            Winget = "Notepad++.Notepad++"
            Priority = 1
        },
        @{
            Name = "TreeSize Free"
            Description = "Disk space analyzer"
            Chocolatey = "treesizefree"
            Winget = "JAMSoftware.TreeSize.Free"
            Priority = 2
        },
        @{
            Name = "CCleaner"
            Description = "System cleaner"
            Chocolatey = "ccleaner"
            Winget = "Piriform.CCleaner"
            Priority = 3
        },
        @{
            Name = "WinRAR"
            Description = "Archive manager"
            Chocolatey = "winrar"
            Winget = "RARLab.WinRAR"
            Priority = 3
        }
    )
    
    "Development" = @(
        @{
            Name = "Visual Studio Code"
            Description = "Code editor"
            Chocolatey = "vscode"
            Winget = "Microsoft.VisualStudioCode"
            Priority = 1
        },
        @{
            Name = "Git"
            Description = "Version control"
            Chocolatey = "git"
            Winget = "Git.Git"
            Priority = 1
        },
        @{
            Name = "PowerShell 7"
            Description = "Modern PowerShell"
            Chocolatey = "powershell-core"
            Winget = "Microsoft.PowerShell"
            Priority = 2
        }
    )
    
    "Healthcare" = @(
        @{
            Name = "Citrix Workspace"
            Description = "Remote access client"
            Chocolatey = "citrix-workspace"
            Winget = "Citrix.Workspace"
            Priority = 1
            Notes = "Common in healthcare environments"
        },
        @{
            Name = "Epic Hyperspace"
            Description = "Healthcare EMR"
            Chocolatey = $null
            Winget = $null
            Priority = 1
            Notes = "Requires manual installation from Epic"
        },
        @{
            Name = "Nuance PowerMic"
            Description = "Voice recognition"
            Chocolatey = $null
            Winget = $null
            Priority = 2
            Notes = "Requires manual installation"
        }
    )
}

# Function to display program list
function Show-ProgramList {
    param([array]$Categories)
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "ESSENTIAL PROGRAMS LIST" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    foreach ($category in $Categories) {
        if ($EssentialPrograms.ContainsKey($category)) {
            Write-Host "`n$category Programs:" -ForegroundColor Yellow
            
            $programs = $EssentialPrograms[$category] | Sort-Object Priority
            foreach ($program in $programs) {
                $priorityColor = switch ($program.Priority) {
                    1 { "Green" }
                    2 { "Yellow" }
                    3 { "White" }
                    default { "Gray" }
                }
                
                Write-Host "  [$($program.Priority)] $($program.Name)" -ForegroundColor $priorityColor
                Write-Host "      Description: $($program.Description)" -ForegroundColor Gray
                
                $installMethods = @()
                if ($program.Chocolatey) { $installMethods += "Chocolatey" }
                if ($program.Winget) { $installMethods += "Winget" }
                if ($installMethods.Count -eq 0) { $installMethods += "Manual" }
                
                Write-Host "      Install via: $($installMethods -join ', ')" -ForegroundColor Gray
                
                if ($program.Notes) {
                    Write-Host "      Notes: $($program.Notes)" -ForegroundColor Cyan
                }
                Write-Host ""
            }
        }
    }
    
    Write-Host "Priority Legend:" -ForegroundColor Cyan
    Write-Host "  [1] Essential - Install first" -ForegroundColor Green
    Write-Host "  [2] Important - Install second" -ForegroundColor Yellow
    Write-Host "  [3] Optional - Install if needed" -ForegroundColor White
}

# Function to check package managers
function Test-PackageManagers {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential
    )
    
    $scriptBlock = {
        $managers = @{
            Chocolatey = (Get-Command choco -ErrorAction SilentlyContinue) -ne $null
            Winget = (Get-Command winget -ErrorAction SilentlyContinue) -ne $null
        }
        return $managers
    }
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            return & $scriptBlock
        } else {
            if ($Credential) {
                return Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock
            } else {
                return Invoke-Command -ComputerName $ComputerName -ScriptBlock $scriptBlock
            }
        }
    } catch {
        return @{Chocolatey = $false; Winget = $false}
    }
}

# Function to install program
function Install-Program {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential,
        [hashtable]$Program,
        [hashtable]$AvailableManagers
    )
    
    Write-Host "Installing $($Program.Name)..." -ForegroundColor Yellow
    
    # Choose installation method
    $installMethod = $null
    $packageName = $null
    
    if ($AvailableManagers.Chocolatey -and $Program.Chocolatey) {
        $installMethod = "Chocolatey"
        $packageName = $Program.Chocolatey
    } elseif ($AvailableManagers.Winget -and $Program.Winget) {
        $installMethod = "Winget"
        $packageName = $Program.Winget
    } else {
        Write-Host "  No package manager available for $($Program.Name)" -ForegroundColor Red
        return $false
    }
    
    $scriptBlock = {
        param($method, $package)
        
        try {
            if ($method -eq "Chocolatey") {
                $result = & choco install $package -y 2>&1
                return @{Success = $LASTEXITCODE -eq 0; Output = $result}
            } elseif ($method -eq "Winget") {
                $result = & winget install --id $package --silent --accept-package-agreements --accept-source-agreements 2>&1
                return @{Success = $LASTEXITCODE -eq 0; Output = $result}
            }
        } catch {
            return @{Success = $false; Output = $_.Exception.Message}
        }
    }
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            $result = & $scriptBlock $installMethod $packageName
        } else {
            if ($Credential) {
                $result = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock -ArgumentList $installMethod, $packageName
            } else {
                $result = Invoke-Command -ComputerName $ComputerName -ScriptBlock $scriptBlock -ArgumentList $installMethod, $packageName
            }
        }
        
        if ($result.Success) {
            Write-Host "  Successfully installed $($Program.Name) via $installMethod" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  Failed to install $($Program.Name): $($result.Output)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "  Error installing $($Program.Name): $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
Show-ProgramsBanner

if ($ListOnly) {
    Show-ProgramList -Categories $Categories
    Read-Host "`nPress Enter to exit"
    exit 0
}

if (-not $TargetPC) {
    $TargetPC = Read-Host "Enter the target PC name to install programs on"
}

if (-not $Credential -and $TargetPC -ne $env:COMPUTERNAME -and $TargetPC -ne "localhost" -and $TargetPC -ne ".") {
    Write-Host "Remote computer access detected. Credentials required." -ForegroundColor Yellow
    $Credential = Get-Credential -Message "Enter credentials for remote computer access"
}

Write-Host "Target PC: $TargetPC" -ForegroundColor Cyan
Write-Host "Installation mode: $(if($AutoInstall){'Automatic'}else{'Interactive'})" -ForegroundColor Cyan

# Show program list first
Show-ProgramList -Categories $Categories

# Check package managers
Write-Host "`nChecking package managers on $TargetPC..." -ForegroundColor Cyan
$managers = Test-PackageManagers -ComputerName $TargetPC -Credential $Credential

Write-Host "Available package managers:" -ForegroundColor Yellow
Write-Host "  Chocolatey: $(if($managers.Chocolatey){'Available'}else{'Not Available'})" -ForegroundColor $(if($managers.Chocolatey){'Green'}else{'Red'})
Write-Host "  Winget: $(if($managers.Winget){'Available'}else{'Not Available'})" -ForegroundColor $(if($managers.Winget){'Green'}else{'Red'})

if (-not $managers.Chocolatey -and -not $managers.Winget) {
    Write-Host "`nNo package managers available. Please install Chocolatey or ensure Winget is available." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Install programs by category and priority
$totalInstalled = 0
$totalFailed = 0

foreach ($category in $Categories) {
    if ($EssentialPrograms.ContainsKey($category)) {
        Write-Host "`n=============================================" -ForegroundColor Cyan
        Write-Host "INSTALLING $category PROGRAMS" -ForegroundColor Yellow
        Write-Host "=============================================" -ForegroundColor Cyan
        
        $programs = $EssentialPrograms[$category] | Sort-Object Priority
        
        foreach ($program in $programs) {
            if ($program.Chocolatey -or $program.Winget) {
                if ($AutoInstall -or (Read-Host "Install $($program.Name)? (y/n)") -eq 'y') {
                    $success = Install-Program -ComputerName $TargetPC -Credential $Credential -Program $program -AvailableManagers $managers
                    if ($success) {
                        $totalInstalled++
                    } else {
                        $totalFailed++
                    }
                    Start-Sleep -Seconds 2  # Brief pause between installations
                }
            } else {
                Write-Host "$($program.Name) requires manual installation" -ForegroundColor Yellow
                if ($program.Notes) {
                    Write-Host "  Notes: $($program.Notes)" -ForegroundColor Cyan
                }
            }
        }
    }
}

Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "INSTALLATION SUMMARY" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

Write-Host "Successfully installed: $totalInstalled programs" -ForegroundColor Green
Write-Host "Failed installations: $totalFailed programs" -ForegroundColor Red

Write-Host "`nRecommendations:" -ForegroundColor Cyan
Write-Host "1. Restart the computer to complete installations" -ForegroundColor White
Write-Host "2. Check for any programs that require manual installation" -ForegroundColor White
Write-Host "3. Configure installed programs according to organization policies" -ForegroundColor White
Write-Host "4. Run Windows Update to ensure all components are up to date" -ForegroundColor White

Read-Host "`nPress Enter to exit"
