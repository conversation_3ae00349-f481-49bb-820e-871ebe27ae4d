try {
    $scriptContent = Get-Content "10.ps1" -Raw
    $null = [scriptblock]::Create($scriptContent)
    Write-Host "✅ PowerShell syntax is VALID!" -ForegroundColor Green
    Write-Host "The script should now run without parse errors." -ForegroundColor Cyan
} catch {
    Write-Host "❌ Syntax Error Found:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Yellow
    Write-Host "Error at line: $($_.InvocationInfo.ScriptLineNumber)" -ForegroundColor Red
}
