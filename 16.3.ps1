# Get and store credentials at script start
$script:storedCredentials = $null
$script:targetSession = $null

function Initialize-Credentials {
    $credentialPath = "$env:USERPROFILE\.migration_creds.xml"

    # Try to load saved credentials first
    if (Test-Path $credentialPath) {
        try {
            Write-Color "Found saved credentials. Loading..." "Green"
            $script:storedCredentials = Import-Clixml -Path $credentialPath

            # Test the credentials by trying to access a domain resource
            Write-Color "Testing saved credentials..." "Yellow"
            try {
                # Try a simple domain operation to validate credentials
                Invoke-Command -ComputerName "localhost" -Credential $script:storedCredentials -ScriptBlock { $env:COMPUTERNAME } -ErrorAction Stop | Out-Null
                Write-Color "Saved credentials are valid!" "Green"

                # Ask if user wants to use saved credentials
                $useSaved = Read-Host "Use saved credentials? (Y/N)"
                if ($useSaved -match '^(Y|y)$') {
                    Write-Color "Using saved credentials" "Green"
                    return $true
                } else {
                    Write-Color "Getting new credentials..." "Yellow"
                }
            }
            catch {
                Write-Color "Saved credentials are invalid or expired. Getting new credentials..." "Yellow"
                Remove-Item -Path $credentialPath -Force -ErrorAction SilentlyContinue
            }
        }
        catch {
            Write-Color "Error loading saved credentials. Getting new credentials..." "Yellow"
            Remove-Item -Path $credentialPath -Force -ErrorAction SilentlyContinue
        }
    }

    # Get new credentials
    Write-Color "Initializing credentials for script execution..." "Cyan"
    try {
        $script:storedCredentials = Get-Credential -Message "Enter domain credentials for script operations" -UserName "mclaren\"
        if ($script:storedCredentials) {
            Write-Color "Credentials stored successfully" "Green"

            # Ask if user wants to save credentials
            $saveChoice = Read-Host "Save credentials for future use? (Y/N)"
            if ($saveChoice -match '^(Y|y)$') {
                try {
                    $script:storedCredentials | Export-Clixml -Path $credentialPath -Force
                    Write-Color "Credentials saved securely to: $credentialPath" "Green"
                    Write-Color "Note: Credentials are encrypted and can only be used by your user account on this computer." "Gray"
                }
                catch {
                    Write-Color "Warning: Could not save credentials - $_" "Yellow"
                }
            }
            return $true
        }
        Write-Color "No credentials provided" "Red"
        return $false
    }
    catch {
        Write-Color "Error storing credentials: $_" "Red"
        return $false
    }
}

function Clear-SavedCredentials {
    $credentialPath = "$env:USERPROFILE\.migration_creds.xml"
    if (Test-Path $credentialPath) {
        Remove-Item -Path $credentialPath -Force
        Write-Color "Saved credentials cleared successfully" "Green"
    } else {
        Write-Color "No saved credentials found to clear" "Yellow"
    }
}

function Write-Color {
    param([string]$Message, [string]$Color = "Gray", [switch]$NoNewline)
    if ($NoNewline) { Write-Host $Message -ForegroundColor $Color -NoNewline }
    else { Write-Host $Message -ForegroundColor $Color }
}

# Robust installation function with timeout and retry logic
function Start-ProcessWithTimeout {
    param(
        [string]$FilePath,
        [string]$ArgumentList,
        [int]$TimeoutMinutes = 5,
        [int]$RetryTimeoutMinutes = 4,
        [scriptblock]$SuccessCheck,
        [string]$ProcessName,
        [bool]$NoNewWindow = $true,
        [int]$MaxRetries = 1
    )

    $attempt = 1
    $maxAttempts = $MaxRetries + 1

    while ($attempt -le $maxAttempts) {
        $currentTimeout = if ($attempt -eq 1) { $TimeoutMinutes } else { $RetryTimeoutMinutes }

        Write-Host "Attempt $attempt of $maxAttempts - Starting $ProcessName (Timeout: $currentTimeout minutes)..." -ForegroundColor Yellow

        # Start the process
        $process = Start-Process -FilePath $FilePath -ArgumentList $ArgumentList -PassThru -NoNewWindow:$NoNewWindow

        # Wait for completion or timeout
        $completed = $process.WaitForExit($currentTimeout * 60 * 1000) # Convert minutes to milliseconds

        if ($completed) {
            Write-Host "Process completed within timeout. Exit code: $($process.ExitCode)" -ForegroundColor Green

            # Check if installation was successful using the provided success check
            if ($SuccessCheck) {
                $isSuccessful = & $SuccessCheck
                if ($isSuccessful) {
                    Write-Host "$ProcessName installation verified successful!" -ForegroundColor Green
                    return $true
                } else {
                    Write-Host "Process completed but installation verification failed" -ForegroundColor Yellow
                }
            } else {
                # No success check provided, assume success based on exit code
                if ($process.ExitCode -eq 0) {
                    Write-Host "$ProcessName completed successfully!" -ForegroundColor Green
                    return $true
                } else {
                    Write-Host "Process completed with non-zero exit code: $($process.ExitCode)" -ForegroundColor Yellow
                }
            }
        } else {
            Write-Host "Process timed out after $currentTimeout minutes" -ForegroundColor Yellow

            # Kill the process if it's still running
            try {
                if (-not $process.HasExited) {
                    $process.Kill()
                    $process.WaitForExit(30000) # Wait up to 30 seconds for cleanup
                    Write-Host "Process terminated due to timeout" -ForegroundColor Red
                }
            } catch {
                Write-Host "Warning: Could not terminate process - $_" -ForegroundColor Yellow
            }

            # Check if installation succeeded despite timeout
            if ($SuccessCheck) {
                Write-Host "Checking if $ProcessName installed successfully despite timeout..." -ForegroundColor Cyan
                $isSuccessful = & $SuccessCheck
                if ($isSuccessful) {
                    Write-Host "$ProcessName installation verified successful despite timeout!" -ForegroundColor Green
                    return $true
                } else {
                    Write-Host "Installation verification failed after timeout" -ForegroundColor Red
                }
            }
        }

        # If we're here, the attempt failed
        if ($attempt -lt $maxAttempts) {
            Write-Host "Attempt $attempt failed. Retrying..." -ForegroundColor Yellow
            Start-Sleep -Seconds 10 # Brief pause before retry
        } else {
            Write-Host "All attempts failed. $ProcessName was not installed successfully." -ForegroundColor Red
            return $false
        }

        $attempt++
    }

    return $false
}

# Success check functions for various installations
function Test-DotNet35Installed {
    try {
        $feature = Get-WindowsOptionalFeature -Online -FeatureName "NetFx3" -ErrorAction SilentlyContinue
        return ($feature -and $feature.State -eq "Enabled")
    } catch {
        Write-Host "Warning: Could not check .NET 3.5 status - $_" -ForegroundColor Yellow
        return $false
    }
}

function Test-JavaInstalled {
    try {
        $javaPath = Get-Command "java.exe" -ErrorAction SilentlyContinue
        return ($null -ne $javaPath)
    } catch {
        return $false
    }
}

function Test-ChromeInstalled {
    try {
        $chromePath = "${env:ProgramFiles}\Google\Chrome\Application\chrome.exe"
        $chromePathx86 = "${env:ProgramFiles(x86)}\Google\Chrome\Application\chrome.exe"
        return ((Test-Path $chromePath) -or (Test-Path $chromePathx86))
    } catch {
        return $false
    }
}

function Show-Banner {
    param([string]$Title, [string]$Color = "Cyan")
    $width = 80
    $padding = [math]::Max(0, ($width - $Title.Length - 4) / 2)
    $leftPad = [math]::Floor($padding)
    $rightPad = [math]::Ceiling($padding)
    Write-Host ""
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ("*" + " " * ($width - 2) + "*") -ForegroundColor $Color
    Write-Host ("*" + " " * $leftPad + $Title + " " * $rightPad + "*") -ForegroundColor $Color
    Write-Host ("*" + " " * ($width - 2) + "*") -ForegroundColor $Color
    Write-Host ("*" * $width) -ForegroundColor $Color
    Write-Host ""
}

function Show-Progress {
    param([string]$Message, [int]$Step, [int]$Total, [string]$Color = "Cyan")
    Write-Color "[$Step/$Total] $Message" $Color
}

function Show-Spinner {
    param([string]$Message, [int]$Seconds = 3)
    $spinner = @('|', '/', '-', '\')
    $startTime = Get-Date
    $endTime = $startTime.AddSeconds($Seconds)
    $i = 0
    Write-Host "  " -NoNewline
    while ((Get-Date) -lt $endTime) {
        Write-Host "`r  $($spinner[$i % $spinner.Length]) $Message" -NoNewline -ForegroundColor Cyan
        Start-Sleep -Milliseconds 100
        $i++
    }
    Write-Host "`r  + $Message" -ForegroundColor Green
}
Function Check-PSRemoting {
    param(
        [string]$ComputerName
    )
    $winrm = Get-WmiObject -Class Win32_Service -ComputerName $ComputerName | Where-Object { $_.Name -eq 'WinRM' } -ErrorAction SilentlyContinue 
    if ($winrm.Status -ne "Running") {
        return $false
    } else {
        $firewallRule = Get-NetFirewallRule -DisplayName 'Windows Remote Management (HTTP-In)' -Enabled True -Direction Inbound
        if ($firewallRule -ne $null) {
            return $true
        } else {
            return $false
        }
    }
}  
 
Function Enable-PSRemoting {
    param(
        [string]$ComputerName
    )
    $SessionArgs = @{
        ComputerName = $ComputerName
        SessionOption = New-CimSessionOption -Protocol Dcom
    }
    $MethodArgsEnablePS = @{
        ClassName = 'Win32_Process'
        MethodName = 'Create'
        CimSession = New-CimSession @SessionArgs
        Arguments = @{ CommandLine = 'powershell -Command "Enable-PSRemoting -Force"' }
    }
    Invoke-CimMethod @MethodArgsEnablePS
    $MethodArgsRunWinRM = @{
        ClassName = 'Win32_Process'
        MethodName = 'Create'
        CimSession = New-CimSession @SessionArgs
        Arguments = @{ CommandLine = 'winrm qc' }
    }
 
    Invoke-CimMethod @MethodArgsRunWinRM
 
    Remove-CimSession -CimSession $MethodArgsEnablePS.CimSession
}
 
Function Execute-RemoteCommandWithCheck {
    param(
        [string]$ComputerName,
        [string]$CommandLine
    )
    if (-not (Check-PSRemoting -ComputerName $ComputerName)) {
        try {
        
            Enable-PSRemoting -ComputerName $ComputerName
        }
        catch {
            Write-Error "Failed to enable PS Remoting on $ComputerName`: $_"
            return
        }
    }
    Invoke-RemoteCommand2 -ComputerName $ComputerName -CommandLine $CommandLine
}
Function Invoke-RemoteCommand2 {
    param(
        [string]$ComputerName,
        [string]$CommandLine
    )
 
    #$remoteOutput = Invoke-Command -ComputerName RemoteHost -ScriptBlock { ping somehost.com -t 3>&1 2>&1 } -AsJob
    $remoteOutput = Invoke-Command -ComputerName $ComputerName -ScriptBlock ([scriptblock]::Create($CommandLine)) -AsJob
    while ($remoteOutput.HasMoreData -eq $true) {
#    $remoteOutput | Format-List
        if ($remoteOutput.hasmoredata) {
           Receive-Job -Job $remoteOutput
        }
        Start-Sleep -Milliseconds  200
 
    }
 
}
function Show-CompletionAnimation {
    $colors = @("Red", "Yellow", "Green", "Cyan", "Blue", "Magenta")
    $message = "MIGRATION COMPLETE!"
    1..3 | ForEach-Object { Write-Host "" }
    1..3 | ForEach-Object {
        $dots = "." * $_
        Write-Host "  Finalizing$dots"
        Start-Sleep -Milliseconds 300
    }
    foreach ($color in $colors) {
        Write-Host $message -ForegroundColor $color
        Start-Sleep -Milliseconds 100
    }
    Write-Host ""
}

function Initialize-PSRemoting {
    param([string]$TargetComputer)

    Write-Color "Initializing PSRemoting connection to $TargetComputer..." "Cyan"

    # Check if WinRM service is running
    try {
        $winrm = Get-WmiObject -Class Win32_Service -ComputerName $TargetComputer | Where-Object { $_.Name -eq 'WinRM' }
        if ($winrm.Status -ne "Running") {
            Write-Color "Enabling PSRemoting on $TargetComputer..." "Yellow"
            $SessionArgs = @{
                ComputerName = $TargetComputer
                SessionOption = New-CimSessionOption -Protocol Dcom
            }
            $MethodArgsEnablePS = @{
                ClassName = 'Win32_Process'
                MethodName = 'Create'
                CimSession = New-CimSession @SessionArgs
                Arguments = @{ CommandLine = 'powershell -Command "Enable-PSRemoting -Force"' }
            }
            Invoke-CimMethod @MethodArgsEnablePS
            Start-Sleep -Seconds 10
        }
    }
    catch {
        Write-Color "Warning: Could not check WinRM service status - $_" "Yellow"
    }

    # Create persistent PSSession with enhanced settings
    Write-Color "Creating persistent PSSession..." "Yellow"
    $maxAttempts = 5
    $attempt = 0

    while ($attempt -lt $maxAttempts -and $null -eq $script:targetSession) {
        $attempt++
        try {
            $sessionOption = New-PSSessionOption -IdleTimeout 3600000 -OpenTimeout 60000 -OperationTimeout 300000
            $script:targetSession = New-PSSession -ComputerName $TargetComputer -Credential $script:storedCredentials -SessionOption $sessionOption -ErrorAction Stop
            Write-Color "PSSession established successfully!" "Green"
            break
        }
        catch {
            Write-Color "PSSession creation attempt $attempt failed: $_" "Yellow"
            if ($attempt -lt $maxAttempts) {
                Write-Color "Retrying in 10 seconds..." "Yellow"
                Start-Sleep -Seconds 10
            }
        }
    }

    if ($null -eq $script:targetSession) {
        Write-Color "Failed to create PSSession after $maxAttempts attempts" "Red"
        return $false
    }

    return $true
}

function Test-PSSession {
    if ($null -eq $script:targetSession -or $script:targetSession.State -ne 'Opened') {
        Write-Color "PSSession is not available or has been closed" "Red"
        return $false
    }

    try {
        Invoke-Command -Session $script:targetSession -ScriptBlock { $env:COMPUTERNAME } -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        Write-Color "PSSession test failed: $_" "Yellow"
        return $false
    }
}

function Repair-PSSession {
    param([string]$TargetComputer)

    Write-Color "Attempting to repair PSSession..." "Yellow"

    # Clean up existing session
    if ($null -ne $script:targetSession) {
        try {
            Remove-PSSession $script:targetSession -ErrorAction SilentlyContinue
        }
        catch { }
        $script:targetSession = $null
    }

    # Re-establish connection
    return Initialize-PSRemoting -TargetComputer $TargetComputer
}

function Invoke-RemoteCommand {
    param(
        [scriptblock]$ScriptBlock,
        [object[]]$ArgumentList = @(),
        [string]$TargetComputer,
        [int]$MaxRetries = 2,
        [switch]$UseFallback
    )

    $attempt = 0
    while ($attempt -le $MaxRetries) {
        $attempt++

        # Test session before use
        if (-not (Test-PSSession)) {
            Write-Color "PSSession not available, attempting repair..." "Yellow"
            if (-not [string]::IsNullOrEmpty($TargetComputer) -and -not (Repair-PSSession -TargetComputer $TargetComputer)) {
                Write-Color "Failed to repair PSSession, trying fallback method..." "Yellow"
                break
            } elseif ([string]::IsNullOrEmpty($TargetComputer)) {
                Write-Color "TargetComputer parameter is empty, cannot repair PSSession" "Red"
                break
            }
        }

        try {
            if ($ArgumentList.Count -gt 0) {
                return Invoke-Command -Session $script:targetSession -ScriptBlock $ScriptBlock -ArgumentList $ArgumentList -ErrorAction Stop
            } else {
                return Invoke-Command -Session $script:targetSession -ScriptBlock $ScriptBlock -ErrorAction Stop
            }
        }
        catch {
            Write-Color "Remote command failed (attempt $attempt): $_" "Yellow"
            if ($attempt -le $MaxRetries) {
                Write-Color "Retrying..." "Yellow"
                Start-Sleep -Seconds 5
                # Force session repair on retry
                $script:targetSession = $null
            }
        }
    }

    # Fallback to direct Invoke-Command if PSSession fails
    if ($UseFallback) {
        Write-Color "Using fallback method (direct Invoke-Command)..." "Cyan"
        try {
            if ($ArgumentList.Count -gt 0) {
                return Invoke-Command -ComputerName $TargetComputer -Credential $script:storedCredentials -ScriptBlock $ScriptBlock -ArgumentList $ArgumentList -ErrorAction Stop
            } else {
                return Invoke-Command -ComputerName $TargetComputer -Credential $script:storedCredentials -ScriptBlock $ScriptBlock -ErrorAction Stop
            }
        }
        catch {
            Write-Color "Fallback method also failed: $_" "Red"
            return "Error: Both PSSession and fallback methods failed"
        }
    }

    Write-Color "Remote command failed after $($MaxRetries + 1) attempts" "Red"
    return "Error: Remote command failed after all attempts"
}

function Get-InstallerPaths {
    param([string]$TargetComputer = "")

    # Define all installer paths based on local vs network mode
    if ($script:useLocalInstallers) {
        # Use local D:\ drive paths (these will be used in remote commands on target computer)
        return @{
            Splashtop = "D:\Splashtop"
            DotNetSxs = "D:\sxs"
            LexmarkDriver = "D:\LexmarkDriver"
            Office365 = "D:\o365"
            Citrix = "D:\4.9_LTSR2"
            Nuance = "D:\Nuance"
            Java = "D:\jre1.7.0_45.msi"
            Chrome = "D:\GoogleChromeStandaloneEnterprise64.msi"
            PhotoViewer = "D:\Win_Photo_Viewer.reg"
            VolumeScript = "D:\Volume.ps1"
            BitLockerScript = "D:\BitLockerAD.ps1"
            DellCommandUpdate = "D:\Commandupdate.EXE"
            LexmarkGDI = "D:\LexmarkGDI"
            ZebraDriver = "D:\ZebraDriver"
            CitrixReceiver = "D:\4.9_LTSR2"
            CitrixWorkspace = "D:\2203_LTSR"
            NuanceDrivers = "D:\Nuance121"
            CiscoVPN = "D:\CiscoVPN"
            ImprivataAgent = "D:\ImprivataAgent_x64.msi"
        }
    } else {
        return @{
            Splashtop = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Splashtop"
            DotNetSxs = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\sxs"
            LexmarkDriver = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\LexmarkDriver"
            Office365 = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\o365"
            Citrix = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\4.9_LTSR2"
            Nuance = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Nuance"
            Java = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\jre1.7.0_45.msi"
            Chrome = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\GoogleChromeStandaloneEnterprise64.msi"
            PhotoViewer = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Win_Photo_Viewer.reg"
            VolumeScript = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Volume.ps1"
            BitLockerScript = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\BitLockerAD.ps1"
            DellCommandUpdate = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Commandupdate.EXE"
            LexmarkGDI = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\LexmarkGDI"
            ZebraDriver = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\ZebraDriver"
            CitrixReceiver = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\4.9_LTSR2"
            CitrixWorkspace = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\2203_LTSR"
            NuanceDrivers = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Nuance121"
            CiscoVPN = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\CiscoVPN"
            ImprivataAgent = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\ImprivataAgent_x64.msi"
        }
    }
}

function Copy-FilesToTarget {
    param([string]$TargetComputer)

    Write-Color "Copying installer files to target computer..." "Cyan"
    $installerPaths = Get-InstallerPaths -TargetComputer $TargetComputer



    $copyItems = @(
        @{ Name = "Splashtop"; Source = $installerPaths.Splashtop; Dest = "Splashtop"; IsFolder = $true },
        @{ Name = ".NET 3.5 SXS"; Source = $installerPaths.DotNetSxs; Dest = "sxs"; IsFolder = $true },
        @{ Name = "Lexmark GDI Driver"; Source = $installerPaths.LexmarkGDI; Dest = "Drivers\Print\GDI"; IsFolder = $true },
        @{ Name = "Zebra Driver"; Source = $installerPaths.ZebraDriver; Dest = "Drivers\Print\Zebra"; IsFolder = $true },
        @{ Name = "Citrix Receiver 4.9"; Source = $installerPaths.CitrixReceiver; Dest = "4.9_LTSR2"; IsFolder = $true },
        @{ Name = "Citrix Workspace"; Source = $installerPaths.CitrixWorkspace; Dest = "2203_LTSR"; IsFolder = $true },
        @{ Name = "Nuance Drivers"; Source = $installerPaths.NuanceDrivers; Dest = "Nuance121"; IsFolder = $true },
        @{ Name = "Cisco VPN"; Source = $installerPaths.CiscoVPN; Dest = "CiscoVPN"; IsFolder = $true },

        @{ Name = "Java"; Source = $installerPaths.Java; Dest = "jre1.7.0_45.msi" },
        @{ Name = "Chrome"; Source = $installerPaths.Chrome; Dest = "GoogleChromeStandaloneEnterprise64.msi" },
        @{ Name = "Photo Viewer"; Source = $installerPaths.PhotoViewer; Dest = "Win_Photo_Viewer.reg" },
        @{ Name = "Volume Script"; Source = $installerPaths.VolumeScript; Dest = "Volume.ps1" },
        @{ Name = "BitLocker Script"; Source = $installerPaths.BitLockerScript; Dest = "BitLockerAD.ps1" },
        @{ Name = "Dell Command Update"; Source = $installerPaths.DellCommandUpdate; Dest = "dcu\Commandupdate.EXE" },
        @{ Name = "Imprivata Agent"; Source = $installerPaths.ImprivataAgent; Dest = "ImprivataAgent_x64.msi" }
    )

    foreach ($item in $copyItems) {
        Write-Color "Copying $($item.Name)" "Cyan"
        try {
            if ($script:useLocalInstallers) {
                # Use remote command to copy from local D:\ drive on target computer
                $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
                    param($SourcePath, $DestPath, $IsFolder)

                    if (Test-Path $SourcePath) {
                        # Create destination directory
                        $destFolder = if ($IsFolder) { $DestPath } else { Split-Path $DestPath -Parent }
                        if (-not (Test-Path $destFolder)) {
                            New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
                        }

                        if ($IsFolder) {
                            # Copy folder contents using robocopy
                            $result = Start-Process -FilePath "robocopy" -ArgumentList "`"$SourcePath`"", "`"$DestPath`"", "/E", "/R:3", "/W:10" -Wait -PassThru -NoNewWindow
                            if ($result.ExitCode -le 7) {
                                return "Success"
                            } else {
                                return "Warning: Exit code $($result.ExitCode)"
                            }
                        } else {
                            # Copy single file
                            Copy-Item -Path $SourcePath -Destination $DestPath -Force
                            return "Success"
                        }
                    } else {
                        return "Error: Source not found at $SourcePath"
                    }
                } -ArgumentList $item.Source, "C:\Files\$($item.Dest)", $item.IsFolder

                if ($result -like "Success*") {
                    Write-Color "Success: $($item.Name)" "Green"
                } else {
                    Write-Color "Failed: $($item.Name) - $result" "Red"
                }
            } else {
                # Network mode - use original Copy-Item method
                if ($item.IsFolder) {
                    # Create destination path with correct folder name
                    $destPath = "\\$TargetComputer\c$\Files\$($item.Dest)"

                    # Ensure the full directory path exists (including nested folders)
                    if (-not (Test-Path $destPath)) {
                        try {
                            # Use New-Item with -Force to create the entire path at once
                            $null = New-Item -Path $destPath -ItemType Directory -Force -ErrorAction Stop
                        }
                        catch {
                            throw "Could not create destination directory: $destPath - $($_.Exception.Message)"
                        }
                    }

                    # Copy contents to the correctly named folder
                    if (Test-Path $item.Source) {
                        Copy-Item -Path "$($item.Source)\*" -Destination $destPath -Recurse -Force -ErrorAction Stop
                    } else {
                        throw "Source path not found: $($item.Source)"
                    }
                } else {
                    # Handle single file copy
                    $destPath = "\\$TargetComputer\c$\Files\$($item.Dest)"
                    $destFolder = Split-Path $destPath -Parent

                    # Ensure the destination folder exists
                    if (-not (Test-Path $destFolder)) {
                        New-Item -Path $destFolder -ItemType Directory -Force -ErrorAction Stop | Out-Null
                    }

                    Copy-Item -Path $item.Source -Destination $destPath -Force -ErrorAction Stop
                }
                Write-Color "Success: $($item.Name)" "Green"
            }
        }
        catch {
            Write-Color "Failed: $($item.Name) - $_" "Red"
        }
    }
}

function Install-ImprivataAgent {
    param([string]$TargetComputer, [int]$AgentType, [string]$SourceComputer)

    Write-Color "Installing Imprivata Agent Type $AgentType..." "Cyan"

    # For Type 2, get stored credentials from source computer
    $AutoUsername = $null
    $AutoPassword = $null

    if ($AgentType -eq 2 -and $SourceComputer) {
        Write-Color "Retrieving autologin credentials from source computer..." "Cyan"
        try {
            # Get stored credentials from source
            $sourceCreds = Invoke-Command -ComputerName $SourceComputer -Credential $script:storedCredentials -ScriptBlock {
                $regPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon"
                if (Test-Path $regPath) {
                    $props = Get-ItemProperty -Path $regPath
                    [PSCustomObject]@{
                        UserName = $props.DefaultUserName
                        Password = $props.DefaultPassword
                    }
                }
            }

            if ($sourceCreds.UserName -and $sourceCreds.Password) {
                Write-Color "  Found stored credentials on source computer" "Green"
                $AutoUsername = $sourceCreds.UserName
                $AutoPassword = ConvertTo-SecureString $sourceCreds.Password -AsPlainText -Force
            } else {
                Write-Color "  No stored credentials found" "Yellow"
                $AutoUsername = Read-Host "Enter Imprivata autologin username"
                $AutoPassword = Read-Host "Enter Imprivata autologin password" -AsSecureString
            }
        } catch {
            Write-Color "  Error retrieving credentials from source: $_" "Yellow"
            $AutoUsername = Read-Host "Enter Imprivata autologin username"
            $AutoPassword = Read-Host "Enter Imprivata autologin password" -AsSecureString
        }
    }

    # Copy Imprivata MSI file first
    if ($script:useLocalInstallers) {
        $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
            $sourcePath = "D:\ImprivataAgent_x64.msi"
            $destPath = "C:\Files\ImprivataAgent_x64.msi"

            if (Test-Path $sourcePath) {
                # Create destination directory
                $destFolder = Split-Path $destPath -Parent
                if (-not (Test-Path $destFolder)) {
                    New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
                }

                # Copy MSI file
                Copy-Item -Path $sourcePath -Destination $destPath -Force
                return "Success: Imprivata Agent MSI copied from local D:\ drive"
            } else {
                return "Error: Imprivata Agent MSI not found at $sourcePath"
            }
        }
        Write-Color $result "Gray"
    } else {
        # Network mode - copy MSI from network location
        try {
            $sourceMSI = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\ImprivataAgent_x64.msi"
            $destMSI = "\\$TargetComputer\C$\Files\ImprivataAgent_x64.msi"

            # Create destination directory
            $destFolder = Split-Path $destMSI -Parent
            if (-not (Test-Path $destFolder)) {
                New-Item -Path $destFolder -ItemType Directory -Force | Out-Null
            }

            Copy-Item -Path $sourceMSI -Destination $destMSI -Force
            Write-Color "Success: Imprivata Agent MSI copied from network" "Gray"
        }
        catch {
            Write-Color "Error: Failed to copy Imprivata MSI - $_" "Red"
            return
        }
    }

    # Install Imprivata with verification
    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($AgentType)

        Write-Host "Installing Imprivata Agent Type $AgentType..." -ForegroundColor Green

        # Remove any existing Imprivata installations first
        Write-Host "Removing any existing Imprivata installations..." -ForegroundColor Yellow
        try {
            Start-Process -FilePath "MsiExec.exe" -ArgumentList "/X{E8A87655-4D4C-4ADF-A097-D4FBBB95CA42} /q REBOOT=REALLYSUPPRESS" -Wait -NoNewWindow -ErrorAction SilentlyContinue
            Start-Process -FilePath "MsiExec.exe" -ArgumentList "/X{26B7B974-0DE7-4CD2-93D7-B3D9CBFC91B2} /q REBOOT=REALLYSUPPRESS" -Wait -NoNewWindow -ErrorAction SilentlyContinue
            Write-Host "Old Imprivata installations removed" -ForegroundColor Green
        }
        catch {
            Write-Host "Note: No existing Imprivata installations found to remove" -ForegroundColor Gray
        }

        # Check if Imprivata MSI exists
        $imprivataAgent = "C:\Files\ImprivataAgent_x64.msi"

        if (Test-Path $imprivataAgent) {
            Write-Host "Installing Imprivata Agent Type $AgentType..." -ForegroundColor Yellow
            try {
                # Use the exact command provided for installation
                $arguments = "/i `"$imprivataAgent`" /q /norestart AGENTTYPE=$AgentType IPTXPRIMSERVER=https://mhc-lxasimp01.mclaren.org/sso/servlet/messagerouter DOMAIN=MCLAREN"
                $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $arguments -Wait -NoNewWindow -PassThru

                Write-Host "Installation process completed with exit code: $($process.ExitCode)" -ForegroundColor Gray

                # Wait for installation to complete
                Write-Host "Waiting for Imprivata installation to complete..." -ForegroundColor Yellow
                Start-Sleep -Seconds 15

                # Verify installation
                $installed = $false
                $maxAttempts = 6
                $attempt = 0

                while (-not $installed -and $attempt -lt $maxAttempts) {
                    $attempt++
                    Write-Host "Verification attempt $attempt of $maxAttempts..." -ForegroundColor Gray

                    # Check for Imprivata service
                    $imprivataService = Get-Service -Name "*Imprivata*" -ErrorAction SilentlyContinue

                    # Check for Imprivata processes
                    $imprivataProcess = Get-Process -Name "*Imprivata*" -ErrorAction SilentlyContinue

                    # Check for Imprivata registry entries
                    $imprivataRegistry = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*" -ErrorAction SilentlyContinue | Where-Object { $_.DisplayName -like "*Imprivata*" }

                    if ($imprivataService -or $imprivataProcess -or $imprivataRegistry) {
                        $installed = $true
                        Write-Host "Imprivata Agent Type $AgentType installation verified successfully!" -ForegroundColor Green

                        if ($imprivataService) {
                            Write-Host "  Found Imprivata service: $($imprivataService.Name)" -ForegroundColor Gray
                        }
                        if ($imprivataProcess) {
                            Write-Host "  Found Imprivata process: $($imprivataProcess.Name -join ', ')" -ForegroundColor Gray
                        }
                        if ($imprivataRegistry) {
                            Write-Host "  Found Imprivata in registry: $($imprivataRegistry.DisplayName)" -ForegroundColor Gray
                        }
                        return "Success: Imprivata Agent Type $AgentType installed and verified"
                    } else {
                        Write-Host "  Imprivata not detected yet, waiting 10 seconds..." -ForegroundColor Yellow
                        Start-Sleep -Seconds 10
                    }
                }

                if (-not $installed) {
                    Write-Host "Warning: Could not verify Imprivata installation after $($maxAttempts * 10) seconds" -ForegroundColor Red
                    Write-Host "Installation may still be in progress or may have failed" -ForegroundColor Yellow
                    return "Warning: Imprivata installation could not be verified"
                }
            }
            catch {
                Write-Host "Error: Failed to install Imprivata Agent Type $AgentType - $_" -ForegroundColor Red
                return "Error: Installation failed - $_"
            }
        } else {
            Write-Host "Error: ImprivataAgent_x64.msi not found at $imprivataAgent" -ForegroundColor Red

            # List available files for troubleshooting
            Write-Host "Available files in C:\Files\:" -ForegroundColor Yellow
            if (Test-Path "C:\Files\") {
                Get-ChildItem -Path "C:\Files\" -Filter "*.msi" | ForEach-Object {
                    Write-Host "  $($_.Name)" -ForegroundColor Gray
                }
            }
            return "Error: Imprivata MSI file not found"
        }
    } -ArgumentList $AgentType

    Write-Color $result "Gray"

    # Configure Type 2 autologin settings if needed
    if ($AgentType -eq 2 -and $AutoUsername -and $AutoPassword) {
        Write-Color "Configuring Type 2 autologin settings..." "Cyan"

        # Convert secure string to plain text for registry
        $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($AutoPassword)
        $PlainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
        [Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)

        $configResult = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
            param($Username, $Password)

            try {
                # Configure Winlogon settings
                $winlogonPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon"
                Set-ItemProperty -Path $winlogonPath -Name "DefaultUserName" -Value $Username
                Set-ItemProperty -Path $winlogonPath -Name "DefaultDomainName" -Value "MCLAREN"
                Set-ItemProperty -Path $winlogonPath -Name "DefaultPassword" -Value $Password
                Set-ItemProperty -Path $winlogonPath -Name "AutoAdminLogon" -Value "1"
                Set-ItemProperty -Path $winlogonPath -Name "ForceAutoLogon" -Value "1"

                # Configure System Policies
                Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "DontDisplayLastUsername" -Value 1 -Type DWord

                # Configure SSO Provider
                $ssoPath = "HKLM:\SOFTWARE\SSOProvider\ISXAgent"
                if (-not (Test-Path $ssoPath)) {
                    New-Item -Path $ssoPath -Force | Out-Null
                }
                Set-ItemProperty -Path $ssoPath -Name "Type" -Value 2 -Type DWord

                Write-Host "Type 2 autologin configuration completed successfully" -ForegroundColor Green
                return "Success: Type 2 autologin configured"
            } catch {
                Write-Host "Error configuring Type 2 autologin: $_" -ForegroundColor Red
                return "Error: Type 2 autologin configuration failed - $_"
            }
        } -ArgumentList $AutoUsername, $PlainPassword

        Write-Color $configResult "Gray"

        # Clear sensitive data
        Remove-Variable -Name AutoUsername, PlainPassword -ErrorAction SilentlyContinue
        [GC]::Collect()
    }
}

function Install-LexmarkDriver {
    param([string]$TargetComputer)

    Write-Color "Installing Lexmark Universal Driver..." "Cyan"

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($UseLocalInstallers)

        # Install Lexmark GDI driver - files should already be copied by Copy-FilesToTarget function
        $gdiDriverPath = "C:\Files\Drivers\Print\GDI\LMUD1n40.inf"

        if (Test-Path $gdiDriverPath) {
            try {
                Write-Host "Installing Lexmark Universal driver..." -ForegroundColor Yellow

                # Install the GDI driver using pnputil
                Pnputil /add-driver $gdiDriverPath /install | Out-Null

                # Add the Lexmark printer driver
                Add-PrinterDriver -Name "Lexmark Universal v2 PostScript 3 Emulation" -ErrorAction Stop

                Write-Host "Lexmark Universal driver installed successfully" -ForegroundColor Green

                # Clean up GDI driver files (keep Zebra drivers for printer migration)
                Remove-Item -Path "C:\Files\Drivers\Print\GDI" -Recurse -Force -ErrorAction SilentlyContinue
                Write-Host "Lexmark driver files cleaned up (Zebra drivers preserved)." -ForegroundColor Green

                return "Success: Lexmark Universal driver installed"
            }
            catch {
                Write-Host "Warning: Failed to install Lexmark driver - $_" -ForegroundColor Yellow
                return "Warning: Lexmark driver installation failed - $_"
            }
        } else {
            Write-Host "Warning: Lexmark GDI driver file not found at $gdiDriverPath" -ForegroundColor Yellow

            # List available files for troubleshooting
            Write-Host "Available files in C:\Files:" -ForegroundColor Yellow
            if (Test-Path "C:\Files\") {
                Get-ChildItem -Path "C:\Files\" -Recurse -Filter "*.inf" | ForEach-Object {
                    Write-Host "  $($_.FullName)" -ForegroundColor Gray
                }
            }
            return "Warning: Lexmark driver file not found"
        }
    } -ArgumentList $script:useLocalInstallers

    Write-Color $result "Gray"
}

function Install-SpecificZebraDriver {
    param([string]$TargetComputer, [string]$DriverName)

    Write-Color "Installing specific Zebra driver: $DriverName" "Yellow"

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($RequestedDriverName)

        $zebraDriverPath = "C:\Files\Drivers\Print\Zebra"
        $zebraInfFile = "$zebraDriverPath\ZDesigner.inf"

        if (Test-Path $zebraInfFile) {
            try {
                # First, ensure the INF is installed in the driver store
                $pnpResult = & pnputil /add-driver $zebraInfFile /install 2>&1

                # Try to add the specific driver
                Add-PrinterDriver -Name $RequestedDriverName -ErrorAction Stop
                Write-Host "Successfully installed Zebra driver: $RequestedDriverName" -ForegroundColor Green
                return "Success: $RequestedDriverName driver installed"
            }
            catch {
                # If the exact driver name fails, try to find a similar one
                Write-Host "Exact driver name failed, searching for similar drivers..." -ForegroundColor Yellow

                # Parse INF file for available drivers
                $infContent = Get-Content $zebraInfFile -ErrorAction SilentlyContinue
                $availableDrivers = @()

                foreach ($line in $infContent) {
                    if ($line -match 'ZDesigner' -and $line -match '".*"') {
                        # Extract driver name from quoted string
                        if ($line -match '"([^"]*)"') {
                            $availableDrivers += $matches[1]
                        }
                    }
                }

                # Look for a driver that contains key parts of the requested name
                $matchingDriver = $null
                $requestedParts = $RequestedDriverName -split '\s+|[-_]'

                foreach ($driver in $availableDrivers) {
                    $matchCount = 0
                    foreach ($part in $requestedParts) {
                        if ($driver -like "*$part*") {
                            $matchCount++
                        }
                    }
                    if ($matchCount -ge 2) {  # At least 2 parts match
                        $matchingDriver = $driver
                        break
                    }
                }

                if ($matchingDriver) {
                    try {
                        Add-PrinterDriver -Name $matchingDriver -ErrorAction Stop
                        Write-Host "Installed similar driver: $matchingDriver" -ForegroundColor Green
                        return "Success: Installed $matchingDriver (similar to $RequestedDriverName)"
                    }
                    catch {
                        Write-Host "Failed to install similar driver: $matchingDriver" -ForegroundColor Yellow
                    }
                }

                # If no match found, install a generic ZDesigner driver
                $genericDrivers = @("ZDesigner Generic Text Driver", "ZDesigner GK420d (EPL)", "ZDesigner ZT220 (ZPL)")
                foreach ($genericDriver in $genericDrivers) {
                    if ($availableDrivers -contains $genericDriver) {
                        try {
                            Add-PrinterDriver -Name $genericDriver -ErrorAction Stop
                            Write-Host "Installed generic driver: $genericDriver" -ForegroundColor Green
                            return "Success: Installed $genericDriver (generic fallback for $RequestedDriverName)"
                        }
                        catch {
                            continue
                        }
                    }
                }

                return "Warning: Could not install $RequestedDriverName or find suitable alternative"
            }
        } else {
            return "Error: Zebra driver files not found at $zebraInfFile"
        }
    } -ArgumentList $DriverName

    Write-Color $result "Gray"
    return $result
}

function Install-CitrixSoftware {
    param([string]$TargetComputer, [string]$InstallType)

    Write-Color "Installing Citrix $InstallType..." "Cyan"

    # Install Citrix and Nuance
    $installResult = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($InstallType)

        try {
            # Check if Citrix is already installed
            Write-Host "Checking for existing Citrix installations..." -ForegroundColor Yellow

            $citrixInstalled = $false
            $installedPrograms = Get-WmiObject -Class Win32_Product | Where-Object {
                $_.Name -like "*Citrix*" -and ($_.Name -like "*Workspace*" -or $_.Name -like "*Receiver*")
            }

            if ($installedPrograms) {
                Write-Host "Found existing Citrix installation(s):" -ForegroundColor Yellow
                $installedPrograms | ForEach-Object {
                    Write-Host "  - $($_.Name) (Version: $($_.Version))" -ForegroundColor Gray
                }
                $citrixInstalled = $true
            } else {
                Write-Host "No existing Citrix installation found" -ForegroundColor Green
            }

            # Determine correct paths and executables based on install type
            if ($InstallType -eq "Receiver") {
                $citrixPath = "C:\Files\4.9_LTSR2"
                $citrixExecutable = "$citrixPath\CitrixReceiver"
                $productName = "Citrix Receiver 4.9"
            } else {
                $citrixPath = "C:\Files\2203_LTSR"
                $citrixExecutable = "$citrixPath\CitrixWorkspaceApp.exe"
                $productName = "Citrix Workspace"
            }

            if (-not $citrixInstalled) {
                Write-Host "Installing $productName..." -ForegroundColor Yellow

                # Check if installer exists
                if (Test-Path $citrixExecutable) {
                    # Run Citrix cleanup utility first
                    $cleanupUtility = "$citrixPath\ReceiverCleanupUtility.exe"
                    if (Test-Path $cleanupUtility) {
                        Write-Host "Running Citrix cleanup utility..." -ForegroundColor Gray
                        $cleanupProcess = Start-Process -FilePath $cleanupUtility -ArgumentList "/silent", "/disableCEIP" -Wait -PassThru -NoNewWindow
                        Write-Host "Cleanup completed with exit code: $($cleanupProcess.ExitCode)" -ForegroundColor Gray

                        # Clean up logs
                        Remove-Item -Path "$citrixPath\ReceiverCleanupUtilityLogs" -Recurse -Force -ErrorAction SilentlyContinue
                        Remove-Item -Path "$citrixPath\config.xml" -Force -ErrorAction SilentlyContinue
                    }

                    # Install Citrix
                    if ($InstallType -eq "Receiver") {
                        $arguments = "/silent", "/includeSSON", "/ALLOWADDSTORE=N", "/AutoUpdateCheck=DISABLED", "/noreboot", "/EnableCEIP=false"
                    } else {
                        $arguments = "/silent", "/forceinstall", "/includeSSON", "/ALLOWADDSTORE=N", "/AutoUpdateCheck=DISABLED", "/noreboot", "/EnableCEIP=false"
                    }

                    $process = Start-Process -FilePath $citrixExecutable -ArgumentList $arguments -Wait -PassThru -NoNewWindow
                    Write-Host "$productName installation completed with exit code: $($process.ExitCode)" -ForegroundColor Green

                    # Check for successful installation
                    if ($process.ExitCode -eq 0) {
                        Write-Host "$productName installed successfully" -ForegroundColor Green
                    } elseif ($process.ExitCode -eq 255) {
                        Write-Host "Warning: Exit code 255 - Software may already be installed or installation was skipped" -ForegroundColor Yellow
                    } else {
                        Write-Host "Warning: Installation completed with exit code: $($process.ExitCode)" -ForegroundColor Yellow
                    }
                } else {
                    Write-Host "Warning: Citrix installer not found at $citrixExecutable" -ForegroundColor Yellow
                    return "Warning: Citrix installer not found"
                }
            } else {
                Write-Host "$productName is already installed - skipping installation" -ForegroundColor Yellow
            }

            # Remove old Nuance installations
            Write-Host "Removing old Nuance drivers..." -ForegroundColor Yellow
            $uninstallCommands = @(
                "MsiExec.exe /Q /X{0F041170-C110-40E4-A157-72B53B803A4B} /norestart",
                "MsiExec.exe /Q /X{227EFA98-EF53-447D-9750-8A3B946950EB} /norestart",
                "MsiExec.exe /Q /X{08D5C615-899A-4808-A484-0099A28BDCAB} /norestart",
                "MsiExec.exe /Q /X{ED9F4A9B-D582-4754-808D-D09D4DC2856D} /norestart"
            )

            foreach ($command in $uninstallCommands) {
                Start-Process -FilePath "cmd.exe" -ArgumentList "/c $command" -Wait -NoNewWindow -ErrorAction SilentlyContinue
            }

            # Install new Nuance drivers
            Write-Host "Installing Nuance drivers..." -ForegroundColor Yellow
            $nuanceMsi1 = "C:\Files\Nuance121\Nuance Citrix Client Audio Extension.msi"
            $nuanceMsi2 = "C:\Files\Nuance121\Nuance PowerMic Citrix Client Extension.msi"

            if (Test-Path $nuanceMsi1) {
                $process = Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$nuanceMsi1`" /qn /norestart" -Wait -PassThru -NoNewWindow
                Write-Host "Nuance Audio Extension installed with exit code: $($process.ExitCode)" -ForegroundColor Green
            }

            if (Test-Path $nuanceMsi2) {
                $process = Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$nuanceMsi2`" /qn /norestart" -Wait -PassThru -NoNewWindow
                Write-Host "Nuance PowerMic Extension installed with exit code: $($process.ExitCode)" -ForegroundColor Green
            }

            return "Success: Citrix $InstallType and Nuance drivers processed successfully"
        }
        catch {
            return "Error: Installation failed - $_"
        }
    } -ArgumentList $InstallType

    Write-Color $installResult "Gray"
    return $installResult
}

function Install-VPNClient {
    param([string]$TargetComputer)

    Write-Color "Installing Cisco Secure Client VPN..." "Cyan"

    # Get installer paths
    $installerPaths = Get-InstallerPaths -TargetComputer $TargetComputer

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($UseLocalInstallers, $CiscoVPNPath)

        Write-Host "*** Installing Cisco Secure Client VPN"
        $netInstallerPath = $CiscoVPNPath
        $remoteFolderPath = "C:\Files\CiscoVPN"
        $remoteScriptPath = "$remoteFolderPath\Deploy-Application.ps1"

        # Create the destination directory
        if (-Not (Test-Path -Path $remoteFolderPath)) {
            New-Item -ItemType Directory -Path $remoteFolderPath -Force | Out-Null
            Write-Host "Created directory: $remoteFolderPath" -ForegroundColor Green
        }

        # Copy installer files
        if ($UseLocalInstallers) {
            # Copy from local D:\ drive
            $localSourcePath = "D:\CiscoVPN"
            if (Test-Path $localSourcePath) {
                Write-Host "Copying VPN installer from local D:\ drive..." -ForegroundColor Yellow
                $result = Start-Process -FilePath "robocopy" -ArgumentList "`"$localSourcePath`"", "`"$remoteFolderPath`"", "/E", "/R:3", "/W:10" -Wait -PassThru -NoNewWindow
                if ($result.ExitCode -le 7) {
                    Write-Host "VPN installer files copied successfully from local D:\ drive" -ForegroundColor Green
                } else {
                    Write-Host "Warning: VPN installer copy completed with exit code: $($result.ExitCode)" -ForegroundColor Yellow
                }
            } else {
                Write-Host "Warning: VPN installer source not found at $localSourcePath" -ForegroundColor Yellow
                return "Warning: VPN installer files not found on local D:\ drive"
            }
        } else {
            # Copy from network path
            Write-Host "Copying VPN installer from network path..." -ForegroundColor Yellow
            try {
                $result = Start-Process -FilePath "robocopy" -ArgumentList "`"$netInstallerPath`"", "`"$remoteFolderPath`"", "/E", "/R:3", "/W:10" -Wait -PassThru -NoNewWindow
                if ($result.ExitCode -le 7) {
                    Write-Host "VPN installer files copied successfully from network" -ForegroundColor Green
                } else {
                    Write-Host "Warning: VPN installer copy completed with exit code: $($result.ExitCode)" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "Error copying from network: $_" -ForegroundColor Red
                return "Error: Failed to copy VPN installer from network"
            }
        }

        # Execute the installation script
        if (Test-Path $remoteScriptPath) {
            Write-Host "Executing VPN installation script..." -ForegroundColor Yellow
            try {
                Start-Process -FilePath "powershell.exe" -ArgumentList "-ExecutionPolicy Bypass -File `"$remoteScriptPath`" -DeployMode Silent" -Wait -NoNewWindow
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "Cisco Secure Client VPN installed successfully" -ForegroundColor Green
                } else {
                    Write-Host "VPN installation completed with exit code: $LASTEXITCODE" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "Error during VPN installation: $_" -ForegroundColor Red
                return "Error: VPN installation failed"
            }
        } else {
            Write-Host "Warning: VPN installation script not found at $remoteScriptPath" -ForegroundColor Yellow
            return "Warning: VPN installation script not found"
        }



        return "Success: Cisco Secure Client VPN installed"

    } -ArgumentList $script:useLocalInstallers, $installerPaths.CiscoVPN

    Write-Color $result "Gray"
}

function Install-OfficeApplication {
    param([string]$TargetComputer, [array]$SourceGroups)

    Write-Color "Checking Office 365 installation requirements..." "Cyan"

    # Determine Imprivata configuration from source groups
    $installType2 = $SourceGroups | Where-Object { $_ -like "*type2*"}

    # Check if Type 2 (Type 2 should NOT get Office)
    if ($installType2) {
        Write-Color "Type 2 membership detected - Office 365 will NOT be installed" "Yellow"
        return
    }

    # For Type 1 or no Imprivata, ask about Office installation
    $installOffice = $false
    if (-not $script:autoYes) {
        $officeChoice = Read-Host "Do you want to install Office 365? (Y/N)"
        $installOffice = $officeChoice -match '^(Y|y)$'
    } else {
        $installOffice = $true  # Auto-yes mode installs Office for non-Type 2
    }

    if (-not $installOffice) {
        Write-Color "Office 365 installation skipped by user choice" "Yellow"
        return
    }

    Write-Color "Office 365 will be installed" "Green"

    # Copy Office 365 files first
    Write-Color "Copying Office 365 installer files..." "Cyan"
    $installerPaths = Get-InstallerPaths -TargetComputer $TargetComputer

    try {
        if ($script:useLocalInstallers) {
            # Use remote command to copy from local D:\ drive on target computer
            $copyResult = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
                $sourcePath = "D:\o365"
                $destPath = "C:\Files\o365"

                if (Test-Path $sourcePath) {
                    # Create destination directory
                    if (-not (Test-Path $destPath)) {
                        New-Item -Path $destPath -ItemType Directory -Force | Out-Null
                    }

                    # Copy folder contents using robocopy
                    $result = Start-Process -FilePath "robocopy" -ArgumentList "`"$sourcePath`"", "`"$destPath`"", "/E", "/R:3", "/W:10" -Wait -PassThru -NoNewWindow
                    if ($result.ExitCode -le 7) {
                        return "Success: Office 365 files copied from local D:\ drive"
                    } else {
                        return "Warning: Office 365 copy completed with exit code $($result.ExitCode)"
                    }
                } else {
                    return "Error: Office 365 source not found at $sourcePath"
                }
            }
            Write-Color $copyResult "Gray"
        } else {
            # Network mode - copy from network location
            $destPath = "\\$TargetComputer\c$\Files\o365"
            if (-not (Test-Path $destPath)) {
                New-Item -Path $destPath -ItemType Directory -Force | Out-Null
            }
            Copy-Item -Path "$($installerPaths.Office365)\*" -Destination $destPath -Recurse -Force
            Write-Color "Success: Office 365 files copied from network" "Gray"
        }
    } catch {
        Write-Color "Error copying Office 365 files: $_" "Red"
        Write-Color "Office 365 installation will be skipped due to file copy failure" "Yellow"
        return
    }

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        Write-Host "*** Install Office 365"
        Write-Host "*** Running Office 365 installation"
        if (Test-Path "C:\Files\o365\batch_365.bat") {
            $p = Start-Process -FilePath "C:\Files\o365\batch_365.bat" -PassThru
            $p.WaitForExit()
            Write-Host "*** Deleting C:\Files\o365 folder"
            Remove-Item -Path "C:\Files\o365" -Recurse -Force

            # Office Ribbon Fix
            Write-Host "***" -ForegroundColor Green
            Write-Output "Office ribbon key fix being applied"
            $RegistryPath = "HKCU:\Software\Microsoft\Office\16.0\Common\ExperimentConfigs\ExternalFeatureOverrides\word"
            $RegistryName = "Microsoft.Office.UXPlatform.FluentSVRefresh"
            $RegistryValue = "false"
            if (-not (Test-Path $RegistryPath)) {
                New-Item -Path $RegistryPath -Force | Out-Null
            }
            Set-ItemProperty -Path $RegistryPath -Name $RegistryName -Value $RegistryValue
            Write-Output "Registry key '$RegistryName' set to '$RegistryValue' in path '$RegistryPath'."
            Write-Host "Office ribbon key and value have been set successfully."

            return "Success: Office 365 installed"
        } else {
            Write-Host "Warning: Office batch file not found at C:\Files\o365\batch_365.bat" -ForegroundColor Yellow
            return "Warning: Office 365 installer not found"
        }
    }

    Write-Color $result "Gray"
}

function Install-DellCommandUpdate {
    param([string]$TargetComputer)

    Write-Color "Installing and configuring Dell Command Update..." "Cyan"

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($UseLocalInstallers)

        Write-Host "*** Installing Dell Command Update"
        $DellCommandUpdatePath = "C:\Program Files (x86)\Dell\CommandUpdate\dcu-cli.exe"
        $DellCommandUpdatePath2 = "C:\Program Files\Dell\CommandUpdate\dcu-cli.exe"
        $BiosPassword = "B1TuSer"
        $targetDirectory = "C:\Files\dcu"

        # Create target directory
        if (-not (Test-Path -Path $targetDirectory)) {
            New-Item -Path $targetDirectory -ItemType Directory -Force
        }

        # Copy installer file if using local installers
        if ($UseLocalInstallers) {
            if (Test-Path "D:\Commandupdate.EXE") {
                Copy-Item -Path "D:\Commandupdate.EXE" -Destination "$targetDirectory\Commandupdate.EXE" -Force
                Write-Host "Dell Command Update installer copied from D:\ drive" -ForegroundColor Green
            } elseif (Test-Path "D:\dcu\Commandupdate.EXE") {
                Copy-Item -Path "D:\dcu\Commandupdate.EXE" -Destination "$targetDirectory\Commandupdate.EXE" -Force
                Write-Host "Dell Command Update installer copied from D:\dcu\ drive" -ForegroundColor Green
            } else {
                Write-Host "Warning: Dell Command Update installer not found at D:\Commandupdate.EXE or D:\dcu\Commandupdate.EXE" -ForegroundColor Yellow
            }
        }

        # Install Dell Command Update
        $InstallerFile = Join-Path $targetDirectory "Commandupdate.EXE"
        if (Test-Path $InstallerFile) {
            Write-Host "Installing Dell Command Update..."
            Start-Process -FilePath $InstallerFile -ArgumentList "/S" -Wait -NoNewWindow -ErrorAction Stop
            Write-Host "Dell Command Update installed successfully" -ForegroundColor Green
        } else {
            Write-Host "Warning: Dell Command Update installer not found at $InstallerFile" -ForegroundColor Yellow
        }

        # Configure Dell Command Update
        Start-Sleep -Seconds 5  # Wait for installation to complete
        if (Test-Path $DellCommandUpdatePath) {
            Write-Host "Configuring Dell Command Update (x86 path)..."
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -biosPassword=$BiosPassword" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -autoSuspendBitLocker=enable" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -scheduleManual" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -updatesNotification=Disable" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/configure -scheduleAction=DownloadInstallAndNotify" -NoNewWindow -Wait
            Write-Host "Running initial scan and update..."
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/scan" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath -ArgumentList "/applyupdates -reboot=disable" -NoNewWindow -Wait
            Write-Host "Dell Command Update configured successfully" -ForegroundColor Green
        }
        elseif (Test-Path $DellCommandUpdatePath2) {
            Write-Host "Configuring Dell Command Update (x64 path)..."
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -biosPassword=$BiosPassword" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -autoSuspendBitLocker=enable" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -scheduleManual" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -updatesNotification=Disable" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/configure -scheduleAction=DownloadInstallAndNotify" -NoNewWindow -Wait
            Write-Host "Running initial scan and update..."
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/scan" -NoNewWindow -Wait
            Start-Process -FilePath $DellCommandUpdatePath2 -ArgumentList "/applyupdates -reboot=disable" -NoNewWindow -Wait
            Write-Host "Dell Command Update configured successfully" -ForegroundColor Green
        } else {
            Write-Host "Warning: Dell Command Update CLI not found after installation" -ForegroundColor Yellow
        }

        return "Success: Dell Command Update installed and configured"

    } -ArgumentList $script:useLocalInstallers

    Write-Color $result "Gray"
}

function Test-Type2PC {
    param([string]$ComputerName)

    Write-Color "Checking if $ComputerName is a Type 2 PC..." "Cyan"

    try {
        $result = Invoke-RemoteCommand -TargetComputer $ComputerName -UseFallback -ScriptBlock {
            # Check for Imprivata Agent Type 2 installation
            $imprivataType2 = Get-WmiObject -Class Win32_Product | Where-Object {
                $_.Name -like "*Imprivata*" -and $_.Name -like "*Agent*"
            }

            # Check registry for Imprivata Agent Type
            $regPath = "HKLM:\SOFTWARE\Imprivata\OneSign"
            $agentType = $null
            if (Test-Path $regPath) {
                try {
                    $agentType = Get-ItemProperty -Path $regPath -Name "AgentType" -ErrorAction SilentlyContinue
                } catch { }
            }

            # Return Type 2 indicators
            return @{
                HasImprivataAgent = ($null -ne $imprivataType2)
                AgentType = if ($agentType) { $agentType.AgentType } else { $null }
                IsType2 = ($agentType -and $agentType.AgentType -eq 2)
            }
        }

        if ($result.IsType2) {
            Write-Color "Detected Type 2 PC (Imprivata Agent Type 2)" "Yellow"
            return $true
        } else {
            Write-Color "Detected Type 1 PC (Standard configuration)" "Green"
            return $false
        }
    }
    catch {
        Write-Color "Could not determine PC type, assuming Type 1: $_" "Yellow"
        return $false
    }
}

function Backup-UserProfiles {
    param([string]$SourceComputer, [string]$TargetComputer, [bool]$IsType2PC = $false)

    if ($IsType2PC) {
        Write-Color "Starting LIMITED user profile backup for Type 2 PC..." "Cyan"
        Write-Color "Type 2 PCs get limited backup (Desktop, Documents, Downloads only)" "Yellow"
    } else {
        Write-Color "Starting comprehensive user profile backup..." "Cyan"
    }

    # Get profile age cutoff
    if ($IsType2PC) {
        # For Type 2, use a shorter default
        $daysInput = Read-Host -Prompt "Enter maximum profile age in days (default 14 for Type 2)"
        if (-not [int]::TryParse($daysInput, [ref]$null)) { $daysInput = 14 }
    } else {
        $daysInput = Read-Host -Prompt "Enter maximum profile age in days (default 30)"
        if (-not [int]::TryParse($daysInput, [ref]$null)) { $daysInput = 30 }
    }
    $cutoffDays = [math]::Max(1, [int]$daysInput)
    $cutoffDate = (Get-Date).AddDays(-$cutoffDays)

    # Store current date for logging purposes if needed
    $BackupPath = "\\$TargetComputer\c$\Files\user_profile_backups\"

    # Create necessary directories
    if (-not (Test-Path $BackupPath)) {
        Show-Spinner "Creating backup directory..." 1
        New-Item -Path $BackupPath -ItemType Directory -Force | Out-Null
    }

    if (-not (Test-Path "C:\Files\ProfileBackup")) {
        Show-Spinner "Creating profile backup directory..." 1
        New-Item -Path "C:\Files\ProfileBackup" -ItemType Directory -Force | Out-Null
    }

    $UserFile = "C:\Files\ProfileBackup\Users.txt"
    Show-Spinner "Getting profiles from source computer..." 2
    (Get-CimInstance -ClassName Win32_UserProfile -ComputerName $SourceComputer).LocalPath | Out-File $UserFile
    $Users = Get-Content -Path $UserFile

    # Process profiles
    $ProfileInfoList = @()
    ForEach ($User in $Users) {
        if ($User -ne "C:\WINDOWS\system32\config\systemprofile" -and
            $User -ne "C:\WINDOWS\ServiceProfiles\NetworkService" -and
            $User -ne "C:\WINDOWS\ServiceProfiles\LocalService" -and
            $User -notlike "*ark*" -and
            $User -notlike "*Imprivata*") {

            $ModifiedPath = $User.Replace("C:\", "\\$SourceComputer\c$\")
            $paths = @(
                "$ModifiedPath\Desktop\Shortcuts",
                "$ModifiedPath\Desktop\McLaren Safety First.url",
                "$ModifiedPath\AppData\Local\Microsoft\Edge\User Data\Default",
                "$ModifiedPath\Desktop\Microsoft Edge.lnk"
            )
            $UserModifiedDate = $null
            foreach ($path in $paths) {
                if (Test-Path -Path $path) {
                    $UserModifiedDate = (Get-Item -Path $path).LastWriteTime
                    break
                }
            }
            if (-not $UserModifiedDate) { $UserModifiedDate = Get-Date "2000-01-01" }
            $ProfileInfoList += [PSCustomObject]@{ User = $User; ModifiedDate = $UserModifiedDate }
        }
    }

    # Copy profiles
    $ProfilesToCopy = $ProfileInfoList | Where-Object { $_.ModifiedDate -ge $cutoffDate }
    if ($ProfilesToCopy.Count -eq 0) {
        $MostRecentProfile = $ProfileInfoList | Sort-Object -Property ModifiedDate -Descending | Select-Object -First 1
        if ($MostRecentProfile) {
            Write-Color "No profiles found within the last $cutoffDays days. Copying the most recently used profile: $($MostRecentProfile.User) (Last modified: $($MostRecentProfile.ModifiedDate))" "Yellow"
            $ProfilesToCopy = @($MostRecentProfile)
        } else {
            Write-Color "No eligible user profiles found to copy." "Red"
        }
    }

    foreach ($Profile in $ProfilesToCopy) {
        $UserOnly = $Profile.User.Replace('C:\Users\<USER>\',"\\$SourceComputer\c$\")
        $UserDestinationPath = Join-Path $BackupPath $UserOnly
        Write-Color " " "Gray"
        Write-Color "Backing up $UserOnly (Last modified: $($Profile.ModifiedDate.ToString('yyyy-MM-dd')))" "Cyan"

        # Define paths based on PC type
        if ($IsType2PC) {
            # Type 2 PCs get limited backup - only Desktop, Documents, Downloads
            $paths = @{
                Desktop = @{
                    Source = "$UserSourcePath\Desktop"
                    Dest = "$UserDestinationPath\Desktop"
                }
                Documents = @{
                    Source = "$UserSourcePath\Documents"
                    Dest = "$UserDestinationPath\Documents"
                }
                Downloads = @{
                    Source = "$UserSourcePath\Downloads"
                    Dest = "$UserDestinationPath\Downloads"
                }
            }
            Write-Color "  Type 2 PC: Limited backup (Desktop, Documents, Downloads only)" "Yellow"
        } else {
            # Type 1 PCs get full backup
            $paths = @{
                Chrome = @{
                    Source = "$UserSourcePath\AppData\Local\Google\Chrome\User Data\Default"
                    Dest = "$UserDestinationPath\AppData\Local\Google\Chrome\User Data\Default"
                    Files = "Bookmarks.*"
                }
                Edge = @{
                    Source = "$UserSourcePath\AppData\Local\Microsoft\Edge\User Data\Default"
                    Dest = "$UserDestinationPath\AppData\Local\Microsoft\Edge\User Data\Default"
                    Files = "Bookmarks.*"
                }
                Desktop = @{
                    Source = "$UserSourcePath\Desktop"
                    Dest = "$UserDestinationPath\Desktop"
                }
                Documents = @{
                    Source = "$UserSourcePath\Documents"
                    Dest = "$UserDestinationPath\Documents"
                }
                Downloads = @{
                    Source = "$UserSourcePath\Downloads"
                    Dest = "$UserDestinationPath\Downloads"
                }
                Favorites = @{
                    Source = "$UserSourcePath\Favorites"
                    Dest = "$UserDestinationPath\Favorites"
                }
            }
            Write-Color "  Type 1 PC: Full backup (all folders and bookmarks)" "Green"
        }

        # Check for OneDrive folder redirection and merge redirected folders
        Write-Color "  Checking for OneDrive folder redirection..." "Gray"
        $oneDriveRedirectedPaths = @{}

        try {
            # Check for OneDrive redirected folders by looking for OneDrive paths
            $oneDriveBasePath = "\\$SourceComputer\c$\Users\$UserOnly\OneDrive"
            $oneDriveBusinessPath = "\\$SourceComputer\c$\Users\$UserOnly\OneDrive - McLaren Health Care"

            # Check both personal and business OneDrive paths
            $oneDrivePaths = @()
            if (Test-Path $oneDriveBasePath) { $oneDrivePaths += $oneDriveBasePath }
            if (Test-Path $oneDriveBusinessPath) { $oneDrivePaths += $oneDriveBusinessPath }

            foreach ($oneDrivePath in $oneDrivePaths) {
                # Check for common redirected folders in OneDrive
                $possibleFolders = @{
                    "Desktop" = "$oneDrivePath\Desktop"
                    "Documents" = "$oneDrivePath\Documents"
                    "Downloads" = "$oneDrivePath\Downloads"
                }

                foreach ($folderType in $possibleFolders.Keys) {
                    $oneDriveFolder = $possibleFolders[$folderType]
                    if (Test-Path $oneDriveFolder) {
                        $oneDriveRedirectedPaths[$folderType] = $oneDriveFolder
                        Write-Color "    Found OneDrive redirected $folderType`: $oneDriveFolder" "Yellow"
                    }
                }
            }
        } catch {
            Write-Color "    Could not check OneDrive redirection: $_" "Gray"
        }

        foreach ($path in $paths.GetEnumerator()) {
            try {
                # Check if this folder has OneDrive redirection
                $folderName = $path.Key
                if ($oneDriveRedirectedPaths.ContainsKey($folderName)) {
                    $oneDriveSource = $oneDriveRedirectedPaths[$folderName]
                    Write-Color "    Copying OneDrive redirected $folderName from: $oneDriveSource" "Cyan"

                    # Copy from OneDrive location
                    if ($path.Value.Files) {
                        robocopy $oneDriveSource $path.Value.Dest $path.Value.Files /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /IS /IT | Out-Null
                    } else {
                        robocopy $oneDriveSource $path.Value.Dest /E /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /IS /IT | Out-Null
                    }

                    # Also copy from original location if it exists (merge)
                    if (Test-Path $path.Value.Source) {
                        Write-Color "    Also merging from original location: $($path.Value.Source)" "Gray"
                        if ($path.Value.Files) {
                            robocopy $path.Value.Source $path.Value.Dest $path.Value.Files /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /IS /IT | Out-Null
                        } else {
                            robocopy $path.Value.Source $path.Value.Dest /E /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /IS /IT | Out-Null
                        }
                    }
                } else {
                    # Normal copy from standard location
                    if ($path.Value.Files) {
                        robocopy $path.Value.Source $path.Value.Dest $path.Value.Files /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /IS /IT | Out-Null
                    } else {
                        robocopy $path.Value.Source $path.Value.Dest /E /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /IS /IT | Out-Null
                    }
                }
            } catch {
                Write-Color "Error copying $($path.Key): $_" "Red"
            }
        }
    }

    # Backup .VBS printer scripts and public desktop
    Write-Color "Backing up .vbs printer scripts in startup folder" "Cyan"
    $sourceStartup = "\\$SourceComputer\c$\ProgramData\Microsoft\Windows\Start Menu\Programs\Startup"
    $destStartup   = "\\$TargetComputer\c$\ProgramData\Microsoft\Windows\Start Menu\Programs\Startup"
    $vbsFiles = Get-ChildItem -Path $sourceStartup -Filter *.vbs -File -ErrorAction SilentlyContinue
    foreach ($file in $vbsFiles) {
        $destFile = Join-Path $destStartup $file.Name
        try {
            Copy-Item -Path $file.FullName -Destination $destFile -Force
            Write-Color "Copied $($file.Name) from $SourceComputer to $TargetComputer startup folder." "Green"
        } catch {
            Write-Color "Failed to copy $($file.Name): $_" "Red"
        }
    }

    Write-Color "Backing up Public desktop" "Cyan"
    $USP1 = "\\$SourceComputer\c$\Users\public\desktop"
    $UDP1 = $BackupPath+"public\desktop"
    try { robocopy $USP1 $UDP1 /E /R:5 /W:5 /MT:16 /NFL /NDL /NJH /NJS /NP /XC /XN | Out-Null } catch { Write-Color "Error copying $USP1 : $_" "Red" }
    Remove-Item -Path $UserFile -ErrorAction Ignore
    if (-not (Get-ChildItem -Path "C:\Files\ProfileBackup")) {
        Remove-Item -Path "C:\Files\ProfileBackup" -Recurse -Force
    }

    # Create the restore script locally
    $startupFolder = "C:\ProgramData\Microsoft\Windows\Start Menu\Programs\Startup"
    $restoreScriptPath = Join-Path -Path $startupFolder -ChildPath "profile_restore.bat"
    $restoreScriptContent = '@echo off
setlocal enabledelayedexpansion
set profile_dir=c:\users\<USER>\files\user_profile_backups\
IF EXIST "c:\files\user_profile_backups\" (
    echo Backup Folder located!
    set bckp_folder_empty=true
    echo ____________Backed Up Profiles________
    for /F %%i in (''dir /b /a "c:\files\user_profile_backups\*"'') do (
        echo %%i
        set bckp_folder_empty=false
        echo bckp_folder_empty: !bckp_folder_empty!
    )
    echo ______________________________________
    if !bckp_folder_empty!==false (
        echo Backup Folder contains profiles...
        echo Checking for existence of: c:\files\user_profile_backups\%USERNAME%\
        IF EXIST "c:\files\user_profile_backups\%USERNAME%\" (
            echo c:\files\user_profile_backups\%USERNAME%\ Exists!
            echo Checking for bookmarks file - c:\files\user_profile_backups\%USERNAME%\Bookmarks :
            IF EXIST c:\files\user_profile_backups\%USERNAME%\Bookmarks (
                Echo c:\files\user_profile_backups\%USERNAME%\Bookmark Found!
                Echo Copying to "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                IF NOT EXIST "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default" ECHO Chrome bookmark folder not built yet. Building..
                IF EXIST %profile_dir%\appdata\ (
                    IF EXIST %profile_dir%\appdata\Local\ (
                        IF EXIST %profile_dir%\appdata\Local\Google\ (
                            IF EXIST "%profile_dir%\appdata\Local\Google\Chrome\" (
                                IF EXIST "%profile_dir%\appdata\Local\Google\Chrome\User Data\" (
                                    IF EXIST "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default" (
                                        ECHO Chrome bookmark folder Found!
                                    ) ELSE (
                                        mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                                    )
                                ) ELSE (
                                    mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\"
                                    mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                                )
                            ) ELSE (
                                mkdir "%profile_dir%\appdata\Local\Google\Chrome\"
                                mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\"
                                mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                            )
                        ) ELSE (
                            mkdir %profile_dir%\appdata\Local\Google\
                            mkdir "%profile_dir%\appdata\Local\Google\Chrome\"
                            mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\"
                            mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                        )
                    ) ELSE (
                        mkdir %profile_dir%\appdata\Local\
                        mkdir %profile_dir%\appdata\Local\Google\
                        mkdir "%profile_dir%\appdata\Local\Google\Chrome\"
                        mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\"
                        mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                    )
                ) ELSE (
                    mkdir %profile_dir%\appdata\
                    mkdir %profile_dir%\appdata\Local\
                    mkdir %profile_dir%\appdata\Local\Google\
                    mkdir "%profile_dir%\appdata\Local\Google\Chrome\"
                    mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\"
                    mkdir "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default"
                )
                Echo Copying bookmarks file
                ECHO F
                xcopy /Y /H /F /G /R /E c:\files\user_profile_backups\%USERNAME%\Bookmarks "%profile_dir%\appdata\Local\Google\Chrome\User Data\Default\Bookmarks"
                del c:\files\user_profile_backups\%USERNAME%\Bookmarks
            )
            echo %profile_dir%
            xcopy /Y /H /F /G /R /E c:\files\user_profile_backups\%USERNAME% %profile_dir%
            RD /S /Q "c:\files\user_profile_backups\%USERNAME%"
            set bckp_folder_empty=true
            for /F %%i in (''dir /b /a "c:\files\user_profile_backups\*"'') do (
                set bckp_folder_empty=false
            )
            if !bckp_folder_empty!==true (
                echo No more backup profiles detected. Removing profile restore files.
                RD /S /Q "c:\files\user_profile_backups"
                IF EXIST "c:\programdata\microsoft\windows\start Menu\programs\startup\profile_restore.bat" (
                    del /q "c:\programdata\microsoft\windows\start Menu\programs\startup\profile_restore.bat"
                )
            ) else (
                echo More backup profiles detected. Leaving profile restore files.
            )
        ) else (
            echo Backup for %USERNAME% doesn''t Exist!
        )
    ) else (
        echo Backup folder exists, but is empty!
        RD /S /Q "c:\files\user_profile_backups"
        IF EXIST "c:\programdata\microsoft\windows\start Menu\programs\startup\profile_restore.bat" (
            del /q "c:\programdata\microsoft\windows\start Menu\programs\startup\profile_restore.bat"
        )
    )
) ELSE (
    echo No Backup Folder on this PC
    IF EXIST "c:\programdata\microsoft\windows\start Menu\programs\startup\profile_restore.bat" (
        del /q "c:\programdata\microsoft\windows\start Menu\programs\startup\profile_restore.bat"
    )
)'

    Try {
        Set-Content -Path $restoreScriptPath -Value $restoreScriptContent
        $remoteStartupFolder = "\\$TargetComputer\c$\ProgramData\Microsoft\Windows\Start Menu\Programs\Startup"
        Copy-Item -Path $restoreScriptPath -Destination $remoteStartupFolder -Force
        Write-Color "Profile restore script copied to $remoteStartupFolder" "Green"
    } Catch {
        Write-Color "Failed to create or copy restore script: $_" "Red"
    }
}

function Set-ADConfiguration {
    param([string]$SourceComputer, [string]$TargetComputer)

    Write-Color "Configuring Active Directory settings..." "Cyan"

    if (-not $script:autoYes) {
        $adChoice = Read-Host "Do you want to copy AD group memberships and place the new PC in the same OU as the old one? (Y/N)"
        if ($adChoice -notmatch '^(Y|y)$') {
            Write-Color "AD configuration skipped" "Yellow"
            return
        }
    }

    try {
        Import-Module ActiveDirectory -ErrorAction Stop

        # Region: Account Validation
        $source = $SourceComputer.Trim()
        $destination = $TargetComputer.Trim()
        $sourceAD = if ($source[-1] -ne '$') { "$source$" } else { $source }
        $destinationAD = if ($destination[-1] -ne '$') { "$destination$" } else { $destination }



        # Validate AD computer access and store description
        $sourceTest = Get-ADComputer -Identity $sourceAD -Properties Description -ErrorAction SilentlyContinue
        if (-not $sourceTest) {
            Write-Color "[!] Source computer '$sourceAD' not found in Active Directory" "Red"
            return
        }
        $script:earlySourceDescription = $sourceTest.Description

        if (-not (Get-ADComputer -Identity $destinationAD -ErrorAction SilentlyContinue)) {
            Write-Color "[!] Target computer '$destinationAD' not found in Active Directory" "Red"
            return
        }

        # Region: Group Membership Processing
        Write-Color "`n[1/3] Preparing group membership changes..." "Cyan"

        $oldGroups = Get-ADComputer -Identity $destinationAD -Properties MemberOf |
            Select-Object -ExpandProperty MemberOf |
            ForEach-Object { (Get-ADGroup $_).Name } |
            Where-Object { $_ -ne "Domain Computers" }

        Write-Color "  Current groups on $destinationAD :" "Yellow"
        $oldGroups | Sort-Object | ForEach-Object { Write-Color "    $_" "Gray" }

        # Remove existing non-default groups
        $oldGroups | ForEach-Object {
            try {
                Remove-ADGroupMember -Identity $_ -Members $destinationAD -Confirm:$false -ErrorAction Stop
                Write-Color "    Removed: $_" "DarkRed"
            }
            catch {
                Write-Color "    [!] Failed removing $_ : $_" "Red"
            }
        }

        # Region: Group Copy Operations
        Write-Color "`n[2/3] Copying group memberships..." "Cyan"

        $sourceGroups = Get-ADComputer -Identity $sourceAD -Properties MemberOf |
            Select-Object -ExpandProperty MemberOf |
            ForEach-Object { (Get-ADGroup $_).Name }

        $sourceGroups | ForEach-Object {
            try {
                Add-ADGroupMember -Identity $_ -Members $destinationAD -ErrorAction Stop
                Write-Color "    Added: $_" "DarkGreen"
            }
            catch {
                Write-Color "    [!] Failed adding $_ : $_" "Red"
            }
        }

        # Region: OU Migration
        Write-Color "`n[3/4] Processing OU relocation..." "Cyan"
        try {
            $sourceOU = (Get-ADComputer $sourceAD).DistinguishedName -replace '^CN=[^,]+,', ''
            $targetComputerDN = (Get-ADComputer $destinationAD).DistinguishedName
            $currentOU = (Get-ADComputer $destinationAD).DistinguishedName -replace '^CN=[^,]+,', ''

            if ($sourceOU -ne $currentOU) {
                Move-ADObject -Identity $targetComputerDN -TargetPath $sourceOU
                Write-Color "  Successfully moved to OU: $sourceOU" "Green"
            }
            else {
                Write-Color "  Computer already in correct OU: $sourceOU" "Yellow"
            }
        }
        catch {
            Write-Color "  [!] OU move failed: $_" "Red"
        }

        # Region: AD Comments Migration
        Write-Color "`n[4/4] Copying AD comments..." "Cyan"
        $adCommentsResult = "Failed"
        try {
            # Get source computer's description/comments
            $sourceDescription = $null

            # Try with stored credentials first
            try {
                $sourceComputer = Get-ADComputer $sourceAD -Properties Description -Credential $script:storedCredentials -ErrorAction Stop
                $sourceDescription = $sourceComputer.Description
            }
            catch {
                # Try without explicit credentials (current user context)
                try {
                    $sourceComputer = Get-ADComputer $sourceAD -Properties Description -ErrorAction Stop
                    $sourceDescription = $sourceComputer.Description
                }
                catch {
                    # Use early test result as fallback
                    if ($script:earlySourceDescription) {
                        $sourceDescription = $script:earlySourceDescription
                    }
                }
            }

            # If still no description, use early test result
            if ((-not $sourceDescription -or $sourceDescription.Trim() -eq "") -and $script:earlySourceDescription) {
                $sourceDescription = $script:earlySourceDescription
            }

            $currentDate = Get-Date -Format "MM/dd/yyyy"

            # Get target computer's current description to preserve it
            try {
                $targetComputer = Get-ADComputer $destinationAD -Properties Description -Credential $script:storedCredentials -ErrorAction Stop
                $currentTargetDescription = $targetComputer.Description
            }
            catch {
                try {
                    $targetComputer = Get-ADComputer $destinationAD -Properties Description -ErrorAction Stop
                    $currentTargetDescription = $targetComputer.Description
                }
                catch {
                    $currentTargetDescription = $null
                }
            }

            if ($sourceDescription -and $sourceDescription.Trim() -ne "") {
                # Create new description with replacement prefix including date and preserve existing target comment
                if ($currentTargetDescription -and $currentTargetDescription.Trim() -ne "") {
                    $newDescription = "{replaced $source - set up on $currentDate} previous comment: $currentTargetDescription"
                } else {
                    $newDescription = "{replaced $source - set up on $currentDate} previous comment: $sourceDescription"
                }

                # Set the new description on target computer
                try {
                    Set-ADComputer $destinationAD -Description $newDescription -Credential $script:storedCredentials -ErrorAction Stop
                    $adCommentsResult = "Copied from source with replacement prefix"
                }
                catch {
                    Set-ADComputer $destinationAD -Description $newDescription -ErrorAction Stop
                    $adCommentsResult = "Copied from source with replacement prefix"
                }
                Write-Color "  Successfully copied AD comments from source: $sourceDescription" "Green"
            }
            else {
                # No source description, but still add replacement info and preserve target description
                if ($currentTargetDescription -and $currentTargetDescription.Trim() -ne "") {
                    $newDescription = "{replaced $source - set up on $currentDate} previous comment: $currentTargetDescription"
                } else {
                    $newDescription = "{replaced $source - set up on $currentDate}"
                }

                try {
                    Set-ADComputer $destinationAD -Description $newDescription -Credential $script:storedCredentials -ErrorAction Stop
                    $adCommentsResult = "No source description - added replacement info only"
                }
                catch {
                    Set-ADComputer $destinationAD -Description $newDescription -ErrorAction Stop
                    $adCommentsResult = "No source description - added replacement info only"
                }
                Write-Color "  Added replacement tracking info" "Yellow"
            }
        }
        catch {
            Write-Color "  [!] AD comments copy failed: $_" "Red"
            $adCommentsResult = "Failed: $_"
        }

        # Final Verification
        Write-Color "`n[Complete] AD configuration summary:" "Cyan"
        Write-Color "  Source Computer: $sourceAD" "Gray"
        Write-Color "  Target Computer: $destinationAD" "Gray"
        Write-Color "  Groups Transferred: $($sourceGroups.Count)" "Gray"
        Write-Color "  Final OU: $sourceOU" "Gray"
        Write-Color "  AD Comments: $adCommentsResult" "Gray"

        return $sourceGroups
    }
    catch {
        Write-Color "[!] Critical error in AD operations: $_" "Red"
        return @()
    }
}

function Install-Applications {
    param([string]$TargetComputer, [bool]$InstallOffice)

    Write-Color "Installing applications..." "Cyan"

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($InstallOffice)

        Write-Host "Installing core applications..." -ForegroundColor Yellow

        # Install Java
        if (Test-Path "C:\Files\jre1.7.0_45.msi") {
            Write-Host "Installing Java..." -ForegroundColor Gray
            Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"C:\Files\jre1.7.0_45.msi`" /quiet" -Wait -NoNewWindow
            Write-Host "Java installed" -ForegroundColor Green
        }

        # Install Chrome
        if (Test-Path "C:\Files\GoogleChromeStandaloneEnterprise64.msi") {
            Write-Host "Installing Chrome..." -ForegroundColor Gray
            Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"C:\Files\GoogleChromeStandaloneEnterprise64.msi`" /quiet" -Wait -NoNewWindow
            Write-Host "Chrome installed" -ForegroundColor Green
        }

        # Install Office if requested
        if ($InstallOffice) {
            Write-Host "*** Install Office 365"
            Write-Host "*** Running Office 365 installation"
            if (Test-Path "C:\Files\o365\batch_365.bat") {
                $p = Start-Process -FilePath "C:\Files\o365\batch_365.bat" -PassThru
                $p.WaitForExit()
                Write-Host "*** Deleting C:\Files\o365 folder"
                Remove-Item -Path "C:\Files\o365" -Recurse -Force

                # Office Ribbon Fix
                Write-Host "***" -ForegroundColor Green
                Write-Output "Office ribbon key fix being applied"
                $RegistryPath = "HKCU:\Software\Microsoft\Office\16.0\Common\ExperimentConfigs\ExternalFeatureOverrides\word"
                $RegistryName = "Microsoft.Office.UXPlatform.FluentSVRefresh"
                $RegistryValue = "false"
                if (-not (Test-Path $RegistryPath)) {
                    New-Item -Path $RegistryPath -Force | Out-Null
                }
                Set-ItemProperty -Path $RegistryPath -Name $RegistryName -Value $RegistryValue
                Write-Output "Registry key '$RegistryName' set to '$RegistryValue' in path '$RegistryPath'."
                Write-Host "Office ribbon key and value have been set successfully."
            } else {
                Write-Host "Warning: Office batch file not found at C:\Files\o365\batch_365.bat" -ForegroundColor Yellow
            }
        }

        # Install Citrix Receiver
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Checking Citrix Receiver 4.9 installation"

        # Check if Citrix is already installed
        $citrixInstalled = Get-WmiObject -Class Win32_Product | Where-Object {
            $_.Name -like "*Citrix*" -and ($_.Name -like "*Workspace*" -or $_.Name -like "*Receiver*")
        }

        if ($citrixInstalled) {
            Write-Host "Citrix is already installed - skipping installation" -ForegroundColor Yellow
            $citrixInstalled | ForEach-Object {
                Write-Host "  Found: $($_.Name) (Version: $($_.Version))" -ForegroundColor Gray
            }
        } else {
            Write-Host "Installing Citrix Receiver 4.9..." -ForegroundColor Yellow
            $citrixExecutable = "C:\Files\4.9_LTSR2\CitrixReceiver"

            if (Test-Path $citrixExecutable) {
                # Run cleanup utility first
                $cleanupUtility = "C:\Files\4.9_LTSR2\ReceiverCleanupUtility.exe"
                if (Test-Path $cleanupUtility) {
                    Write-Host "Running Citrix cleanup utility..." -ForegroundColor Gray
                    Start-Process -FilePath $cleanupUtility -ArgumentList "/silent", "/disableCEIP" -Wait -NoNewWindow
                }

                # Install Citrix Receiver
                $arguments = "/silent", "/includeSSON", "/ALLOWADDSTORE=N", "/AutoUpdateCheck=DISABLED", "/noreboot", "/EnableCEIP=false"
                $process = Start-Process -FilePath $citrixExecutable -ArgumentList $arguments -Wait -PassThru -NoNewWindow

                if ($process.ExitCode -eq 0) {
                    Write-Host "Citrix Receiver 4.9 installed successfully" -ForegroundColor Green
                } elseif ($process.ExitCode -eq 255) {
                    Write-Host "Warning: Exit code 255 - Software may already be installed" -ForegroundColor Yellow
                } else {
                    Write-Host "Citrix installation completed with exit code: $($process.ExitCode)" -ForegroundColor Yellow
                }
            } else {
                Write-Host "Warning: Citrix installer not found at $citrixExecutable" -ForegroundColor Yellow
            }
        }

        return "Success: Applications installed"

    } -ArgumentList $InstallOffice

    Write-Color $result "Gray"
}

function Rename-Computer {
    param([string]$TargetComputer, [string]$NewName)

    Write-Color "Renaming computer to $NewName..." "Cyan"

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {
        param($NewName, [PSCredential]$DomainCredentials)

        try {
            # Use domain credentials for renaming domain-joined computers
            if ($DomainCredentials) {
                Write-Host "Renaming computer with domain credentials..." -ForegroundColor Yellow
                Rename-Computer -NewName $NewName -DomainCredential $DomainCredentials -Force -Restart
                Write-Host "Computer will be renamed to $NewName and restarted" -ForegroundColor Green
                return "Success: Computer rename scheduled with restart"
            } else {
                Write-Host "Renaming computer without domain credentials..." -ForegroundColor Yellow
                Rename-Computer -NewName $NewName -Force
                Write-Host "Computer will be renamed to $NewName on next restart" -ForegroundColor Green
                return "Success: Computer rename scheduled"
            }
        }
        catch {
            Write-Host "Error renaming computer: $_" -ForegroundColor Red
            return "Error: Computer rename failed - $_"
        }

    } -ArgumentList $NewName, $script:storedCredentials

    Write-Color $result "Gray"
}

function Install-PrinterMigration {
    param([string]$SourceComputer, [string]$TargetComputer)

    Write-Color "Starting printer migration..." "Cyan"

    if (-not $script:autoYes) {
        $printerChoice = Read-Host "Do you want to perform printer backup and restore? (Y/N)"
        if ($printerChoice -notmatch '^(Y|y)$') {
            Write-Color "Printer migration skipped" "Yellow"
            return
        }
    }

    # Validate essential variables
    if ([string]::IsNullOrEmpty($SourceComputer) -or [string]::IsNullOrEmpty($TargetComputer)) {
        Write-Color "ERROR: Missing source or target computer identifiers" "Red"
        return
    }

    # Standardize and sanitize input
    $sourcePC = $SourceComputer.Trim()
    $targetPC = $TargetComputer.Trim()

    try {
        # Verify network connectivity
        Write-Color "`n[1/5] Verifying network connectivity..." "Cyan"
        Test-Connection $sourcePC -Count 1 -ErrorAction Stop | Out-Null
        Test-Connection $targetPC -Count 1 -ErrorAction Stop | Out-Null

        $DriverTempPath = "\\$targetPC\c`$\Temp\Drivers\"
        $exportPath = "\\$sourcePC\c`$\Temp\Drivers\"
        $ExcludeList = "XPS|Fax|PDF|OneNote|MSFT|CutePDF|Send to Microsoft OneNote|Microsoft Print To PDF|Microsoft XPS Document Writer|Snagit|WebEx Document Loader|WebEx"

        # Create temporary directories
        Write-Color "`n[2/5] Preparing temporary directories..." "Cyan"
        Invoke-Command -ComputerName $sourcePC -Credential $script:storedCredentials -ScriptBlock {
            New-Item -Path $using:exportPath -ItemType Directory -Force -ErrorAction Stop | Out-Null
        }
        Invoke-Command -ComputerName $targetPC -Credential $script:storedCredentials -ScriptBlock {
            New-Item -Path $using:DriverTempPath -ItemType Directory -Force -ErrorAction Stop | Out-Null
        }

        # Get source printers
        Write-Color "`n[3/5] Retrieving source printers..." "Cyan"
        $printers = Invoke-Command -ComputerName $sourcePC -Credential $script:storedCredentials -ScriptBlock {
            Get-Printer | Where-Object {
                $_.Name -notmatch $using:ExcludeList -and
                $_.Type -notmatch 'Text|PrintServer'
            } | Select-Object Name, DriverName, PortName, Shared, Published, Location, @{
                Name = 'DriverInfPath'
                Expression = {
                    $driver = Get-PrinterDriver -Name $_.DriverName -ErrorAction SilentlyContinue
                    if ($driver) { $driver.InfPath } else { $null }
                }
            }
        } -ErrorAction Stop

        Write-Color "  Found $($printers.Count) printers on source computer" "Gray"
        $printers | ForEach-Object {
            Write-Color "  [Source] $($_.Name) | Driver: $($_.DriverName)" "DarkGray"
        }

        # Get target drivers with normalized names
        $targetDrivers = Invoke-Command -ComputerName $targetPC -Credential $script:storedCredentials -ScriptBlock {
            Get-PrinterDriver | Select-Object Name, @{
                Name = 'Normalized'
                Expression = {
                    ($_.Name -replace 'PS3','PostScript3' -replace '[^a-zA-Z0-9]','').ToLower()
                }
            }
        } -ErrorAction Stop

        # Process printers
        Write-Color "`n[4/5] Migrating printers..." "Cyan"
        $successCount = 0
        $failureCount = 0

        foreach ($printer in $printers) {
            $printerName = $printer.Name
            $wantedDriverName = $printer.DriverName
            $portName = $printer.PortName

            try {
                Write-Color "  Processing: $printerName" "Gray"

                # Enhanced normalization with PS3 -> PostScript substitution
                $normalizedSourceDriver = ($wantedDriverName -replace 'PS3','PostScript3' -replace '[^a-zA-Z0-9]','').ToLower()

                # Find best match using multiple criteria
                $matchedDriver = $targetDrivers | Where-Object {
                    ($_.Normalized -eq $normalizedSourceDriver) -or
                    ($_.Normalized -like "*$normalizedSourceDriver*" -and $_.Normalized -like "*lexmark*") -or
                    ($_.Normalized -replace 'postscript','ps' -eq $normalizedSourceDriver)
                } | Select-Object -First 1

                # Check if this is a Zebra printer - if so, try to install the specific driver
                $isZebraPrinter = ($printerName -like "*ZD*" -or $printerName -like "*ZT*" -or $printerName -like "*ZQ*" -or
                                  $printerName -like "*Zebra*" -or $wantedDriverName -like "*ZDesigner*")

                if ($matchedDriver -and -not $isZebraPrinter) {
                    $finalDriverName = $matchedDriver.Name
                    Write-Color "    Auto-matched driver: $finalDriverName" "Yellow"
                }
                elseif ($isZebraPrinter) {
                    # For Zebra printers, try to install the specific driver first
                    Write-Color "    Zebra printer detected: $printerName | Driver: $wantedDriverName" "Cyan"

                    # Try to install the specific Zebra driver
                    $zebraInstallResult = Install-SpecificZebraDriver -TargetComputer $targetPC -DriverName $wantedDriverName

                    if ($zebraInstallResult -like "Success:*") {
                        # Driver installed successfully, use it
                        $finalDriverName = $wantedDriverName
                        Write-Color "    Installed and using driver: $finalDriverName" "Green"
                    }
                    else {
                        # Driver installation failed, show user options
                        Write-Color "    Could not install exact driver, showing alternatives..." "Yellow"

                        # Skip interactive selection in auto-yes mode
                        if ($script:autoYes) {
                            Write-Color "    Auto-yes mode: Skipping printer with driver installation failure" "Yellow"
                            $failureCount++
                            continue
                        }

                        # Extract key terms from printer name and driver name for smart filtering
                        $searchTerms = @()
                        $printerWords = ($printerName -split '\s+|[-_]') | Where-Object { $_.Length -gt 2 }
                        $driverWords = ($wantedDriverName -split '\s+|[-_]') | Where-Object { $_.Length -gt 2 }
                        $searchTerms += $printerWords + $driverWords

                        # Filter drivers based on search terms
                        $relevantDrivers = @()
                        $allDrivers = $targetDrivers.Name

                        # First, look for drivers that match any search terms
                        foreach ($driver in $allDrivers) {
                            $matchCount = 0
                            foreach ($term in $searchTerms) {
                                if ($driver -like "*$term*") {
                                    $matchCount++
                                }
                            }
                            if ($matchCount -gt 0) {
                                $relevantDrivers += [PSCustomObject]@{
                                    Name = $driver
                                    MatchCount = $matchCount
                                }
                            }
                        }

                        # Sort by match count (most relevant first) and take top 10
                        $topDrivers = $relevantDrivers | Sort-Object MatchCount -Descending | Select-Object -First 10

                        if ($topDrivers.Count -gt 0) {
                            Write-Host "Most relevant Zebra printer drivers:"
                            $i = 0
                            $topDrivers | ForEach-Object {
                                Write-Host "[$i] $($_.Name)"
                                $i++
                            }

                            # Also show a few common fallback drivers
                            $commonDrivers = $allDrivers | Where-Object {
                                $_ -like "*Generic*" -or $_ -like "*Universal*" -or $_ -like "*PDF*" -or $_ -like "*OneNote*"
                            } | Select-Object -First 3

                            if ($commonDrivers) {
                                Write-Host "Common fallback drivers:"
                                foreach ($driver in $commonDrivers) {
                                    Write-Host "[$i] $driver"
                                    $i++
                                }
                            }

                            $allFilteredDrivers = @($topDrivers.Name) + @($commonDrivers)
                            $selectedIndex = Read-Host "Enter driver index or press Enter to type name"

                            if ($selectedIndex -match '^\d+$' -and [int]$selectedIndex -lt $allFilteredDrivers.Count) {
                                $finalDriverName = $allFilteredDrivers[[int]$selectedIndex]
                            } else {
                                $finalDriverName = Read-Host "Enter exact driver name"
                            }
                        } else {
                            Write-Host "No relevant drivers found for '$printerName' / '$wantedDriverName'"
                            $finalDriverName = Read-Host "Enter exact driver name"
                        }
                    }
                }
                else {
                    # Use Lexmark Universal as fallback for Lexmark printers
                    if ($wantedDriverName -like "*Lexmark*" -or $printerName -like "*Lexmark*") {
                        $finalDriverName = "Lexmark Universal v2 PostScript 3 Emulation"
                        Write-Color "    Using Lexmark Universal driver fallback" "Yellow"
                    }
                    else {
                        # Skip interactive selection in auto-yes mode
                        if ($script:autoYes) {
                            Write-Color "    Auto-yes mode: Skipping printer with no matching driver" "Yellow"
                            $failureCount++
                            continue
                        }

                        # For non-Zebra printers, show generic driver selection
                        Write-Color "    Driver '$wantedDriverName' not found on $targetPC." "Yellow"
                        $finalDriverName = Read-Host "Enter exact driver name"
                    }
                }

                # Create printer port and printer
                Invoke-Command -ComputerName $targetPC -Credential $script:storedCredentials -ScriptBlock {
                    param($printerName, $finalDriverName, $portName, $printer)

                    # Create port if missing
                    if (-not (Get-PrinterPort -Name $portName -ErrorAction SilentlyContinue)) {
                        Add-PrinterPort -Name $portName -ErrorAction Stop | Out-Null
                    }

                    # Create printer if missing
                    if (-not (Get-Printer -Name $printerName -ErrorAction SilentlyContinue)) {
                        Add-Printer -Name $printerName -DriverName $finalDriverName -PortName $portName -ErrorAction Stop | Out-Null
                    }

                    # Set printer properties
                    Set-Printer -Name $printerName -Shared:$printer.Shared -ErrorAction SilentlyContinue | Out-Null
                } -ArgumentList $printerName, $finalDriverName, $portName, $printer -ErrorAction Stop

                Write-Color "    Success: $printerName" "Green"
                $successCount++
            }
            catch {
                Write-Color "    [!] Failed: $printerName - $($_.Exception.Message)" "Red"
                $failureCount++
            }
        }

        # Cleanup
        Write-Color "`n[5/5] Removing temporary files..." "Cyan"
        Invoke-Command -ComputerName $sourcePC -Credential $script:storedCredentials -ScriptBlock {
            Remove-Item -Path $using:exportPath -Recurse -Force -ErrorAction SilentlyContinue | Out-Null
        }
        Invoke-Command -ComputerName $targetPC -Credential $script:storedCredentials -ScriptBlock {
            Remove-Item -Path $using:DriverTempPath -Recurse -Force -ErrorAction SilentlyContinue | Out-Null
        }

        # Results summary
        Write-Color "`n[Migration Complete]" "Cyan"
        Write-Color "  Attempted printers: $($printers.Count)" "Gray"
        Write-Color "  Successful: $successCount" "Green"
        Write-Color "  Failed: $failureCount" "Red"
    }
    catch {
        Write-Color "`n[!] Critical printer migration error: $($_.Exception.Message)" "Red"
    }
}

function Install-SystemConfiguration {
    param([string]$TargetComputer)

    Write-Color "Installing system configuration..." "Cyan"

    $result = Invoke-RemoteCommand -TargetComputer $TargetComputer -UseFallback -ScriptBlock {

        # System Information
        $ComputerName = $env:computername
        $ComputerBIOS = Get-WmiObject -Class Win32_Bios
        $ComputerSN = $ComputerBIOS.SerialNumber
        $ComputerModel = (Get-WmiObject -Class Win32_ComputerSystem).Model
        $ComputerMemory = Get-CimInstance Win32_PhysicalMemory | Measure-Object -Property capacity -Sum | Select-Object -ExpandProperty Sum
        $ComputerMemoryGB = [math]::Round($ComputerMemory / 1GB, 2)

        Write-Host "*** Setting Pagefile info"
        Write-Host "*** Computer Name: $ComputerName"
        Write-Host "*** Computer Model: $ComputerModel"
        Write-Host "*** Computer Serial: $ComputerSN"
        Write-Host "*** Computer RAM: $ComputerMemoryGB GB"

        # Install Splashtop
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Checking Splashtop Remote installation"
        if ((Test-Path "C:\Program Files (x86)\Splashtop\Splashtop Remote\Server\SRServer.exe") -or
            (Test-Path "C:\Program Files\Splashtop\Splashtop Remote\Server\SRServer.exe")) {
            Write-Host "Splashtop Remote is already installed - restarting service" -ForegroundColor Yellow
            try {
                Stop-Service -Name "SplashtopRemoteService" -ErrorAction SilentlyContinue
                Start-Sleep -Seconds 5
                Start-Service -Name "SplashtopRemoteService" -ErrorAction SilentlyContinue
                Write-Host "SplashtopRemoteService restarted successfully" -ForegroundColor Green
            } catch {
                Write-Host "Warning: Could not restart SplashtopRemoteService - $_" -ForegroundColor Yellow
            }
        } else {
            Write-Host "Installing Splashtop Remote" -ForegroundColor Yellow
            if (Test-Path "C:\Files\Splashtop\Splashtop_Streamer_v3.5.8.3.msi") {
                $siteCode = "TIIHJDTLCNDN"
                Write-Host "Selected Site Code: $siteCode"
                Start-Process -FilePath "msiexec.exe" -ArgumentList "/i C:\Files\Splashtop\Splashtop_Streamer_v3.5.8.3.msi /q USERINFO='sc=splashtop.mclaren.org:443,dcode=$siteCode,hidewindow=1,confirm_d=0'" -Wait
                Write-Host "Splashtop installation complete" -ForegroundColor Green
                Remove-Item "C:\Files\Splashtop" -Recurse -Force -ErrorAction SilentlyContinue
            } else {
                Write-Host "Warning: Splashtop installer not found at C:\Files\Splashtop\" -ForegroundColor Yellow
            }
        }

        # Install .NET Framework 3.5
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Checking .NET Framework 3.5 installation"

        # Check if .NET Framework 3.5 is already installed
        $netFx35Installed = $false
        try {
            $feature = Get-WindowsOptionalFeature -Online -FeatureName "NetFx3" -ErrorAction SilentlyContinue
            if ($feature -and $feature.State -eq "Enabled") {
                $netFx35Installed = $true
            }
        } catch {
            # Fallback check using registry
            $regPath = "HKLM:\SOFTWARE\Microsoft\NET Framework Setup\NDP\v3.5"
            if (Test-Path $regPath) {
                $installValue = Get-ItemProperty -Path $regPath -Name "Install" -ErrorAction SilentlyContinue
                if ($installValue -and $installValue.Install -eq 1) {
                    $netFx35Installed = $true
                }
            }
        }

        if ($netFx35Installed) {
            Write-Host ".NET Framework 3.5 is already installed - skipping installation" -ForegroundColor Yellow
        } else {
            Write-Host "Installing .NET Framework 3.5 with timeout and retry logic" -ForegroundColor Yellow
            $destinationPath2 = "C:\Files\sxs"
            if (-Not (Test-Path -Path $destinationPath2)) {
                New-Item -ItemType Directory -Path $destinationPath2 -Force | Out-Null
            }
            if (Test-Path "$destinationPath2\*") {
                # Install .NET Framework 3.5 using DISM
                try {
                    Write-Host "Installing .NET Framework 3.5..." -ForegroundColor Yellow
                    $process = Start-Process -FilePath "DISM.exe" -ArgumentList "/Online /enable-feature /featurename:netfx3 /all /Source:$destinationPath2 /LimitAccess" -Wait -PassThru -NoNewWindow

                    if ($process.ExitCode -eq 0) {
                        Write-Host ".NET Framework 3.5 installation completed successfully!" -ForegroundColor Green
                    } else {
                        Write-Host ".NET Framework 3.5 installation completed with exit code: $($process.ExitCode)" -ForegroundColor Yellow
                    }
                } catch {
                    Write-Host ".NET Framework 3.5 installation failed: $_" -ForegroundColor Red
                }
            } else {
                Write-Host "Warning: .NET Framework 3.5 source files not found at $destinationPath2" -ForegroundColor Yellow
            }
            Remove-Item -Path "$destinationPath2\*" -Recurse -Force -ErrorAction SilentlyContinue
        }

        # Configure Local Admin Account
        $username = "bayfs-local"
        $password = ConvertTo-SecureString "FSMIS302!" -AsPlainText -Force
        $description = "bay fs Local Admin"
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Checking Local Admin Account $username"

        # Check if user already exists
        $existingUser = Get-LocalUser -Name $username -ErrorAction SilentlyContinue
        if ($existingUser) {
            Write-Host "Local admin account '$username' already exists - checking group membership" -ForegroundColor Yellow

            # Check if user is in Administrators group
            $adminMembers = Get-LocalGroupMember -Group "Administrators" -ErrorAction SilentlyContinue | Where-Object { $_.Name -like "*$username" }
            if ($adminMembers) {
                Write-Host "User '$username' is already in Administrators group" -ForegroundColor Green
            } else {
                Write-Host "Adding '$username' to Administrators group" -ForegroundColor Yellow
                try {
                    Add-LocalGroupMember -Group "Administrators" -Member $username -ErrorAction Stop
                    Write-Host "User '$username' added to Administrators group successfully" -ForegroundColor Green
                } catch {
                    Write-Host "Warning: Could not add '$username' to Administrators group - $_" -ForegroundColor Yellow
                }
            }
        } else {
            Write-Host "Creating Local Admin Account $username" -ForegroundColor Yellow
            try {
                New-LocalUser -Name $username -Password $password -Description $description -FullName $username -ErrorAction Stop
                Add-LocalGroupMember -Group "Administrators" -Member $username -ErrorAction Stop
                Write-Host "Local admin account '$username' created and added to Administrators group" -ForegroundColor Green
            } catch {
                Write-Host "Warning: Could not create local admin account - $_" -ForegroundColor Yellow
            }
        }

        # Configure Printers
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Removing generic printers"
        Remove-Printer -Name "Microsoft XPS Document Writer" -ErrorAction Ignore
        Remove-Printer -Name "Microsoft Print to PDF" -ErrorAction Ignore
        Remove-Printer -Name "Fax" -ErrorAction Ignore
        Set-ItemProperty -path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -name HideFastUserSwitching -type DWord -value 0

        # Configure Volume and BitLocker
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Setting Volume"
        Start-Job -ScriptBlock {
            powershell.exe -ExecutionPolicy Bypass -File "C:\Files\Volume.ps1"
        } | Out-Null

        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Adding BitLocker Recovery in AD"
        Start-Job -ScriptBlock {
            powershell.exe -ExecutionPolicy Bypass -File "C:\Files\BitLockerAD.ps1"
        } | Out-Null

        # Configure NumLock
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Turning NumLock On at Start-up for Windows 10"
        Set-ItemProperty -path "HKCU:\Control Panel\Keyboard" -name "InitialKeyboardIndicators" -value "2"
        Set-ItemProperty -path "Registry::HKEY_USERS\.DEFAULT\Control Panel\Keyboard" -name "InitialKeyboardIndicators" -value "2"
        Set-ItemProperty -path "Registry::HKEY_USERS\S-1-5-19\Control Panel\Keyboard" -name "InitialKeyboardIndicators" -value "2"
        Set-ItemProperty -path "Registry::HKEY_USERS\S-1-5-20\Control Panel\Keyboard" -name "InitialKeyboardIndicators" -value "2"

        # Configure Windows Photo Viewer
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Adding Windows Photo Viewer"
        if (Test-Path "C:\Files\Win_Photo_Viewer.reg") {
            try {
                $result = reg import "C:\Files\Win_Photo_Viewer.reg" 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "Windows Photo Viewer registry settings imported successfully" -ForegroundColor Green
                } else {
                    Write-Host "Warning: Windows Photo Viewer registry import failed - $result" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "Warning: Could not import Windows Photo Viewer registry - $_" -ForegroundColor Yellow
            }
        } else {
            Write-Host "Warning: Windows Photo Viewer registry file not found at C:\Files\Win_Photo_Viewer.reg" -ForegroundColor Yellow
        }

        # Configure Edge
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Disabling Edge desktop shortcut creation"
        try {
            New-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" -Name "DisableEdgeDesktopShortcutCreation" -Value "1" -PropertyType DWORD -Force | Out-Null
            Write-Host "Edge desktop shortcut creation disabled" -ForegroundColor Green
        } catch {
            Write-Host "Warning: Could not disable Edge desktop shortcut creation - $_" -ForegroundColor Yellow
        }

        # Install Java
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Checking Java RE 7 Update 45 installation"

        # Check multiple possible Java installation paths
        $javaInstalled = $false
        $javaPaths = @(
            "C:\Program Files (x86)\Java\jre7\bin\client\jvm.dll",
            "C:\Program Files\Java\jre7\bin\client\jvm.dll",
            "C:\Program Files (x86)\Java\jre1.7.0_45\bin\client\jvm.dll",
            "C:\Program Files\Java\jre1.7.0_45\bin\client\jvm.dll"
        )

        foreach ($path in $javaPaths) {
            if (Test-Path $path -PathType Leaf) {
                $javaInstalled = $true
                Write-Host "Java RE 7 found at: $path" -ForegroundColor Green
                break
            }
        }

        if ($javaInstalled) {
            Write-Host "Java RE 7 Update 45 is already installed - skipping installation" -ForegroundColor Yellow
        } else {
            Write-Host "Installing Java RE 7 Update 45..." -ForegroundColor Yellow
            if (Test-Path "C:\Files\jre1.7.0_45.msi") {
                try {
                    $process = Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"C:\Files\jre1.7.0_45.msi`" /quiet" -Wait -PassThru -NoNewWindow

                    if ($process.ExitCode -eq 0) {
                        Write-Host "Java RE 7 Update 45 installation completed successfully!" -ForegroundColor Green
                    } else {
                        Write-Host "Java RE 7 Update 45 installation completed with exit code: $($process.ExitCode)" -ForegroundColor Yellow
                    }
                } catch {
                    Write-Host "Java RE 7 Update 45 installation failed: $_" -ForegroundColor Red
                }
            } else {
                Write-Host "Warning: Java installer not found at C:\Files\jre1.7.0_45.msi" -ForegroundColor Yellow
            }
        }

        # Install Chrome
        Write-Host "***" -ForegroundColor Green
        Write-Host "*** Checking Chrome installation"
        $chromeInstalled = $false
        $chromePaths = @(
            "C:\Program Files\Google\Chrome\Application\chrome.exe",
            "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        )

        foreach ($path in $chromePaths) {
            if (Test-Path $path) {
                $chromeInstalled = $true
                Write-Host "Chrome found at: $path" -ForegroundColor Green
                break
            }
        }

        if ($chromeInstalled) {
            Write-Host "Chrome is already installed - skipping installation" -ForegroundColor Yellow
        } else {
            Write-Host "Installing Chrome..." -ForegroundColor Yellow
            if (Test-Path "C:\Files\GoogleChromeStandaloneEnterprise64.msi") {
                try {
                    $process = Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"C:\Files\GoogleChromeStandaloneEnterprise64.msi`" /quiet" -Wait -PassThru -NoNewWindow

                    if ($process.ExitCode -eq 0) {
                        Write-Host "Chrome installation completed successfully!" -ForegroundColor Green
                    } else {
                        Write-Host "Chrome installation completed with exit code: $($process.ExitCode)" -ForegroundColor Yellow
                    }
                } catch {
                    Write-Host "Chrome installation failed: $_" -ForegroundColor Red
                }
            } else {
                Write-Host "Warning: Chrome installer not found at C:\Files\GoogleChromeStandaloneEnterprise64.msi" -ForegroundColor Yellow
            }
        }

        # Note: Citrix installation is now handled in a separate dedicated section

        # Note: Nuance installation is now handled in the Citrix installation section

        return "Success: System configuration completed"

    }

    Write-Color $result "Gray"
}

function Test-PCOnlineAndDNS {
    param([string]$computerName)

    try {
        Test-Connection -ComputerName $computerName -Count 1 -ErrorAction Stop | Out-Null
        $dnsStatus = if ($computerName -match '^\d+\.\d+\.\d+\.\d+$') { "IP Address" } else { "DNS Resolved" }
        return @{ Online = $true; DNSStatus = $dnsStatus; Error = $null }
    }
    catch {
        return @{ Online = $false; DNSStatus = "Failed"; Error = $_.Exception.Message }
    }
}

# Main script execution function
function Start-MainScript {
    # Initialize credentials
    if (-not (Initialize-Credentials)) {
        Write-Color "Cannot proceed without valid credentials" "Red"
        return
    }

    # Display enhanced ASCII banner
    Clear-Host
    Write-Host ""

    # Enhanced ASCII Box Banner (using safe characters)
    Write-Host "  =================================================================" -ForegroundColor "Cyan"
    Write-Host "                   COMPUTER MIGRATION WIZARD v4.0                  " -ForegroundColor "Yellow"
    Write-Host "  =================================================================" -ForegroundColor "Cyan"
    Write-Host ""
    Write-Host "    Welcome to the Computer Migration Wizard!                     " -ForegroundColor "Green"
    Write-Host ""
    Write-Host "    Features:                                                      " -ForegroundColor "White"
    Write-Host "    * Seamlessly transfer your computer setup with ease           " -ForegroundColor "Yellow"
    Write-Host "    * Automated installation and configuration                    " -ForegroundColor "Yellow"
    Write-Host "    * Smart profile and data migration                            " -ForegroundColor "Yellow"
    Write-Host "    * Enhanced security and reliability                           " -ForegroundColor "Yellow"
    Write-Host "    * Persistent credential management                            " -ForegroundColor "Yellow"
    Write-Host "    * Timeout and retry logic for robust installations            " -ForegroundColor "Yellow"
    Write-Host ""
    Write-Host "    Developed by: The greatest technician that ever lived         " -ForegroundColor "Magenta"
    Write-Host ""
    Write-Host "  =================================================================" -ForegroundColor "Cyan"
    Write-Host ""



    # Configuration Options
    Show-Banner "*** CONFIGURATION OPTIONS ***" "Yellow"

    # Local installer option (ask first)
    Write-Color "Do you want to use local installer files (D:\) instead of network paths? (Y/N): " "Yellow" -NoNewline
    $localInstallerInput = Read-Host
    $script:useLocalInstallers = $localInstallerInput -match '^(Y|y)$'

    if ($script:useLocalInstallers) {
        Write-Color "Local installer mode enabled - using D:\ paths on target computer" "Green"
        Write-Color "Note: This will check for files on D:\ of the target computer (copytopc)" "Gray"
    } else {
        Write-Color "Network installer mode enabled - using network paths" "Green"
    }
    Write-Host ""

    # Auto-yes option (ask second)
    Write-Color "Do you want to automatically answer 'Yes' to all prompts? (Y/N): " "Yellow" -NoNewline
    $autoYesInput = Read-Host
    $script:autoYes = $autoYesInput -match '^(Y|y)$'
    if ($script:autoYes) {
        Write-Color "Auto-yes mode enabled - all prompts will be automatically approved" "Green"
    } else {
        Write-Color "Manual mode enabled - you will be prompted for each option" "Yellow"
    }
    Write-Host ""

    #  Credential management option
    #  Write-Color "Do you want to clear saved credentials? (Y/N): " "Yellow" -NoNewline
    #  clearCredsInput = Read-Host
    #  if ($clearCredsInput -match '^(Y|y)$') {
    #    Clear-SavedCredentials
    #    Write-Color "You will need to re-enter credentials on next run." "Gray"
    #    Write-Host ""
    #    }

    # Get device IDs with connectivity validation loop
    do {
        $copyFromDeviceId = Read-Host -Prompt "`nEnter (copy from) Hostname or IP"
        $copyToDeviceId = Read-Host "Enter the (copy to) device ID"

        # Test connectivity
        Show-Banner "=== CONNECTIVITY TEST ===" "Yellow"
        Show-Progress "Testing connectivity to target computers" 1 6 "Yellow"

        Write-Color "Testing connectivity to source computer: $copyFromDeviceId" "Cyan"
        $result = Test-PCOnlineAndDNS -computerName $copyFromDeviceId

        Write-Color "Testing connectivity to target computer: $copyToDeviceId" "Cyan"
        $result2 = Test-PCOnlineAndDNS -computerName $copyToDeviceId

        $allOnline = $result.Online -and $result2.Online

        if ($allOnline) {
            Write-Color "$copyFromDeviceId is online. DNS Status: $($result.DNSStatus)" "Green"
			Execute-RemoteCommandWithCheck -ComputerName $copyFromDeviceId -CommandLine  "" | out-null
            Write-Color "$copyToDeviceId is online. DNS Status: $($result2.DNSStatus)" "Green"
			Execute-RemoteCommandWithCheck -ComputerName $copyToDeviceId -CommandLine  "" | out-null
            Write-Host ""
            break
        } else {
            Write-Host ""
            Write-Color "=== CONNECTIVITY ISSUES DETECTED ===" "Red"

            if (-not $result.Online) {
                Write-Color "SOURCE COMPUTER OFFLINE: $copyFromDeviceId" "Red"
                Write-Color "   Error: $($result.Error)" "Yellow"
            } else {
                Write-Color "Source computer online: $copyFromDeviceId" "Green"
            }

            if (-not $result2.Online) {
                Write-Color "TARGET COMPUTER OFFLINE: $copyToDeviceId" "Red"
                Write-Color "   Error: $($result2.Error)" "Yellow"
            } else {
                Write-Color "Target computer online: $copyToDeviceId" "Green"
            }

            Write-Host ""
            Write-Color "Please check the following:" "Yellow"
            Write-Color "• Verify computer names are spelled correctly" "Gray"
            Write-Color "• Ensure computers are powered on and connected to network" "Gray"
            Write-Color "• Check if computers are accessible from this location" "Gray"
            Write-Color "• Try using IP addresses instead of hostnames if DNS issues" "Gray"
            Write-Host ""

            $retry = Read-Host "Do you want to re-enter computer names? (Y/N)"
            if ($retry -notmatch '^(Y|y)$') {
                Write-Color "Exiting script due to connectivity issues." "Red"
                return
            }
            Write-Host ""
        }
    } while (-not $allOnline)

    # Initialize PSRemoting to target computer
    Show-Banner "=== PSREMOTING INITIALIZATION ===" "Blue"
    Show-Progress "Establishing persistent connection to target computer" 2 6 "Yellow"

    if (-not (Initialize-PSRemoting -TargetComputer $copyToDeviceId)) {
        Write-Color "Failed to establish PSRemoting connection. Cannot continue." "Red"
        return
    }

    # Determine configuration type
    Show-Banner "=== CONFIGURATION DETECTION ===" "Blue"
    Show-Progress "Detecting target computer configuration" 3 6 "Yellow"

    $installType1 = $false
    $installType2 = $false

    try {
        Import-Module ActiveDirectory -ErrorAction Stop
        $destination = $copyToDeviceId.Trim()
        $destinationAD = if ($destination[-1] -ne '$') { "$destination$" } else { $destination }

        if (Get-ADComputer -Identity $destinationAD -ErrorAction SilentlyContinue) {
            $groups = Get-ADComputer -Identity $destinationAD -Properties MemberOf |
                Select-Object -ExpandProperty MemberOf |
                ForEach-Object { (Get-ADGroup $_).Name }

            $installType1 = $groups | Where-Object { $_ -like "*type1*"}
            $installType2 = $groups | Where-Object { $_ -like "*type2*"}

            Write-Color "Target computer AD groups detected:" "Green"
            $groups | ForEach-Object { Write-Color "  $_" "Gray" }

            if ($installType1) { Write-Color "Type 1 membership detected" "Yellow" }
            if ($installType2) { Write-Color "Type 2 membership detected" "Yellow" }
        }
    }
    catch {
        Write-Color "Could not check AD groups: $_" "Yellow"
        Write-Color "Proceeding with manual configuration selection..." "Gray"
    }



    # Use already-detected PC type from AD groups (no need to re-detect)
    $sourceInstallType1 = $script:sourceGroups | Where-Object { $_ -like "*type1*"}
    $sourceInstallType2 = $script:sourceGroups | Where-Object { $_ -like "*type2*"}
    $isType2PC = [bool]$sourceInstallType2

    if ($isType2PC) {
        Write-Color "Using already-detected Type 2 configuration from AD groups" "Green"
    } elseif ($sourceInstallType1) {
        Write-Color "Using already-detected Type 1 configuration from AD groups" "Green"
    } else {
        Write-Color "No Imprivata type detected from AD groups - using standard configuration" "Yellow"
    }

    # Profile backup
    Show-Banner "=== PROFILE BACKUP IN PROGRESS ===" "Green"
    Show-Progress "Backing up user profiles" 2 11 "Yellow"

    Backup-UserProfiles -SourceComputer $copyFromDeviceId -TargetComputer $copyToDeviceId -IsType2PC $isType2PC

    # AD Configuration
    Show-Banner "=== ACTIVE DIRECTORY CONFIGURATION ===" "Blue"
    Show-Progress "Configuring Active Directory settings" 3 11 "Yellow"

    $sourceGroups = Set-ADConfiguration -SourceComputer $copyFromDeviceId -TargetComputer $copyToDeviceId

    # Store source groups for later use
    $script:sourceGroups = $sourceGroups

    # Copy installer files
    Show-Banner "=== FILE COPYING ===" "Green"
    Show-Progress "Copying installer files to target computer" 4 11 "Yellow"

    Copy-FilesToTarget -TargetComputer $copyToDeviceId

    # Install Lexmark Driver (BEFORE printer migration)
    Show-Banner "=== LEXMARK DRIVER INSTALLATION ===" "Green"
    Show-Progress "Installing Lexmark Universal Driver" 5 11 "Yellow"

    Install-LexmarkDriver -TargetComputer $copyToDeviceId

    # Printer Migration (AFTER Lexmark driver is installed)
    Show-Banner "*** PRINTER MIGRATION ***" "Green"
    Show-Progress "Migrating printers" 6 11 "Yellow"

    Install-PrinterMigration -SourceComputer $copyFromDeviceId -TargetComputer $copyToDeviceId

    # System Configuration
    Show-Banner "=== SYSTEM CONFIGURATION ===" "Green"
    Show-Progress "Installing system configuration" 7 11 "Yellow"

    Install-SystemConfiguration -TargetComputer $copyToDeviceId

    # Citrix Installation (separate section with user choice)
    Show-Banner "*** CITRIX INSTALLATION ***" "Green"
    Show-Progress "Installing Citrix software" 7.5 11 "Yellow"

    if ($script:autoYes) {
        Write-Color "Auto-yes mode: Installing Citrix Receiver 4.9 (default)" "Yellow"
        Install-CitrixSoftware -TargetComputer $copyToDeviceId -InstallType "Receiver"
    } else {
        Write-Color "Select Citrix installation option:" "Cyan"
        Write-Color "[1] Citrix Receiver 4.9 (Legacy)" "Gray"
        Write-Color "[2] Citrix Workspace (Modern)" "Gray"

        do {
            $citrixChoice = Read-Host "Enter your choice (1/2)"
        } while ($citrixChoice -notin @("1", "2"))

        $installType = if ($citrixChoice -eq "1") { "Receiver" } else { "Workspace" }
        Write-Color "Installing Citrix $installType..." "Yellow"
        Install-CitrixSoftware -TargetComputer $copyToDeviceId -InstallType $installType
    }

    # Office 365 Installation (separate section with Type 2 check)
    Show-Banner "*** OFFICE 365 INSTALLATION ***" "Green"
    Show-Progress "Installing Office 365 (if applicable)" 8 11 "Yellow"

    if ($isType2PC) {
        Write-Color "Type 2 PC detected - Office 365 installation will be SKIPPED" "Yellow"
        Write-Color "Type 2 PCs do not receive Office 365 installation" "Gray"
    } else {
        Write-Color "Type 1 PC detected - Installing Office 365" "Green"
        Install-OfficeApplication -TargetComputer $copyToDeviceId -SourceGroups $script:sourceGroups
    }

    # Imprivata Agent Installation (separate section)
    Show-Banner "+++ IMPRIVATA AGENT INSTALLATION +++" "Magenta"
    Show-Progress "Installing Imprivata Agent (if applicable)" 9 11 "Yellow"

    $sourceInstallType1 = $script:sourceGroups | Where-Object { $_ -like "*type1*"}
    $sourceInstallType2 = $script:sourceGroups | Where-Object { $_ -like "*type2*"}

    if ($sourceInstallType1 -or $sourceInstallType2) {
        if ($sourceInstallType1) {
            Write-Color "Type 1 membership detected - Installing Type 1 Imprivata" "Yellow"
            Install-ImprivataAgent -TargetComputer $copyToDeviceId -AgentType 1 -SourceComputer $copyFromDeviceId
        } elseif ($sourceInstallType2) {
            Write-Color "Type 2 membership detected - Installing Type 2 Imprivata with autologin transfer" "Yellow"
            Install-ImprivataAgent -TargetComputer $copyToDeviceId -AgentType 2 -SourceComputer $copyFromDeviceId
        }
    } else {
        Write-Color "No Type 1 or Type 2 membership detected - Imprivata will NOT be installed" "Magenta"
    }

    # VPN Client Installation (for Laptops only)
    if ($copyFromDeviceId.ToUpper().EndsWith('L')) {
        Show-Banner "=== VPN CLIENT INSTALLATION ===" "Green"
        Show-Progress "Installing Cisco Secure Client VPN (Laptop detected)" 9.5 11 "Yellow"

        Write-Color "Source computer '$copyFromDeviceId' ends with 'L' - Installing VPN client for laptop" "Yellow"
        Install-VPNClient -TargetComputer $copyToDeviceId
    } else {
        Write-Color "Source computer '$copyFromDeviceId' does not end with 'L' - VPN client will NOT be installed" "Gray"
    }

    # Dell Command Update (before renaming)
    Show-Banner "*** DELL COMMAND UPDATE ***" "Green"
    Show-Progress "Installing and configuring Dell Command Update" 10 11 "Yellow"

    Install-DellCommandUpdate -TargetComputer $copyToDeviceId

    # Cleanup installer files before renaming
    Show-Banner "=== CLEANUP INSTALLER FILES ===" "Green"
    Show-Progress "Cleaning up installer files" 10.5 11 "Yellow"

    Write-Color "Cleaning up C:\Files folder on target computer (preserving profile backups)..." "Cyan"
    $cleanupResult = Invoke-RemoteCommand -TargetComputer $copyToDeviceId -UseFallback -ScriptBlock {
        try {
            if (Test-Path "C:\Files") {
                Write-Host "Cleaning up installer files while preserving profile backups..." -ForegroundColor Yellow

                # Get all items in C:\Files
                $allItems = Get-ChildItem -Path "C:\Files" -ErrorAction SilentlyContinue
                $cleanedItems = 0
                $preservedItems = 0

                foreach ($item in $allItems) {
                    # Preserve the specific user_profile_backups folder
                    if ($item.Name -eq "user_profile_backups") {
                        Write-Host "  Preserving: $($item.Name)" -ForegroundColor Green
                        $preservedItems++
                    } else {
                        Write-Host "  Removing: $($item.Name)" -ForegroundColor Yellow
                        Remove-Item -Path $item.FullName -Recurse -Force -ErrorAction SilentlyContinue
                        $cleanedItems++
                    }
                }

                Write-Host "Cleanup completed: $cleanedItems items removed, $preservedItems items preserved" -ForegroundColor Green
                return "Success: Installer files cleaned up - $cleanedItems items processed, $preservedItems items preserved"
            } else {
                Write-Host "C:\Files folder not found - nothing to clean up" -ForegroundColor Gray
                return "Info: No files to clean up"
            }
        }
        catch {
            Write-Host "Warning: Could not clean up C:\Files folder - $_" -ForegroundColor Yellow
            return "Warning: Cleanup failed - $_"
        }
    }
    Write-Color $cleanupResult "Gray"

    # Computer Renaming with advanced logic
    Show-Banner "*** COMPUTER NAMING ***" "Green"
    Show-Progress "Configuring new computer name" 10.8 11 "Yellow"

    $runRename = Read-Host "Do you want to run the rename logic? (Y/N)"
    if ($runRename -eq 'Y') {
        do {
            $deviceType = (Read-Host "Is the target device a Desktop or Laptop? (D/L)").ToUpper()
        } until ($deviceType -in @('D','L'))
        $suffix = $deviceType

        # Generate base name from source PC (all but last 3 chars)
        $sourcePC = $copyFromDeviceId.Trim().ToUpper()
        $targetPC = $copyToDeviceId.Trim().ToUpper()
        $baseName = $sourcePC.Substring(0, $sourcePC.Length - 3)

        # Check naming convention
        if ($targetPC -notmatch "^$($baseName)\d{2}$suffix$") {
            Write-Color "`n[!] Target name doesn't match source naming convention" "Yellow"

            # AD query for existing names
            $existingNames = Get-ADComputer -Filter "Name -like `'$baseName*`'" |
                Select-Object -ExpandProperty Name |
                Where-Object { $_ -imatch "^$baseName\d{2}$suffix$" } |
                ForEach-Object { $_.ToUpper() }

            # Generate available numbers
            $usedNumbers = $existingNames | ForEach-Object {
                if ($_ -imatch "$baseName(\d{2})$suffix$") { $matches[1] }
            }
            $allPossible = 00..99 | ForEach-Object { "{0:D2}" -f $_ }
            $availableNumbers = $allPossible | Where-Object { $_ -notin $usedNumbers } | Select-Object -First 20

            # Display available names
            Write-Color "`nAvailable PC numbers for $baseName ($suffix)" "Cyan"
            $i = 0
            $availableNumbers | ForEach-Object {
                Write-Color "  [$i] ${baseName}$_$suffix" "Gray"
                $i++
            }

            # Get user selection
            do {
                $selected = Read-Host "`nEnter number choice (0-19) or manual number (00-99)"
                if ($selected -match "^\d{2}$") {
                    $number = $selected
                    break
                } elseif ($selected -match "^\d+$" -and [int]$selected -lt 20) {
                    $number = $availableNumbers[[int]$selected]
                    break
                }
                Write-Color "Invalid selection. Please try again." "Red"
            } while ($true)

            $newName = "${baseName}${number}${suffix}"

            # Final confirmation and rename
            Write-Color "`nProposed new name: $newName" "Cyan"
            $confirm = Read-Host "Confirm rename? (Y/N)"
            if ($confirm -eq "Y") {
                try {
                    Write-Color "Renaming computer using local PowerShell cmdlet..." "Yellow"

                    # Use the same method as working.ps1 - direct Rename-Computer cmdlet
                    Rename-Computer -ComputerName $targetPC -NewName $newName -DomainCredential $script:storedCredentials -Force -Restart -ErrorAction Stop
                    $copyToDeviceId = $newName
                    Write-Color "Successfully renamed to $newName" "Green"
                    Start-Sleep -Seconds 10
                } catch {
                    Write-Color "Rename failed: $_" "Red"
                }
            }
        }
    }

    # Completion
    Show-Banner "!!! MIGRATION COMPLETE !!!" "Green"
    Show-Progress "Migration completed successfully" 11 11 "Yellow"
    Show-CompletionAnimation

    # Clean up PSSession
    if ($null -ne $script:targetSession) {
        try {
            Remove-PSSession $script:targetSession
            Write-Color "PSSession cleaned up successfully" "Gray"
        }
        catch {
            Write-Color "Warning: Could not clean up PSSession - $_" "Yellow"
        }
        $script:targetSession = $null
    }

    # Keep restart prompt
    $restart = Read-Host "Press Enter to restart the script or type exit to quit"
    if ($restart -ne "exit") {
        Remove-Variable * -ErrorAction SilentlyContinue
        & $PSCommandPath
    }
}

# Global script variables
$script:useLocalInstallers = $false
$script:autoYes = $false

# Initial script execution
Start-MainScript
