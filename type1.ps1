# Install Imprivata Type 1
        Write-Color "`nInstalling Imprivata Type 1..." "Cyan"
        
        # Create Imprivata directories
        $localImprivataDir = Join-Path $env:TEMP "Imprivata"
        $remoteImprivataDir = "\\$copyToDeviceId\C$\files\Imprivata"

        New-Item -Path $localImprivataDir -ItemType Directory -Force | Out-Null
        New-Item -Path $remoteImprivataDir -ItemType Directory -Force | Out-Null

        # Copy Imprivata files
        Copy-Item -Path "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Imprivata_push\*" -Destination $remoteImprivataDir -Recurse -Force

        # Install Imprivata
        Invoke-Command -Session $session -ScriptBlock {
            Start-Process -FilePath "C:\Temp\Imprivata\InstallType1.cmd" -WorkingDirectory "C:\Temp\Imprivata" -Wait -NoNewWindow
        }

        # Cleanup
        Remove-Item -Path $localImprivataDir -Recurse -Force -ErrorAction SilentlyContinue
        Remove-Item -Path $remoteImprivataDir -Recurse -Force -ErrorAction SilentlyContinue
    }
    finally {
        if ($null -ne $session) {
            Remove-PSSession $session
        }
    }