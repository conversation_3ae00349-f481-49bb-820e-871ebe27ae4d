# Install Missing Components Script
# Downloads and installs missing applications and drivers
# Developed by: The greatest technician that ever lived

param(
    [string]$TargetPC,
    [PSCredential]$Credential,
    [array]$MissingApps,
    [array]$MissingDrivers,
    [switch]$AutoInstall
)

# Function to display banner
function Show-InstallBanner {
    Clear-Host
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "    Missing Components Installer" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "Developed by: The greatest technician that ever lived" -ForegroundColor Green
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host ""
}

# Function to check if <PERSON>y is installed
function Test-ChocolateyInstalled {
    param([string]$ComputerName, [PSCredential]$Credential)
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            return (Get-Command choco -ErrorAction SilentlyContinue) -ne $null
        } else {
            $result = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock {
                (Get-Command choco -ErrorAction SilentlyContinue) -ne $null
            } -ErrorAction SilentlyContinue
            return $result
        }
    } catch {
        return $false
    }
}

# Function to install Chocolatey
function Install-Chocolatey {
    param([string]$ComputerName, [PSCredential]$Credential)
    
    Write-Host "Installing Chocolatey on $ComputerName..." -ForegroundColor Yellow
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            Set-ExecutionPolicy Bypass -Scope Process -Force
            [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
            Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        } else {
            Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock {
                Set-ExecutionPolicy Bypass -Scope Process -Force
                [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
                Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
            }
        }
        Write-Host "Chocolatey installed successfully!" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "Failed to install Chocolatey: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to check if winget is available
function Test-WingetAvailable {
    param([string]$ComputerName, [PSCredential]$Credential)
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            return (Get-Command winget -ErrorAction SilentlyContinue) -ne $null
        } else {
            $result = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock {
                (Get-Command winget -ErrorAction SilentlyContinue) -ne $null
            } -ErrorAction SilentlyContinue
            return $result
        }
    } catch {
        return $false
    }
}

# Function to map application names to package managers
function Get-PackageInfo {
    param([string]$AppName)
    
    $packageMap = @{
        "Google Update Helper" = @{
            Chocolatey = "googlechrome"
            Winget = "Google.Chrome"
            Description = "Google Chrome (includes update helper)"
        }
        "Microsoft Update Health Tools" = @{
            Chocolatey = $null
            Winget = $null
            Description = "Windows Update component (install via Windows Update)"
            InstallMethod = "WindowsUpdate"
        }
        "Island Extension Utility" = @{
            Chocolatey = $null
            Winget = $null
            Description = "Island browser extension utility"
            InstallMethod = "Manual"
        }
        "7-Zip" = @{
            Chocolatey = "7zip"
            Winget = "7zip.7zip"
            Description = "File archiver"
        }
        "Adobe Acrobat Reader" = @{
            Chocolatey = "adobereader"
            Winget = "Adobe.Acrobat.Reader.64-bit"
            Description = "PDF reader"
        }
        "Citrix Workspace" = @{
            Chocolatey = "citrix-workspace"
            Winget = "Citrix.Workspace"
            Description = "Citrix remote access client"
        }
    }
    
    # Try exact match first
    if ($packageMap.ContainsKey($AppName)) {
        return $packageMap[$AppName]
    }
    
    # Try partial matches
    foreach ($key in $packageMap.Keys) {
        if ($AppName -like "*$key*" -or $key -like "*$AppName*") {
            return $packageMap[$key]
        }
    }
    
    return $null
}

# Function to install application via Chocolatey
function Install-ViaChocolatey {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential,
        [string]$PackageName,
        [string]$AppName
    )
    
    Write-Host "  Installing $AppName via Chocolatey..." -ForegroundColor Yellow
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            $result = & choco install $PackageName -y
        } else {
            $result = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock {
                param($pkg)
                & choco install $pkg -y
            } -ArgumentList $PackageName
        }
        
        Write-Host "  Successfully installed $AppName via Chocolatey!" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "  Failed to install $AppName via Chocolatey: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to install application via winget
function Install-ViaWinget {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential,
        [string]$PackageId,
        [string]$AppName
    )
    
    Write-Host "  Installing $AppName via winget..." -ForegroundColor Yellow
    
    try {
        if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
            $result = & winget install --id $PackageId --silent --accept-package-agreements --accept-source-agreements
        } else {
            $result = Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock {
                param($id)
                & winget install --id $id --silent --accept-package-agreements --accept-source-agreements
            } -ArgumentList $PackageId
        }
        
        Write-Host "  Successfully installed $AppName via winget!" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "  Failed to install $AppName via winget: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to install missing applications
function Install-MissingApplications {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential,
        [array]$Apps,
        [bool]$AutoInstall
    )
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "INSTALLING MISSING APPLICATIONS" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    if ($Apps.Count -eq 0) {
        Write-Host "No missing applications to install." -ForegroundColor Green
        return
    }
    
    # Check package managers
    $chocoAvailable = Test-ChocolateyInstalled -ComputerName $ComputerName -Credential $Credential
    $wingetAvailable = Test-WingetAvailable -ComputerName $ComputerName -Credential $Credential
    
    Write-Host "Package manager availability:" -ForegroundColor Cyan
    Write-Host "  Chocolatey: $(if($chocoAvailable){'Available'}else{'Not Available'})" -ForegroundColor $(if($chocoAvailable){'Green'}else{'Red'})
    Write-Host "  Winget: $(if($wingetAvailable){'Available'}else{'Not Available'})" -ForegroundColor $(if($wingetAvailable){'Green'}else{'Red'})
    
    # Install Chocolatey if not available
    if (-not $chocoAvailable) {
        if ($AutoInstall -or (Read-Host "`nInstall Chocolatey? (y/n)") -eq 'y') {
            $chocoAvailable = Install-Chocolatey -ComputerName $ComputerName -Credential $Credential
        }
    }
    
    foreach ($app in $Apps) {
        Write-Host "`nProcessing: $($app.DisplayName)" -ForegroundColor Yellow
        
        $packageInfo = Get-PackageInfo -AppName $app.DisplayName
        
        if ($packageInfo) {
            $installed = $false
            
            # Try Chocolatey first
            if ($chocoAvailable -and $packageInfo.Chocolatey) {
                if ($AutoInstall -or (Read-Host "  Install $($app.DisplayName) via Chocolatey? (y/n)") -eq 'y') {
                    $installed = Install-ViaChocolatey -ComputerName $ComputerName -Credential $Credential -PackageName $packageInfo.Chocolatey -AppName $app.DisplayName
                }
            }
            
            # Try winget if Chocolatey failed or not available
            if (-not $installed -and $wingetAvailable -and $packageInfo.Winget) {
                if ($AutoInstall -or (Read-Host "  Install $($app.DisplayName) via winget? (y/n)") -eq 'y') {
                    $installed = Install-ViaWinget -ComputerName $ComputerName -Credential $Credential -PackageId $packageInfo.Winget -AppName $app.DisplayName
                }
            }
            
            # Special handling for specific apps
            if (-not $installed -and $packageInfo.InstallMethod) {
                switch ($packageInfo.InstallMethod) {
                    "WindowsUpdate" {
                        Write-Host "  $($app.DisplayName) should be installed via Windows Update" -ForegroundColor Yellow
                    }
                    "Manual" {
                        Write-Host "  $($app.DisplayName) requires manual installation" -ForegroundColor Yellow
                    }
                }
            }
            
            if (-not $installed -and -not $packageInfo.InstallMethod) {
                Write-Host "  Could not install $($app.DisplayName) - no suitable package manager available" -ForegroundColor Red
            }
        } else {
            Write-Host "  No package mapping found for $($app.DisplayName)" -ForegroundColor Yellow
            Write-Host "  You may need to install this manually or add it to the package mapping" -ForegroundColor Yellow
        }
    }
}

# Function to handle driver installation
function Install-MissingDrivers {
    param(
        [string]$ComputerName,
        [PSCredential]$Credential,
        [array]$Drivers,
        [bool]$AutoInstall
    )
    
    Write-Host "`n=============================================" -ForegroundColor Cyan
    Write-Host "INSTALLING MISSING DRIVERS" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    
    if ($Drivers.Count -eq 0) {
        Write-Host "No missing drivers to install." -ForegroundColor Green
        return
    }
    
    Write-Host "Driver installation options:" -ForegroundColor Cyan
    Write-Host "1. Windows Update (recommended)" -ForegroundColor White
    Write-Host "2. Device Manager scan" -ForegroundColor White
    Write-Host "3. Manual driver download" -ForegroundColor White
    
    foreach ($driver in $Drivers) {
        Write-Host "`nProcessing driver: $($driver.DeviceName)" -ForegroundColor Yellow
        
        if ($driver.DeviceName -like "*Network Printer*") {
            Write-Host "  This appears to be a network printer driver" -ForegroundColor Cyan
            Write-Host "  Recommendation: Add the printer through Windows Settings > Printers & Scanners" -ForegroundColor Yellow
        }
        elseif ($driver.DeviceName -like "*WinUsb*") {
            Write-Host "  This appears to be a USB device driver" -ForegroundColor Cyan
            Write-Host "  Recommendation: Connect the USB device and let Windows auto-install" -ForegroundColor Yellow
        }
        else {
            Write-Host "  Generic driver - may need manual installation" -ForegroundColor Yellow
        }
    }
    
    # Option to run Windows Update
    if ($AutoInstall -or (Read-Host "`nRun Windows Update to search for drivers? (y/n)") -eq 'y') {
        Write-Host "Running Windows Update driver search..." -ForegroundColor Yellow
        try {
            if ($ComputerName -eq $env:COMPUTERNAME -or $ComputerName -eq "localhost" -or $ComputerName -eq ".") {
                Start-Process "ms-settings:windowsupdate" -Wait
            } else {
                Invoke-Command -ComputerName $ComputerName -Credential $Credential -ScriptBlock {
                    # Trigger Windows Update scan
                    $updateSession = New-Object -ComObject Microsoft.Update.Session
                    $updateSearcher = $updateSession.CreateUpdateSearcher()
                    $searchResult = $updateSearcher.Search("IsInstalled=0 and Type='Driver'")
                    Write-Host "Found $($searchResult.Updates.Count) driver updates available"
                }
            }
        } catch {
            Write-Host "Failed to run Windows Update: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Main execution
Show-InstallBanner

if (-not $TargetPC) {
    $TargetPC = Read-Host "Enter the target PC name to install missing components on"
}

if (-not $Credential -and $TargetPC -ne $env:COMPUTERNAME -and $TargetPC -ne "localhost" -and $TargetPC -ne ".") {
    Write-Host "Remote computer access detected. Credentials required." -ForegroundColor Yellow
    $Credential = Get-Credential -Message "Enter credentials for remote computer access"
}

Write-Host "Target PC: $TargetPC" -ForegroundColor Cyan
Write-Host "Auto-install mode: $(if($AutoInstall){'Enabled'}else{'Disabled (will prompt for each item)'})" -ForegroundColor Cyan

# For demo purposes, using the results from the previous comparison
if (-not $MissingApps) {
    $MissingApps = @(
        @{DisplayName="Google Update Helper"; DisplayVersion="1.3.35.441"},
        @{DisplayName="Microsoft Update Health Tools"; DisplayVersion="********"}
    )
}

if (-not $MissingDrivers) {
    $MissingDrivers = @(
        @{DeviceName="Network Printer Connection"; DriverVersion="10.0.19041.1"},
        @{DeviceName="WinUsb Device"; DriverVersion="10.0.19041.1"}
    )
}

# Install missing applications
Install-MissingApplications -ComputerName $TargetPC -Credential $Credential -Apps $MissingApps -AutoInstall $AutoInstall

# Install missing drivers  
Install-MissingDrivers -ComputerName $TargetPC -Credential $Credential -Drivers $MissingDrivers -AutoInstall $AutoInstall

Write-Host "`n=============================================" -ForegroundColor Cyan
Write-Host "INSTALLATION COMPLETE" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

Read-Host "`nPress Enter to exit"
