# Test script to demonstrate the cleaned up PSRemoting output

# Mock functions to simulate the behavior
function Write-Color {
    param([string]$Message, [string]$Color = "Gray", [switch]$NoNewline)
    if ($NoNewline) { Write-Host $Message -ForegroundColor $Color -NoNewline }
    else { Write-Host $Message -ForegroundColor $Color }
}

Write-Host "`n=== BEFORE (Verbose Output) ===" -ForegroundColor Red
Write-Color "Initializing PSRemoting connection to MMG-3XNFN94..." "Cyan"
Write-Color "Testing network connectivity to MMG-3XNFN94..." "Yellow"
Write-Color "Network connectivity successful - Average response time: 3.5ms" "Green"
Write-Color "Validating credentials against target computer..." "Yellow"
Write-Color "Credential validation successful - connecting as: mclaren\a.runyan.ark" "Green"
Write-Color "Target computer: MMG-3XNFN94" "Gray"
Write-Color "Checking WinRM service status..." "Yellow"
Write-Color "WinRM service is not running. Attempting to enable PSRemoting..." "Yellow"
Write-Color "PSRemoting enable command sent successfully. Waiting 15 seconds for service restart..." "Green"
Write-Color "Testing WSMan connectivity..." "Yellow"
Write-Color "WSMan connectivity test successful" "Green"
Write-Color "Creating persistent PSSession..." "Yellow"
Write-Color "PSSession established successfully!" "Green"
Write-Color "PSSession test successful - connected to: MMG-3XNFN94" "Green"

Write-Host "`nDuring operation when connection is lost:" -ForegroundColor Yellow
Write-Color "Remote command failed (attempt 1): Processing data for a remote command failed with the following error message: The I/O operation has been aborted because of either a thread exit or an application request. For more information, see the about_Remote_Troubleshooting Help topic." "Yellow"
Write-Color "Retrying..." "Yellow"
Write-Color "PSSession is not available or has been closed" "Red"
Write-Color "PSSession not available, attempting repair..." "Yellow"
Write-Color "Attempting to repair PSSession..." "Yellow"
Write-Color "Initializing PSRemoting connection to mmg-3xnfn94..." "Cyan"
Write-Color "Testing network connectivity to mmg-3xnfn94..." "Yellow"
Write-Color "Network connectivity successful - Average response time: 3.5ms" "Green"
Write-Color "Validating credentials against target computer..." "Yellow"
Write-Color "Credential validation successful - connecting as: mclaren\a.runyan.ark" "Green"
Write-Color "Target computer: MMG-3XNFN94" "Gray"
Write-Color "Checking WinRM service status..." "Yellow"
Write-Color "WinRM service is not running. Attempting to enable PSRemoting..." "Yellow"
Write-Color "PSRemoting enable command sent successfully. Waiting 15 seconds for service restart..." "Green"
Write-Color "Creating persistent PSSession..." "Yellow"
Write-Color "PSSession established successfully!" "Green"
Write-Color "PSSession test successful - connected to: MMG-3XNFN94" "Green"

Write-Host "`n=== AFTER (Clean Output) ===" -ForegroundColor Green
Write-Color "Connecting to MMG-3XNFN94..." "Cyan"
Write-Color "Network connectivity: OK (3.5ms)" "Green"
Write-Color "Credentials: OK (connecting as mclaren\a.runyan.ark)" "Green"
Write-Color "Enabling PSRemoting on target computer..." "Yellow"
Write-Color "PSRemoting enabled successfully. Waiting for service restart..." "Green"
Write-Color "Establishing secure session..." "Yellow"
Write-Color "Connection established successfully to: MMG-3XNFN94" "Green"

Write-Host "`nDuring operation when connection is lost (quiet mode):" -ForegroundColor Yellow
Write-Color "Remote command failed: The I/O operation has been aborted" "Yellow"
Write-Color "Connection lost, reconnecting..." "Yellow"
Write-Color "Reconnecting to mmg-3xnfn94..." "Yellow"
Write-Color "Connection established successfully to: MMG-3XNFN94" "Green"

Write-Host "`n=== SUMMARY ===" -ForegroundColor Cyan
Write-Host "The cleaned up version reduces output by approximately 70% while maintaining essential information." -ForegroundColor White
Write-Host "Key improvements:" -ForegroundColor White
Write-Host "• Consolidated connection steps into fewer, clearer messages" -ForegroundColor Gray
Write-Host "• Reduced verbose retry and repair messages" -ForegroundColor Gray
Write-Host "• Only shows detailed diagnostics when there are actual failures" -ForegroundColor Gray
Write-Host "• Uses quiet mode for subsequent operations after initial connection" -ForegroundColor Gray
