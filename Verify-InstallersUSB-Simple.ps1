# Simple USB Installer Verification Script
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

function Write-Color {
    param([string]$Text, [string]$Color = "White")
    Write-Host $Text -ForegroundColor $Color
}

function Get-AvailableDrives {
    Write-Color "Detecting available drives..." "Cyan"
    
    $drives = @()
    $driveCount = 0
    
    $allDrives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -ne "C:" }
    
    Write-Color "REMOVABLE DRIVES (USB/External):" "Yellow"
    $removableDrives = $allDrives | Where-Object { $_.DriveType -eq 2 }
    
    foreach ($drive in $removableDrives) {
        $driveCount++
        $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
        $volumeLabel = if ($drive.VolumeName) { "`"$($drive.VolumeName)`"" } else { "(No Label)" }
        
        Write-Color "[$driveCount] $($drive.DeviceID) $volumeLabel (USB/Removable) - $freeSpaceGB GB free / $totalSpaceGB GB total" "White"
        
        $drives += @{
            Number = $driveCount
            DeviceID = $drive.DeviceID
            VolumeName = $drive.VolumeName
            FreeSpace = $drive.FreeSpace
            Size = $drive.Size
            Type = "USB/Removable"
        }
    }
    
    Write-Host ""
    Write-Color "FIXED DRIVES (Local Hard Drives):" "Yellow"
    $fixedDrives = $allDrives | Where-Object { $_.DriveType -eq 3 }
    
    foreach ($drive in $fixedDrives) {
        $driveCount++
        $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
        $volumeLabel = if ($drive.VolumeName) { "`"$($drive.VolumeName)`"" } else { "(No Label)" }
        
        Write-Color "[$driveCount] $($drive.DeviceID) $volumeLabel (Fixed Drive) - $freeSpaceGB GB free / $totalSpaceGB GB total" "White"
        
        $drives += @{
            Number = $driveCount
            DeviceID = $drive.DeviceID
            VolumeName = $drive.VolumeName
            FreeSpace = $drive.FreeSpace
            Size = $drive.Size
            Type = "Fixed Drive"
        }
    }
    
    if ($driveCount -eq 0) {
        Write-Color "ERROR: No suitable drives found!" "Red"
        Read-Host "Press Enter to exit"
        exit 1
    }
    
    Write-Host ""
    Write-Color "[0] Exit" "Gray"
    Write-Host ""
    
    return $drives
}

function Select-Drive {
    param([array]$Drives)
    
    do {
        $choice = Read-Host "Please select a drive to verify (1-$($Drives.Count) or 0 to exit)"
        
        if ($choice -eq "0") {
            Write-Color "Operation cancelled by user." "Yellow"
            exit 0
        }
        
        $selectedDrive = $Drives | Where-Object { $_.Number -eq [int]$choice }
        
        if (-not $selectedDrive) {
            Write-Color "Invalid selection. Please try again." "Red"
        }
    } while (-not $selectedDrive)
    
    return $selectedDrive
}

function Test-InstallerFiles {
    param([string]$TargetDrive)
    
    Write-Color "Checking for required installer files on $TargetDrive..." "Cyan"
    Write-Host ""
    
    $missingFiles = 0
    $totalFiles = 0
    $presentFiles = 0
    
    # Define required folders
    $requiredFolders = @(
        @{ Path = "$TargetDrive\Splashtop_Push"; Name = "Splashtop" },
        @{ Path = "$TargetDrive\sxs"; Name = ".NET 3.5 SXS" },
        @{ Path = "$TargetDrive\Lexmark_Universal_v2_UD1_PostScript_3_Emulation"; Name = "Lexmark Universal Driver" },
        @{ Path = "$TargetDrive\o365"; Name = "Office 365" },
        @{ Path = "$TargetDrive\4.9_LTSR2"; Name = "Citrix 4.9 LTSR2" },
        @{ Path = "$TargetDrive\Nuance121"; Name = "Nuance" },
        @{ Path = "$TargetDrive\FSTools"; Name = "FSTools" },
        @{ Path = "$TargetDrive\LexmarkGDI"; Name = "Lexmark GDI Driver" },
        @{ Path = "$TargetDrive\Imprivata_push"; Name = "Imprivata Push" }
    )
    
    # Define required files
    $requiredFiles = @(
        @{ Path = "$TargetDrive\jre1.7.0_45.msi"; Name = "Java RE 7" },
        @{ Path = "$TargetDrive\GoogleChromeStandaloneEnterprise64.msi"; Name = "Google Chrome" },
        @{ Path = "$TargetDrive\Win_Photo_Viewer.reg"; Name = "Windows Photo Viewer Registry" },
        @{ Path = "$TargetDrive\Volume.ps1"; Name = "Volume PowerShell Script" },
        @{ Path = "$TargetDrive\BitLockerAD.ps1"; Name = "BitLocker PowerShell Script" },
        @{ Path = "$TargetDrive\Commandupdate.EXE"; Name = "Dell Command Update" }
    )
    
    # Check folders
    foreach ($folder in $requiredFolders) {
        $totalFiles++
        if (Test-Path $folder.Path -PathType Container) {
            Write-Color "✓ FOUND: $($folder.Name) folder" "Green"
            $presentFiles++
        } else {
            Write-Color "✗ MISSING: $($folder.Name) folder ($($folder.Path))" "Red"
            $missingFiles++
        }
    }
    
    # Check files
    foreach ($file in $requiredFiles) {
        $totalFiles++
        if (Test-Path $file.Path -PathType Leaf) {
            Write-Color "✓ FOUND: $($file.Name) file" "Green"
            $presentFiles++
        } else {
            Write-Color "✗ MISSING: $($file.Name) file ($($file.Path))" "Red"
            $missingFiles++
        }
    }
    
    Write-Host ""
    Write-Color "********************************************************************************" "Cyan"
    Write-Color "*                           VERIFICATION RESULTS                              *" "Cyan"
    Write-Color "********************************************************************************" "Cyan"
    Write-Host ""
    
    Write-Color "Total items checked: $totalFiles" "White"
    Write-Color "Missing items: $missingFiles" "Red"
    Write-Color "Present items: $presentFiles" "Green"
    Write-Host ""
    
    if ($missingFiles -eq 0) {
        Write-Color "✓ SUCCESS: All required installer files are present on $TargetDrive!" "Green"
        Write-Color "  You can now use these files with the migration script." "Green"
    } else {
        Write-Color "✗ WARNING: $missingFiles required files/folders are missing!" "Red"
        Write-Color "  Please run the copy utility to download missing files to $TargetDrive." "Red"
        Write-Host ""
        Write-Color "  Missing files need to be copied before using local installer option." "Yellow"
    }
    
    Write-Host ""
    Write-Color "NEXT STEPS:" "Yellow"
    if ($missingFiles -eq 0) {
        Write-Color "1. If files are on USB drive, copy them to D:\ on target computer" "White"
        Write-Color "2. Run migration script and select 'Yes' for local installer files" "White"
        Write-Color "3. Migration script will automatically use D:\ drive" "White"
    } else {
        Write-Color "1. Run copy utility to download missing files" "White"
        Write-Color "2. Verify again before proceeding with migration" "White"
    }
}

# Main execution
Clear-Host
Write-Color "********************************************************************************" "Magenta"
Write-Color "*                 INSTALLER FILES VERIFICATION UTILITY                       *" "Magenta"
Write-Color "********************************************************************************" "Magenta"
Write-Host ""

$availableDrives = Get-AvailableDrives
$selectedDrive = Select-Drive -Drives $availableDrives

Write-Host ""
Write-Color "Selected drive for verification: $($selectedDrive.DeviceID)" "Green"
Write-Host ""

Test-InstallerFiles -TargetDrive $selectedDrive.DeviceID

Write-Host ""
Read-Host "Press Enter to exit"
