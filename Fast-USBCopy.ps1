# FAST USB Copy Script
# Developed by: The greatest technician that ever lived
# Purpose: MAXIMUM SPEED USB file copying

param([switch]$WhatIf)

function Write-Status($Message, $Type = "Info") {
    $color = switch ($Type) {
        "Success" { "Green" }; "Warning" { "Yellow" }; "Error" { "Red" }; "Info" { "Cyan" }
        default { "White" }
    }
    Write-Host $Message -ForegroundColor $color
}

function Format-Size([long]$Bytes) {
    if ($Bytes -eq 0) { return "0 B" }
    $sizes = @("B", "KB", "MB", "GB")
    $index = 0; $size = [double]$Bytes
    while ($size -ge 1024 -and $index -lt 3) { $size /= 1024; $index++ }
    return "{0:N2} {1}" -f $size, $sizes[$index]
}

Write-Status "🚀 FAST USB COPY UTILITY 🚀" "Info"
if ($WhatIf) { Write-Status "WHAT-IF MODE ENABLED" "Warning" }
Write-Host ""

# STEP 1: RAPID USB DETECTION
Write-Status "⚡ Finding USB drives..." "Info"
$usbDrives = @(Get-WmiObject Win32_LogicalDisk | Where-Object { $_.DriveType -eq 2 -and $_.Size -gt 0 } | ForEach-Object {
    [PSCustomObject]@{
        Drive = $_.DeviceID
        Label = if ($_.VolumeName) { $_.VolumeName } else { "No Label" }
        SizeGB = [math]::Round($_.Size / 1GB, 2)
        FreeGB = [math]::Round($_.FreeSpace / 1GB, 2)
        Display = "$($_.DeviceID) [$($_.VolumeName)] ($([math]::Round($_.Size / 1GB, 2)) GB, $([math]::Round($_.FreeSpace / 1GB, 2)) GB free)"
    }
})

if ($usbDrives.Count -eq 0) {
    Write-Status "❌ NO USB DRIVES FOUND!" "Error"
    Get-WmiObject Win32_LogicalDisk | ForEach-Object {
        $type = @("Unknown", "No Root", "Removable", "Fixed", "Network", "CD-ROM", "RAM")[$_.DriveType]
        Write-Host "  $($_.DeviceID) - $type - $([math]::Round($_.Size / 1GB, 2)) GB" -ForegroundColor Gray
    }
    exit 1
}

Write-Status "✅ Found $($usbDrives.Count) USB drive(s)" "Success"
$usbDrives | ForEach-Object -Begin { $i = 0 } -Process { Write-Host "  [$i] $($_.Display)" -ForegroundColor Gray; $i++ }

# STEP 2: INSTANT SELECTION
do {
    $selection = Read-Host "`n🎯 Select USB drive (0-$($usbDrives.Count - 1))"
    $index = $null
} while (-not ([int]::TryParse($selection, [ref]$index) -and $index -ge 0 -and $index -lt $usbDrives.Count))

$selectedUSB = $usbDrives[$index]
Write-Status "🎯 Selected: $($selectedUSB.Display)" "Success"

# STEP 3: LIGHTNING SOURCE CHECK
Write-Status "`n⚡ Checking source..." "Info"
$sourcePath = "\\storagehd\Desktopshare\Scripts\Andrew\Needed\Usb drive files"

if (-not (Test-Path $sourcePath)) {
    Write-Status "❌ SOURCE NOT FOUND: $sourcePath" "Error"
    if (Test-Path "\\storagehd\Desktopshare\Scripts\Andrew\Needed") {
        Write-Status "💡 Run Copy-NetworkFiles.ps1 first!" "Warning"
    } else {
        Write-Status "💡 Check network connectivity!" "Warning"
    }
    exit 1
}

# RAPID SIZE CALCULATION
$totalSize = (Get-ChildItem $sourcePath -Recurse -File -ErrorAction SilentlyContinue | Measure-Object Length -Sum).Sum
$totalSizeGB = [math]::Round($totalSize / 1GB, 2)
$itemCount = (Get-ChildItem $sourcePath -ErrorAction SilentlyContinue).Count

Write-Status "✅ Source ready: $itemCount items, $(Format-Size $totalSize)" "Success"

# STEP 4: INSTANT SPACE CHECK
$requiredGB = $totalSizeGB * 1.05  # 5% buffer for speed
if ($selectedUSB.FreeGB -lt $requiredGB) {
    Write-Status "❌ INSUFFICIENT SPACE!" "Error"
    Write-Status "Need: $requiredGB GB | Available: $($selectedUSB.FreeGB) GB" "Error"
    exit 1
}
Write-Status "✅ Space OK: $($selectedUSB.FreeGB) GB available" "Success"

# STEP 5: FINAL CONFIRMATION
if (-not $WhatIf) {
    Write-Status "`n🚀 READY TO COPY $totalSizeGB GB TO $($selectedUSB.Drive)" "Warning"
    $confirm = Read-Host "Continue? (Y/N)"
    if ($confirm -notmatch '^[Yy]$') { Write-Status "Cancelled." "Info"; exit 0 }
}

# STEP 6: MAXIMUM SPEED COPY
Write-Status "`n🚀 COPYING AT MAXIMUM SPEED..." "Info"
$destination = $selectedUSB.Drive + "\"
$startTime = Get-Date

if ($WhatIf) {
    Write-Status "WHAT-IF: Would copy $sourcePath to $destination" "Info"
    Write-Status "WHAT-IF: Using maximum speed robocopy with 64 threads" "Info"
} else {
    # SINGLE ULTRA-FAST ROBOCOPY OPERATION
    $robocopyArgs = @(
        "`"$sourcePath`""
        "`"$destination`""
        "/E"           # Copy all subdirectories
        "/MT:64"       # 64 threads for MAXIMUM speed
        "/R:1"         # Only 1 retry for speed
        "/W:1"         # 1 second wait for speed
        "/J"           # Unbuffered I/O for large files
        "/COMPRESS"    # Network compression for speed
        "/NP"          # No progress (faster)
        "/NFL"         # No file logging (faster)
        "/NDL"         # No directory logging (faster)
        "/NJH"         # No job header (faster)
        "/NJS"         # No job summary (faster)
        "/NC"          # No class (faster)
        "/NS"          # No size (faster)
        "/NDD"         # No directory data (faster)
    )
    
    Write-Status "⚡ Executing ultra-fast robocopy..." "Info"
    $result = Start-Process -FilePath "robocopy" -ArgumentList $robocopyArgs -Wait -PassThru -NoNewWindow
    
    $endTime = Get-Date
    $duration = ($endTime - $startTime).TotalSeconds
    $speed = if ($duration -gt 0) { [math]::Round(($totalSize / 1MB) / $duration, 2) } else { 0 }
    
    if ($result.ExitCode -le 7) {
        Write-Status "🎉 COPY COMPLETE!" "Success"
        Write-Status "⚡ Time: $([math]::Round($duration, 1)) seconds" "Success"
        Write-Status "🚀 Speed: $speed MB/s" "Success"
        
        # Final space check
        $finalDrive = Get-WmiObject Win32_LogicalDisk | Where-Object { $_.DeviceID -eq $selectedUSB.Drive }
        $finalFreeGB = [math]::Round($finalDrive.FreeSpace / 1GB, 2)
        $usedGB = $selectedUSB.FreeGB - $finalFreeGB
        
        Write-Status "📊 Used: $(Format-Size ($usedGB * 1GB)) | Remaining: $finalFreeGB GB" "Info"
        Write-Status "✅ USB DRIVE READY FOR MIGRATION!" "Success"
    } else {
        Write-Status "❌ COPY FAILED (Exit code: $($result.ExitCode))" "Error"
        Write-Status "💡 Try running as Administrator or check USB drive" "Warning"
    }
}

Write-Host "`nPress Enter to exit..." -ForegroundColor Gray
Read-Host
