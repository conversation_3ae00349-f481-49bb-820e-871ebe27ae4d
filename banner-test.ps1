# Enhanced Banner Test Script
# Testing ASCII-only enhanced banners for PowerShell compatibility

function Show-EnhancedBanner {
    param(
        [string]$Title = "COMPUTER MIGRATION WIZARD v4.0",
        [string]$Color = "Cyan"
    )
    
    Clear-Host
    Write-Host ""
    
    # Top border
    Write-Host "  +================================================================+" -ForegroundColor $Color
    Write-Host "  |                                                                |" -ForegroundColor $Color
    
    # Title line - center the title
    $titlePadding = (64 - $Title.Length) / 2
    $leftPad = [math]::Floor($titlePadding)
    $rightPad = [math]::Ceiling($titlePadding)
    $titleLine = "  |" + (" " * $leftPad) + $Title + (" " * $rightPad) + "|"
    Write-Host $titleLine -ForegroundColor "Yellow"
    
    Write-Host "  |                                                                |" -ForegroundColor $Color
    Write-Host "  +================================================================+" -ForegroundColor $Color
    Write-Host "  |                                                                |" -ForegroundColor $Color
    Write-Host "  |    Welcome to the Computer Migration Wizard!                  |" -ForegroundColor "Green"
    Write-Host "  |                                                                |" -ForegroundColor $Color
    Write-Host "  |    Features:                                                   |" -ForegroundColor "White"
    Write-Host "  |    * Seamlessly transfer your computer setup with ease        |" -ForegroundColor "Yellow"
    Write-Host "  |    * Automated installation and configuration                 |" -ForegroundColor "Yellow"
    Write-Host "  |    * Smart profile and data migration                         |" -ForegroundColor "Yellow"
    Write-Host "  |    * Enhanced security and reliability                        |" -ForegroundColor "Yellow"
    Write-Host "  |    * Persistent credential management                         |" -ForegroundColor "Yellow"
    Write-Host "  |                                                                |" -ForegroundColor $Color
    Write-Host "  |    Developed by: The greatest technician that ever lived      |" -ForegroundColor "Magenta"
    Write-Host "  |                                                                |" -ForegroundColor $Color
    Write-Host "  +================================================================+" -ForegroundColor $Color
    Write-Host ""
}

function Show-SimpleBanner {
    param(
        [string]$Title = "COMPUTER MIGRATION WIZARD v4.0",
        [string]$Color = "Cyan"
    )
    
    Clear-Host
    Write-Host ""
    
    # Simple double-line border
    Write-Host "  ================================================================" -ForegroundColor $Color
    Write-Host "  ================================================================" -ForegroundColor $Color
    Write-Host "" -ForegroundColor $Color
    
    # Center the title
    $titlePadding = (64 - $Title.Length) / 2
    $leftPad = [math]::Floor($titlePadding)
    $titleSpaces = " " * $leftPad
    Write-Host "  $titleSpaces$Title" -ForegroundColor "Yellow"
    Write-Host ""
    
    Write-Host "  ================================================================" -ForegroundColor $Color
    Write-Host "" -ForegroundColor $Color
    Write-Host "    Welcome to the Computer Migration Wizard!" -ForegroundColor "Green"
    Write-Host "" -ForegroundColor $Color
    Write-Host "    Features:" -ForegroundColor "White"
    Write-Host "    - Seamlessly transfer your computer setup with ease" -ForegroundColor "Yellow"
    Write-Host "    - Automated installation and configuration" -ForegroundColor "Yellow"
    Write-Host "    - Smart profile and data migration" -ForegroundColor "Yellow"
    Write-Host "    - Enhanced security and reliability" -ForegroundColor "Yellow"
    Write-Host "    - Persistent credential management" -ForegroundColor "Yellow"
    Write-Host "" -ForegroundColor $Color
    Write-Host "    Developed by: The greatest technician that ever lived" -ForegroundColor "Magenta"
    Write-Host "" -ForegroundColor $Color
    Write-Host "  ================================================================" -ForegroundColor $Color
    Write-Host "  ================================================================" -ForegroundColor $Color
    Write-Host ""
}

function Show-StarBanner {
    param(
        [string]$Title = "COMPUTER MIGRATION WIZARD v4.0",
        [string]$Color = "Cyan"
    )
    
    Clear-Host
    Write-Host ""
    
    # Star border
    Write-Host "  ****************************************************************" -ForegroundColor $Color
    Write-Host "  *                                                              *" -ForegroundColor $Color
    
    # Title line - center the title
    $titlePadding = (62 - $Title.Length) / 2
    $leftPad = [math]::Floor($titlePadding)
    $rightPad = [math]::Ceiling($titlePadding)
    $titleLine = "  *" + (" " * $leftPad) + $Title + (" " * $rightPad) + "*"
    Write-Host $titleLine -ForegroundColor "Yellow"
    
    Write-Host "  *                                                              *" -ForegroundColor $Color
    Write-Host "  ****************************************************************" -ForegroundColor $Color
    Write-Host "  *                                                              *" -ForegroundColor $Color
    Write-Host "  *    Welcome to the Computer Migration Wizard!                *" -ForegroundColor "Green"
    Write-Host "  *                                                              *" -ForegroundColor $Color
    Write-Host "  *    Features:                                                 *" -ForegroundColor "White"
    Write-Host "  *    - Seamlessly transfer your computer setup with ease      *" -ForegroundColor "Yellow"
    Write-Host "  *    - Automated installation and configuration               *" -ForegroundColor "Yellow"
    Write-Host "  *    - Smart profile and data migration                       *" -ForegroundColor "Yellow"
    Write-Host "  *    - Enhanced security and reliability                      *" -ForegroundColor "Yellow"
    Write-Host "  *    - Persistent credential management                       *" -ForegroundColor "Yellow"
    Write-Host "  *                                                              *" -ForegroundColor $Color
    Write-Host "  *    Developed by: The greatest technician that ever lived    *" -ForegroundColor "Magenta"
    Write-Host "  *                                                              *" -ForegroundColor $Color
    Write-Host "  ****************************************************************" -ForegroundColor $Color
    Write-Host ""
}

# Test all three banner styles
Write-Host "Testing Banner Style 1: Box Style with + and |" -ForegroundColor "White"
Write-Host "Press any key to continue..." -ForegroundColor "Gray"
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Show-EnhancedBanner

Write-Host "Press any key for next banner..." -ForegroundColor "Gray"
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Write-Host "Testing Banner Style 2: Simple Double Line" -ForegroundColor "White"
Write-Host "Press any key to continue..." -ForegroundColor "Gray"
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Show-SimpleBanner

Write-Host "Press any key for next banner..." -ForegroundColor "Gray"
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Write-Host "Testing Banner Style 3: Star Border" -ForegroundColor "White"
Write-Host "Press any key to continue..." -ForegroundColor "Gray"
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Show-StarBanner

Write-Host "Banner testing complete!" -ForegroundColor "Green"
Write-Host "All banners use only ASCII characters and should be PowerShell-safe." -ForegroundColor "Yellow"
Write-Host ""
Write-Host "Which banner style do you prefer?" -ForegroundColor "Cyan"
Write-Host "1. Box Style (+ and | characters)" -ForegroundColor "White"
Write-Host "2. Simple Double Line (= characters)" -ForegroundColor "White"
Write-Host "3. Star Border (* characters)" -ForegroundColor "White"
