# Script to fix quote issues in the main script
$scriptPath = "net version 2.ps1"
$content = Get-Content $scriptPath -Raw

# Replace smart quotes with regular quotes
$content = $content -replace '"', '"'  # Left double quote
$content = $content -replace '"', '"'  # Right double quote
$content = $content -replace ''', "'"  # Left single quote
$content = $content -replace ''', "'"  # Right single quote

# Save the fixed content
Set-Content -Path $scriptPath -Value $content -Encoding UTF8

Write-Host "Fixed quote characters in the script" -ForegroundColor Green
